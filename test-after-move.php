<?php
echo "<h1>Test Application After File Move</h1>";

$rootPath = dirname(__DIR__);

echo "<h2>File Structure Check:</h2>";

// Check critical Laravel files
$criticalFiles = [
    'vendor/autoload.php' => 'Laravel framework',
    'bootstrap/app.php' => 'Laravel bootstrap',
    'routes/web.php' => 'Application routes',
    'app/Http/Controllers/HomeController.php' => 'Home controller',
    '.env' => 'Environment configuration'
];

$allFilesExist = true;
foreach ($criticalFiles as $file => $description) {
    $filePath = $rootPath . '/' . $file;
    if (file_exists($filePath)) {
        echo "✅ {$file} - {$description}<br>";
    } else {
        echo "❌ {$file} - {$description} MISSING<br>";
        $allFilesExist = false;
    }
}

if ($allFilesExist) {
    echo "<br><h2>Laravel Bootstrap Test:</h2>";
    
    try {
        // Test Laravel loading
        require $rootPath . '/vendor/autoload.php';
        echo "✅ Autoloader loaded<br>";
        
        $app = require $rootPath . '/bootstrap/app.php';
        echo "✅ Laravel application loaded<br>";
        
        // Test configuration
        $config = $app->make('config');
        echo "✅ Configuration loaded<br>";
        echo "App Name: " . $config->get('app.name') . "<br>";
        
        // Test database
        try {
            $exhibitions = \App\Models\Exhibition::count();
            echo "✅ Database working: {$exhibitions} exhibitions<br>";
        } catch (Exception $e) {
            echo "❌ Database error: " . $e->getMessage() . "<br>";
        }
        
        // Test routes
        $routes = \Illuminate\Support\Facades\Route::getRoutes();
        echo "✅ Routes loaded: " . count($routes) . " routes<br>";
        
        // Check for home route
        $homeRoute = null;
        foreach ($routes as $route) {
            if ($route->uri() === '/') {
                $homeRoute = $route;
                break;
            }
        }
        
        if ($homeRoute) {
            echo "✅ Home route found: " . $homeRoute->getActionName() . "<br>";
        } else {
            echo "❌ Home route not found<br>";
        }
        
        echo "<br><div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px;'>";
        echo "<h3 style='color: #155724;'>🎉 EXCELLENT!</h3>";
        echo "<p>Laravel is working correctly!</p>";
        echo "<p><strong><a href='/' style='font-size: 18px;'>🚀 TEST SEASON EXPO APPLICATION</a></strong></p>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<br><div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px;'>";
        echo "<h3 style='color: #721c24;'>❌ Laravel Error</h3>";
        echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
        echo "<p>File: " . $e->getFile() . "</p>";
        echo "<p>Line: " . $e->getLine() . "</p>";
        echo "</div>";
    }
    
} else {
    echo "<br><div style='background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; border-radius: 5px;'>";
    echo "<h3 style='color: #856404;'>⚠️ Files Still Missing</h3>";
    echo "<p>Some critical files are still missing. You may need to:</p>";
    echo "<ol>";
    echo "<li>Extract season_expo.zip to the root directory</li>";
    echo "<li>Or manually copy missing files</li>";
    echo "</ol>";
    echo "</div>";
}

echo "<h2>Next Steps:</h2>";
echo "<ol>";
echo "<li>If Laravel is working, test your main application</li>";
echo "<li>Clear all caches if needed</li>";
echo "<li>Delete all test files for security</li>";
echo "</ol>";
?>

<script setup>
import { Link } from '@inertiajs/vue3';

const props = defineProps({
  currentLocale: {
    type: String,
    default: 'ar'
  }
});

const languages = [
  { code: 'ar', name: 'العربية', flag: '🇸🇦' },
  { code: 'en', name: 'English', flag: '🇺🇸' }
];

const switchLanguage = (locale) => {
  window.location.href = `/language/${locale}`;
};
</script>

<template>
  <div class="relative inline-block text-left">
    <div class="group">
      <button
        type="button"
        class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
      >
        <span class="mr-2">{{ currentLocale === 'ar' ? '🇸🇦' : '🇺🇸' }}</span>
        {{ currentLocale === 'ar' ? 'العربية' : 'English' }}
        <svg class="-mr-1 ml-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
        </svg>
      </button>

      <div class="absolute right-0 z-10 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
        <div class="py-1" role="menu">
          <button
            v-for="language in languages"
            :key="language.code"
            @click="switchLanguage(language.code)"
            class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900"
            :class="{ 'bg-gray-100 text-gray-900': currentLocale === language.code }"
            role="menuitem"
          >
            <span class="mr-3">{{ language.flag }}</span>
            {{ language.name }}
            <svg v-if="currentLocale === language.code" class="ml-auto h-4 w-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

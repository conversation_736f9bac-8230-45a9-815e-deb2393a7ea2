<?php
// Set headers to prevent caching
header("Cache-Control: no-cache, no-store, must-revalidate");
header("Pragma: no-cache");
header("Expires: 0");

echo "<h1>Cache Headers Test</h1>";
echo "<p>This page should never be cached.</p>";
echo "<p>Current time: " . date('Y-m-d H:i:s') . "</p>";

// Test Laravel with no-cache headers
echo "<h2>Lara<PERSON> Test (No Cache):</h2>";

try {
    require '../vendor/autoload.php';
    $app = require_once '../bootstrap/app.php';
    
    // Get some data
    $exhibitions = \App\Models\Exhibition::take(3)->get();
    
    echo "✅ Laravel working<br>";
    echo "✅ Found " . count($exhibitions) . " exhibitions:<br>";
    
    foreach ($exhibitions as $exhibition) {
        echo "- " . $exhibition->title . "<br>";
    }
    
    echo "<br><strong>If you see this data, <PERSON><PERSON> is working correctly.</strong><br>";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
}

echo "<br><h2>Browser Instructions:</h2>";
echo "<strong>Microsoft Edge:</strong><br>";
echo "1. Press F12 to open Developer Tools<br>";
echo "2. Go to Network tab<br>";
echo "3. Check 'Disable cache'<br>";
echo "4. Refresh the page<br>";
echo "5. Look for any failed requests (red entries)<br>";

echo "<br><strong>Google Chrome:</strong><br>";
echo "1. Press Ctrl+Shift+R for hard refresh<br>";
echo "2. Or press F12 → Network → check 'Disable cache'<br>";

echo "<br><a href='/'>Test Main Application</a>";
?>

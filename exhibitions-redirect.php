<?php
// Redirect fix for exhibitions routes
// Add this to .htaccess or use as a temporary redirect

// Check if the request is for exhibitions/1/simple
$requestUri = $_SERVER['REQUEST_URI'];

if (strpos($requestUri, '/exhibitions/1/simple') !== false) {
    header('Location: /exhibitions-1-simple.php');
    exit;
}

// For other exhibition IDs, redirect to exhibitions list
if (preg_match('/\/exhibitions\/(\d+)\/simple/', $requestUri, $matches)) {
    $exhibitionId = $matches[1];
    
    // If it's exhibition 1, redirect to our fixed file
    if ($exhibitionId == 1) {
        header('Location: /exhibitions-1-simple.php');
        exit;
    }
    
    // For other IDs, redirect to exhibitions list for now
    header('Location: /exhibitions-simple');
    exit;
}

// Default: show error
echo "Redirect handler for exhibitions";
?>

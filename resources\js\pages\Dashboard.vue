<script setup>
import { <PERSON>, <PERSON> } from '@inertiajs/vue3';

const props = defineProps({
    locale: {
        type: String,
        default: 'ar'
    },
    translations: {
        type: Object,
        default: () => ({ app: {} })
    }
});

const t = (key) => {
    return props.translations?.app?.[key] || key;
};

const isRTL = props.locale === 'ar';
</script>

<template>
    <Head :title="t('dashboard_title') || 'Dashboard - Season Expo'" />

    <div class="min-h-screen bg-gray-50" :dir="isRTL ? 'rtl' : 'ltr'">
        <!-- Fixed Navigation -->
        <nav class="fixed top-0 left-0 right-0 bg-white shadow-sm z-50">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <div class="flex items-center">
                        <Link href="/" class="text-2xl font-bold text-blue-600 hover:text-blue-700 transition-colors">
                            {{ t('site_name') || 'Season Expo' }}
                        </Link>
                    </div>
                    <div class="flex items-center" :class="isRTL ? 'space-x-reverse space-x-4' : 'space-x-4'">
                        <Link href="/exhibitions" class="nav-link text-gray-600">{{ t('exhibitions') || 'Exhibitions' }}</Link>
                        <Link href="/" class="nav-link text-gray-600">{{ t('view_homepage') || 'Home' }}</Link>
                        <Link
                            href="/logout"
                            method="post"
                            as="button"
                            class="nav-link text-gray-600 bg-transparent border-none cursor-pointer"
                        >
                            {{ t('logout') || 'Logout' }}
                        </Link>
                    </div>
                </div>
            </div>
        </nav>

        <div class="py-12 pt-24">
            <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">


                <!-- Welcome Section -->
                <div class="overflow-hidden bg-white shadow-sm sm:rounded-lg mb-8">
                    <div class="p-6 text-gray-900">
                        <h1 class="text-2xl font-bold mb-4">{{ t('welcome_to_season_expo') || 'Welcome to Season Expo' }}</h1>
                        <p class="text-gray-600 mb-4">
                            {{ t('dashboard_subtitle') || 'Your gateway to amazing exhibitions and booth reservations' }}
                        </p>
                        <div class="flex" :class="isRTL ? 'space-x-reverse space-x-4' : 'space-x-4'">
                            <Link
                                href="/exhibitions"
                                class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                            >
                                {{ t('browse_exhibitions') || 'Browse Exhibitions' }}
                            </Link>
                            <Link
                                href="/"
                                class="bg-gray-100 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-200 transition-colors"
                            >
                                {{ t('view_homepage') || 'View Homepage' }}
                            </Link>
                        </div>
                    </div>
                </div>

                <!-- Quick Stats -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <svg class="h-8 w-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                </div>
                                <div :class="isRTL ? 'mr-5 w-0 flex-1' : 'ml-5 w-0 flex-1'">
                                    <dl>
                                        <dt class="text-sm font-medium text-gray-500 truncate">
                                            {{ t('active_exhibitions') || 'Active Exhibitions' }}
                                        </dt>
                                        <dd class="text-lg font-medium text-gray-900">
                                            3
                                        </dd>
                                    </dl>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <svg class="h-8 w-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                    </svg>
                                </div>
                                <div :class="isRTL ? 'mr-5 w-0 flex-1' : 'ml-5 w-0 flex-1'">
                                    <dl>
                                        <dt class="text-sm font-medium text-gray-500 truncate">
                                            {{ t('available_booths') || 'Available Booths' }}
                                        </dt>
                                        <dd class="text-lg font-medium text-gray-900">
                                            150
                                        </dd>
                                    </dl>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <svg class="h-8 w-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                                    </svg>
                                </div>
                                <div :class="isRTL ? 'mr-5 w-0 flex-1' : 'ml-5 w-0 flex-1'">
                                    <dl>
                                        <dt class="text-sm font-medium text-gray-500 truncate">
                                            {{ t('my_bookings') || 'My Bookings' }}
                                        </dt>
                                        <dd class="text-lg font-medium text-gray-900">
                                            0
                                        </dd>
                                    </dl>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h2 class="text-lg font-medium text-gray-900 mb-4">{{ t('quick_actions') || 'Quick Actions' }}</h2>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                            <Link
                                href="/exhibitions"
                                class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors"
                            >
                                <svg :class="isRTL ? 'h-6 w-6 text-blue-600 ml-3' : 'h-6 w-6 text-blue-600 mr-3'" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                                <div>
                                    <div class="font-medium text-gray-900">{{ t('browse_exhibitions') || 'Browse Exhibitions' }}</div>
                                    <div class="text-sm text-gray-500">{{ t('find_next_event') || 'Find your next event' }}</div>
                                </div>
                            </Link>

                            <Link
                                href="/dashboard/booth-search"
                                class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-green-300 hover:bg-green-50 transition-colors"
                            >
                                <svg :class="isRTL ? 'h-6 w-6 text-green-600 ml-3' : 'h-6 w-6 text-green-600 mr-3'" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                                <div>
                                    <div class="font-medium text-gray-900">{{ t('search_booths') || 'Search Booths' }}</div>
                                    <div class="text-sm text-gray-500">{{ t('find_perfect_space') || 'Find your perfect space' }}</div>
                                </div>
                            </Link>

                            <Link
                                href="/dashboard/reservations"
                                class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-purple-300 hover:bg-purple-50 transition-colors"
                            >
                                <svg :class="isRTL ? 'h-6 w-6 text-purple-600 ml-3' : 'h-6 w-6 text-purple-600 mr-3'" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                                </svg>
                                <div>
                                    <div class="font-medium text-gray-900">{{ t('my_bookings') || 'My Bookings' }}</div>
                                    <div class="text-sm text-gray-500">{{ t('manage_reservations') || 'Manage your reservations' }}</div>
                                </div>
                            </Link>

                            <a
                                href="#"
                                class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-indigo-300 hover:bg-indigo-50 transition-colors"
                            >
                                <svg :class="isRTL ? 'h-6 w-6 text-indigo-600 ml-3' : 'h-6 w-6 text-indigo-600 mr-3'" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                                <div>
                                    <div class="font-medium text-gray-900">{{ t('media_management') || 'Media Management' }}</div>
                                    <div class="text-sm text-gray-500">{{ t('manage_all_images') || 'Manage all images' }}</div>
                                </div>
                            </a>

                            <Link
                                href="/signatures"
                                class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-emerald-300 hover:bg-emerald-50 transition-colors"
                            >
                                <svg :class="isRTL ? 'h-6 w-6 text-emerald-600 ml-3' : 'h-6 w-6 text-emerald-600 mr-3'" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                <div>
                                    <div class="font-medium text-gray-900">{{ t('digital_signatures') || 'التوقيعات الرقمية' }}</div>
                                    <div class="text-sm text-gray-500">{{ t('manage_digital_signatures') || 'إنشاء وإدارة التوقيعات والمستندات الرقمية' }}</div>
                                </div>
                            </Link>

                            <a
                                href="#"
                                class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-orange-300 hover:bg-orange-50 transition-colors"
                            >
                                <svg :class="isRTL ? 'h-6 w-6 text-orange-600 ml-3' : 'h-6 w-6 text-orange-600 mr-3'" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z"></path>
                                </svg>
                                <div>
                                    <div class="font-medium text-gray-900">{{ t('get_support') || 'Get Support' }}</div>
                                    <div class="text-sm text-gray-500">{{ t('need_help') || 'Need help?' }}</div>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <footer class="bg-white border-t border-gray-200 py-8">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center text-gray-500">
                    <p>{{ t('copyright') || '© 2024 Season Expo. All rights reserved.' }}</p>
                </div>
            </div>
        </footer>
    </div>
</template>

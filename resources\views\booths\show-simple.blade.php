<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{{ $booth->booth_number }} - {{ $exhibition->title }}</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
    </style>
</head>
<body class="min-h-screen bg-gray-50">
    <!-- Fixed Navigation -->
    <nav class="fixed top-0 left-0 right-0 bg-white shadow-sm z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="/" class="text-2xl font-bold text-blue-600 hover:text-blue-700 transition-colors">
                        Season Expo
                    </a>
                </div>
                <div class="flex items-center space-x-reverse space-x-4">
                    <a href="/dashboard" class="text-gray-600 hover:text-gray-900">لوحة التحكم</a>
                    <a href="/exhibitions" class="text-gray-600 hover:text-gray-900">المعارض</a>
                    <a href="/" class="text-gray-600 hover:text-gray-900">الصفحة الرئيسية</a>
                </div>
            </div>
        </div>
    </nav>

    <div class="pt-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <!-- Breadcrumb -->
            <nav class="flex mb-8" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-3 space-x-reverse">
                    <li class="inline-flex items-center">
                        <a href="/" class="text-gray-700 hover:text-blue-600">الرئيسية</a>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <span class="mx-2 text-gray-400">/</span>
                            <a href="/exhibitions" class="text-gray-700 hover:text-blue-600">المعارض</a>
                        </div>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <span class="mx-2 text-gray-400">/</span>
                            <a href="/exhibitions/{{ $exhibition->slug }}" class="text-gray-700 hover:text-blue-600">{{ $exhibition->title }}</a>
                        </div>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <span class="mx-2 text-gray-400">/</span>
                            <a href="/exhibitions/{{ $exhibition->slug }}/booths" class="text-gray-700 hover:text-blue-600">الأجنحة</a>
                        </div>
                    </li>
                    <li aria-current="page">
                        <div class="flex items-center">
                            <span class="mx-2 text-gray-400">/</span>
                            <span class="text-gray-500">{{ $booth->booth_number }}</span>
                        </div>
                    </li>
                </ol>
            </nav>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Main Content -->
                <div class="lg:col-span-2">
                    <!-- Booth Header -->
                    <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                        <div class="flex justify-between items-start mb-4">
                            <div>
                                <h1 class="text-3xl font-bold text-gray-900">{{ $booth->booth_number }}</h1>
                                @if($booth->name)
                                    <p class="text-lg text-gray-600 mt-1">{{ $booth->name }}</p>
                                @endif
                            </div>
                            <span class="px-3 py-1 text-sm font-medium rounded-full 
                                {{ $booth->status === 'available' ? 'bg-green-100 text-green-800' : '' }}
                                {{ $booth->status === 'reserved' ? 'bg-yellow-100 text-yellow-800' : '' }}
                                {{ $booth->status === 'booked' ? 'bg-red-100 text-red-800' : '' }}">
                                {{ $booth->status === 'available' ? 'متاح' : '' }}
                                {{ $booth->status === 'reserved' ? 'محجوز مؤقتاً' : '' }}
                                {{ $booth->status === 'booked' ? 'مؤكد الحجز' : '' }}
                            </span>
                        </div>

                        @if($booth->description)
                            <p class="text-gray-700 mb-4">{{ $booth->description }}</p>
                        @endif

                        <!-- Booth Details Grid -->
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                            <div class="text-center p-4 bg-gray-50 rounded-lg">
                                <div class="text-2xl font-bold text-blue-600">{{ $booth->area }}</div>
                                <div class="text-sm text-gray-600">متر مربع</div>
                            </div>
                            <div class="text-center p-4 bg-gray-50 rounded-lg">
                                <div class="text-2xl font-bold text-green-600">
                                    {{ $booth->size === 'small' ? 'صغير' : '' }}
                                    {{ $booth->size === 'medium' ? 'متوسط' : '' }}
                                    {{ $booth->size === 'large' ? 'كبير' : '' }}
                                </div>
                                <div class="text-sm text-gray-600">الحجم</div>
                            </div>
                            <div class="text-center p-4 bg-gray-50 rounded-lg">
                                <div class="text-2xl font-bold text-purple-600">{{ $booth->width }}×{{ $booth->height }}</div>
                                <div class="text-sm text-gray-600">الأبعاد (متر)</div>
                            </div>
                            <div class="text-center p-4 bg-gray-50 rounded-lg">
                                <div class="text-2xl font-bold text-orange-600">{{ number_format($booth->price, 3) }}</div>
                                <div class="text-sm text-gray-600">د.ك</div>
                            </div>
                        </div>

                        <!-- Location -->
                        @if($booth->location)
                            <div class="mb-6">
                                <h3 class="text-lg font-semibold text-gray-900 mb-2">📍 الموقع</h3>
                                <p class="text-gray-700">{{ $booth->location }}</p>
                            </div>
                        @endif

                        <!-- Features -->
                        @if($booth->features && is_array($booth->features) && count($booth->features) > 0)
                            <div class="mb-6">
                                <h3 class="text-lg font-semibold text-gray-900 mb-3">✨ المميزات المتاحة</h3>
                                <div class="grid grid-cols-2 md:grid-cols-3 gap-3">
                                    @foreach($booth->features as $feature)
                                        <div class="flex items-center p-3 bg-blue-50 rounded-lg">
                                            <span class="text-blue-600 ml-2">
                                                @if($feature === 'electricity') ⚡ @endif
                                                @if($feature === 'wifi') 📶 @endif
                                                @if($feature === 'storage') 📦 @endif
                                                @if($feature === 'furniture') 🪑 @endif
                                            </span>
                                            <span class="text-gray-700">
                                                {{ $feature === 'electricity' ? 'كهرباء' : $feature }}
                                                {{ $feature === 'wifi' ? 'واي فاي' : '' }}
                                                {{ $feature === 'storage' ? 'تخزين' : '' }}
                                                {{ $feature === 'furniture' ? 'أثاث' : '' }}
                                            </span>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        @endif
                    </div>

                    <!-- Exhibition Info -->
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">🏢 معلومات المعرض</h3>
                        <div class="space-y-3">
                            <div class="flex justify-between">
                                <span class="text-gray-600">اسم المعرض:</span>
                                <span class="font-medium">{{ $exhibition->title }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">التاريخ:</span>
                                <span class="font-medium">{{ $exhibition->start_date->format('d/m/Y') }} - {{ $exhibition->end_date->format('d/m/Y') }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">المكان:</span>
                                <span class="font-medium">{{ $exhibition->venue_name }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">المدينة:</span>
                                <span class="font-medium">{{ $exhibition->city }}, {{ $exhibition->country }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="lg:col-span-1">
                    <!-- Booking Card -->
                    <div class="bg-white rounded-lg shadow-md p-6 mb-6 sticky top-24">
                        <div class="text-center mb-6">
                            <div class="text-3xl font-bold text-blue-600 mb-2">{{ number_format($booth->price, 3) }} د.ك</div>
                            <p class="text-gray-600">سعر الجناح</p>
                        </div>

                        @if($booth->status === 'available')
                            <form method="POST" action="/exhibitions/{{ $exhibition->slug }}/booths/{{ $booth->id }}/book" class="space-y-4">
                                @csrf
                                <button type="submit" class="w-full bg-green-600 text-white py-3 px-4 rounded-lg font-semibold hover:bg-green-700 transition-colors">
                                    🎯 احجز هذا الجناح
                                </button>
                            </form>
                            <p class="text-sm text-gray-500 text-center mt-3">
                                ✅ متاح للحجز الفوري
                            </p>
                        @elseif($booth->status === 'reserved')
                            <div class="text-center">
                                <button disabled class="w-full bg-yellow-400 text-yellow-800 py-3 px-4 rounded-lg font-semibold cursor-not-allowed">
                                    ⏳ محجوز مؤقتاً
                                </button>
                                <p class="text-sm text-gray-500 mt-3">
                                    هذا الجناح محجوز مؤقتاً من قبل عميل آخر
                                </p>
                            </div>
                        @else
                            <div class="text-center">
                                <button disabled class="w-full bg-red-400 text-red-800 py-3 px-4 rounded-lg font-semibold cursor-not-allowed">
                                    🔒 غير متاح
                                </button>
                                <p class="text-sm text-gray-500 mt-3">
                                    هذا الجناح غير متاح للحجز حالياً
                                </p>
                            </div>
                        @endif

                        <div class="mt-6 pt-6 border-t border-gray-200">
                            <h4 class="font-semibold text-gray-900 mb-3">📞 تحتاج مساعدة؟</h4>
                            <div class="space-y-2 text-sm text-gray-600">
                                <p>📧 <EMAIL></p>
                                <p>📱 +965 1234 5678</p>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <h4 class="font-semibold text-gray-900 mb-4">🔗 روابط سريعة</h4>
                        <div class="space-y-3">
                            <a href="/exhibitions/{{ $exhibition->slug }}/booths" class="block w-full text-center bg-gray-100 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-200 transition-colors">
                                ← العودة للأجنحة
                            </a>
                            <a href="/exhibitions/{{ $exhibition->slug }}" class="block w-full text-center bg-blue-100 text-blue-700 py-2 px-4 rounded-lg hover:bg-blue-200 transition-colors">
                                📋 تفاصيل المعرض
                            </a>
                            <a href="/dashboard" class="block w-full text-center bg-purple-100 text-purple-700 py-2 px-4 rounded-lg hover:bg-purple-200 transition-colors">
                                🏠 لوحة التحكم
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-white border-t border-gray-200 py-8 mt-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center text-gray-500">
                <p>© 2024 Season Expo. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>
</body>
</html>

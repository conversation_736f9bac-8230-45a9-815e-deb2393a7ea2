<?php
// Test Laravel Routes
// Check if Laravel routes are working

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='utf-8'>";
echo "<title>اختبار Laravel Routes</title>";
echo "<script src='https://cdn.tailwindcss.com'></script>";
echo "</head>";
echo "<body class='bg-gray-50 p-8'>";

echo "<div class='max-w-4xl mx-auto'>";
echo "<h1 class='text-2xl font-bold mb-6'>🔍 اختبار Laravel Routes</h1>";

// Test different routes
echo "<div class='bg-white rounded-lg shadow p-6 mb-6'>";
echo "<h2 class='text-lg font-bold mb-4'>🧪 اختبار الروابط</h2>";

$testRoutes = [
    'Laravel Index' => '/',
    'Dashboard' => '/dashboard',
    'Bookings Index' => '/bookings',
    'Bookings Create' => '/bookings/create',
    'Bookings Create with params' => '/bookings/create?booth_id=23&exhibition_id=1',
    'Payment Initiate' => '/payment/initiate/1',
    'Admin' => '/admin',
    'API Test' => '/api/test'
];

echo "<div class='space-y-3'>";

foreach ($testRoutes as $name => $route) {
    echo "<div class='flex items-center justify-between p-3 border border-gray-200 rounded'>";
    echo "<div>";
    echo "<h3 class='font-semibold'>{$name}</h3>";
    echo "<code class='text-sm text-gray-600'>{$route}</code>";
    echo "</div>";
    echo "<a href='{$route}' target='_blank' class='bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 text-sm'>اختبار</a>";
    echo "</div>";
}

echo "</div>";
echo "</div>";

// Check if Laravel is working
echo "<div class='bg-white rounded-lg shadow p-6 mb-6'>";
echo "<h2 class='text-lg font-bold mb-4'>🏗️ فحص Laravel</h2>";

// Check if Laravel files exist
$laravelFiles = [
    'index.php' => 'Laravel Entry Point',
    'artisan' => 'Laravel Artisan',
    'routes/web.php' => 'Web Routes',
    'app/Http/Controllers/BookingController.php' => 'Booking Controller',
    'app/Http/Controllers/PaymentController.php' => 'Payment Controller'
];

echo "<div class='space-y-2'>";

foreach ($laravelFiles as $file => $description) {
    $exists = file_exists($file) ? '✅' : '❌';
    $color = file_exists($file) ? 'text-green-600' : 'text-red-600';
    
    echo "<div class='flex items-center justify-between p-2 border border-gray-200 rounded'>";
    echo "<div>";
    echo "<span class='font-medium'>{$description}</span>";
    echo "<br><code class='text-xs text-gray-500'>{$file}</code>";
    echo "</div>";
    echo "<span class='{$color} text-lg'>{$exists}</span>";
    echo "</div>";
}

echo "</div>";
echo "</div>";

// Alternative solution
echo "<div class='bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-6'>";
echo "<h2 class='text-lg font-bold text-yellow-700 mb-4'>💡 حل بديل مؤقت</h2>";

echo "<p class='text-yellow-700 mb-4'>إذا كان Laravel لا يعمل، يمكننا إنشاء صفحة حجز بسيطة مؤقتة:</p>";

echo "<div class='bg-white p-4 rounded border'>";
echo "<h3 class='font-semibold mb-2'>الخيارات:</h3>";
echo "<ol class='text-sm space-y-1 list-decimal list-inside'>";
echo "<li><strong>إصلاح Laravel:</strong> التأكد من عمل BookingController</li>";
echo "<li><strong>حل مؤقت:</strong> إنشاء صفحة حجز PHP بسيطة</li>";
echo "<li><strong>تحويل مباشر:</strong> توجيه مباشر لنظام الدفع</li>";
echo "</ol>";
echo "</div>";

echo "</div>";

// Instructions
echo "<div class='bg-blue-50 border border-blue-200 rounded-lg p-6'>";
echo "<h2 class='text-lg font-bold text-blue-700 mb-4'>📋 التعليمات</h2>";

echo "<div class='space-y-4'>";

echo "<div class='bg-white p-4 rounded border'>";
echo "<h3 class='font-semibold mb-2'>1. اختبر الروابط أعلاه:</h3>";
echo "<ul class='text-sm space-y-1 list-disc list-inside'>";
echo "<li>اضغط على كل رابط واختبره</li>";
echo "<li>لاحظ أي أخطاء أو رسائل</li>";
echo "<li>أخبرني بالنتائج</li>";
echo "</ul>";
echo "</div>";

echo "<div class='bg-white p-4 rounded border'>";
echo "<h3 class='font-semibold mb-2'>2. بعد تحديث .htaccess:</h3>";
echo "<ul class='text-sm space-y-1 list-disc list-inside'>";
echo "<li>اختبر رابط الحجز مرة أخرى</li>";
echo "<li>تحقق من أن التوجيه يعمل بشكل صحيح</li>";
echo "</ul>";
echo "</div>";

echo "<div class='bg-white p-4 rounded border'>";
echo "<h3 class='font-semibold mb-2'>3. إذا استمرت المشكلة:</h3>";
echo "<ul class='text-sm space-y-1 list-disc list-inside'>";
echo "<li>سأنشئ صفحة حجز بديلة تعمل فوراً</li>";
echo "<li>مع تكامل مباشر مع ماي فاتورة</li>";
echo "</ul>";
echo "</div>";

echo "</div>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>

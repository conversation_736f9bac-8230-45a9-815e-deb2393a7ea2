<?php
// Minimal test to isolate the issue
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h1>Minimal Laravel Test</h1>";
echo "<p>Testing step by step to find the exact issue...</p>";

$rootPath = dirname(__DIR__);

try {
    echo "<h2>Step 1: Basic PHP</h2>";
    echo "✅ PHP working<br>";
    echo "PHP Version: " . phpversion() . "<br>";
    echo "Memory Limit: " . ini_get('memory_limit') . "<br>";
    echo "Max Execution Time: " . ini_get('max_execution_time') . "<br>";
    
    echo "<h2>Step 2: File System</h2>";
    echo "Root path: {$rootPath}<br>";
    echo "Current directory: " . getcwd() . "<br>";
    echo "Script path: " . __FILE__ . "<br>";
    
    echo "<h2>Step 3: Critical Files</h2>";
    $criticalFiles = [
        'vendor/autoload.php',
        'bootstrap/app.php',
        '.env'
    ];
    
    foreach ($criticalFiles as $file) {
        $path = $rootPath . '/' . $file;
        if (file_exists($path)) {
            echo "✅ {$file} exists (" . filesize($path) . " bytes)<br>";
        } else {
            echo "❌ {$file} MISSING<br>";
            throw new Exception("Critical file missing: {$file}");
        }
    }
    
    echo "<h2>Step 4: Autoloader Test</h2>";
    require_once $rootPath . '/vendor/autoload.php';
    echo "✅ Autoloader loaded<br>";
    
    echo "<h2>Step 5: Laravel App Test</h2>";
    $app = require_once $rootPath . '/bootstrap/app.php';
    echo "✅ Laravel app loaded<br>";
    echo "App class: " . get_class($app) . "<br>";
    
    echo "<h2>Step 6: Environment Test</h2>";
    // Test .env loading
    $envPath = $rootPath . '/.env';
    $envContent = file_get_contents($envPath);
    
    if (strpos($envContent, 'APP_KEY=') !== false) {
        echo "✅ APP_KEY found in .env<br>";
    } else {
        echo "❌ APP_KEY missing in .env<br>";
    }
    
    if (strpos($envContent, 'DB_DATABASE=') !== false) {
        echo "✅ Database config found in .env<br>";
    } else {
        echo "❌ Database config missing in .env<br>";
    }
    
    echo "<h2>Step 7: HTTP Kernel Test</h2>";
    $kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);
    echo "✅ HTTP Kernel created<br>";
    
    echo "<h2>Step 8: Simple Request Test</h2>";
    // Create a very simple request
    $request = Illuminate\Http\Request::create('/', 'GET');
    echo "✅ Request created<br>";
    
    // This is where it might fail
    echo "Attempting to handle request...<br>";
    $response = $kernel->handle($request);
    echo "✅ Request handled successfully!<br>";
    echo "Status: " . $response->getStatusCode() . "<br>";
    
    $kernel->terminate($request, $response);
    echo "✅ Request terminated<br>";
    
    echo "<br><div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px;'>";
    echo "<h3>🎉 ALL TESTS PASSED!</h3>";
    echo "<p>Laravel is working correctly. The issue might be with your specific index.php file.</p>";
    echo "</div>";
    
} catch (Error $e) {
    echo "<br><div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px;'>";
    echo "<h3>❌ FATAL ERROR FOUND!</h3>";
    echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>File:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    echo "<h4>Stack Trace:</h4>";
    echo "<pre style='max-height: 300px; overflow-y: auto; background: #f8f8f8; padding: 10px;'>";
    echo htmlspecialchars($e->getTraceAsString());
    echo "</pre>";
    echo "</div>";
} catch (Exception $e) {
    echo "<br><div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px;'>";
    echo "<h3>❌ EXCEPTION FOUND!</h3>";
    echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>File:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    echo "<h4>Stack Trace:</h4>";
    echo "<pre style='max-height: 300px; overflow-y: auto; background: #f8f8f8; padding: 10px;'>";
    echo htmlspecialchars($e->getTraceAsString());
    echo "</pre>";
    echo "</div>";
}

echo "<h2>Debug Information:</h2>";
echo "<p><strong>Server Software:</strong> " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "</p>";
echo "<p><strong>PHP SAPI:</strong> " . php_sapi_name() . "</p>";
echo "<p><strong>Error Reporting:</strong> " . error_reporting() . "</p>";
echo "<p><strong>Display Errors:</strong> " . ini_get('display_errors') . "</p>";
?>

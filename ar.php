<?php
// Arabic Language Route Handler
// This file handles the /ar route from Laravel language switcher

session_start();

// Set language to Arabic
$_SESSION['language'] = 'ar';
$_SESSION['locale'] = 'ar';

// Set persistent cookie
setcookie('language', 'ar', time() + (30 * 24 * 60 * 60), '/');
setcookie('locale', 'ar', time() + (30 * 24 * 60 * 60), '/');

// Redirect to homepage with language parameter
header('Location: /homepage-fixed.php?lang=ar');
exit;
?>

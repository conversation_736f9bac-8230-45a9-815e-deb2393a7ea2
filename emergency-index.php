<?php
echo "<h1>Emergency Index.php Creator</h1>";

$indexPath = __DIR__ . '/index.php';

// Ultra-simple index.php with maximum error reporting
$emergencyIndexContent = '<?php
// Emergency index.php with full error reporting
ini_set("display_errors", 1);
ini_set("display_startup_errors", 1);
error_reporting(E_ALL);

try {
    define("LARAVEL_START", microtime(true));
    
    // Check maintenance mode
    if (file_exists($maintenance = __DIR__."/../storage/framework/maintenance.php")) {
        require $maintenance;
    }
    
    // Load autoloader
    $autoloader = __DIR__."/../vendor/autoload.php";
    if (!file_exists($autoloader)) {
        throw new Exception("Autoloader not found at: " . $autoloader);
    }
    require $autoloader;
    
    // Load Laravel app
    $appFile = __DIR__."/../bootstrap/app.php";
    if (!file_exists($appFile)) {
        throw new Exception("Bootstrap file not found at: " . $appFile);
    }
    $app = require_once $appFile;
    
    // Create kernel
    $kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);
    
    // Handle request
    $request = Illuminate\Http\Request::capture();
    $response = $kernel->handle($request);
    $response->send();
    
    // Terminate
    $kernel->terminate($request, $response);
    
} catch (Throwable $e) {
    // Show detailed error information
    echo "<h1>Laravel Application Error</h1>";
    echo "<div style=\"background: #f8d7da; padding: 20px; border: 1px solid #f5c6cb; border-radius: 5px; margin: 20px;\">";
    echo "<h2>Error Details:</h2>";
    echo "<p><strong>Message:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>File:</strong> " . htmlspecialchars($e->getFile()) . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    echo "<h3>Stack Trace:</h3>";
    echo "<pre style=\"background: #f8f8f8; padding: 10px; max-height: 400px; overflow-y: auto;\">";
    echo htmlspecialchars($e->getTraceAsString());
    echo "</pre>";
    echo "</div>";
    
    echo "<h2>System Information:</h2>";
    echo "<p>PHP Version: " . phpversion() . "</p>";
    echo "<p>Current Directory: " . getcwd() . "</p>";
    echo "<p>Script Path: " . __FILE__ . "</p>";
    
    http_response_code(500);
}
?>';

echo "<h2>Creating Emergency index.php:</h2>";

// Backup current index.php
if (file_exists($indexPath)) {
    $backupPath = $indexPath . '.emergency-backup.' . date('Y-m-d-H-i-s');
    if (copy($indexPath, $backupPath)) {
        echo "✅ Current index.php backed up as: " . basename($backupPath) . "<br>";
    }
}

// Write emergency index.php
if (file_put_contents($indexPath, $emergencyIndexContent) !== false) {
    echo "✅ Emergency index.php created successfully!<br>";
    echo "File size: " . filesize($indexPath) . " bytes<br>";
    
    echo "<br><div style='background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; border-radius: 5px;'>";
    echo "<h3>🚨 Emergency index.php Active</h3>";
    echo "<p>This version will show detailed error messages if something goes wrong.</p>";
    echo "<p><strong><a href='/' style='font-size: 18px;'>🔍 TEST APPLICATION WITH DETAILED ERRORS</a></strong></p>";
    echo "</div>";
    
} else {
    echo "❌ Failed to create emergency index.php<br>";
}

echo "<h2>What This Emergency Version Does:</h2>";
echo "<ul>";
echo "<li>✅ Shows all PHP errors and warnings</li>";
echo "<li>✅ Provides detailed error messages</li>";
echo "<li>✅ Shows exact file and line where errors occur</li>";
echo "<li>✅ Displays full stack trace</li>";
echo "<li>✅ Shows system information</li>";
echo "</ul>";

echo "<h2>Next Steps:</h2>";
echo "<ol>";
echo "<li><a href='minimal-test.php'>Run minimal test first</a></li>";
echo "<li><a href='/'>Test application with emergency index.php</a></li>";
echo "<li>Check Hostinger error logs in control panel</li>";
echo "<li>Share any error messages you see</li>";
echo "</ol>";
?>

<?php
// Exhibitions Index Page
// This page displays all available exhibitions

session_start();

// Get language
$currentLang = $_SESSION['language'] ?? $_COOKIE['language'] ?? $_GET['lang'] ?? 'ar';

// Include database configuration
require_once '../config/database.php';

try {
    $pdo = getDatabaseConnection();
    
    // Get all published exhibitions
    $stmt = $pdo->query("
        SELECT 
            e.id,
            e.title,
            e.description,
            e.start_date,
            e.end_date,
            e.venue_name,
            e.city,
            e.featured_image,
            COUNT(b.id) as total_booths,
            COUNT(CASE WHEN b.status = 'available' THEN 1 END) as available_booths
        FROM exhibitions e
        LEFT JOIN booths b ON e.id = b.exhibition_id
        WHERE e.status = 'published'
        GROUP BY e.id, e.title, e.description, e.start_date, e.end_date, e.venue_name, e.city, e.featured_image
        ORDER BY e.start_date
    ");
    
    $exhibitions = $stmt->fetchAll(PDO::FETCH_OBJ);
    
} catch (Exception $e) {
    $dbError = "خطأ في قاعدة البيانات: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="<?= $currentLang ?>" dir="<?= $currentLang === 'ar' ? 'rtl' : 'ltr' ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المعارض - Season Expo Kuwait</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg fixed w-full top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="/">
                        <img src="/images/logo.png" alt="Season Expo Kuwait" style="height: 40px; width: auto;" class="hover:opacity-80 transition-opacity">
                    </a>
                </div>
                <div class="flex items-center space-x-reverse space-x-4">
                    <a href="/" class="text-gray-600 hover:text-gray-900">الصفحة الرئيسية</a>
                    <a href="/exhibitions" class="text-blue-600 font-semibold">المعارض</a>
                    <a href="/admin/dashboard.php" class="text-gray-600 hover:text-gray-900">حسابي</a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="pt-16">
        <div class="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
            
            <!-- Page Header -->
            <div class="bg-white rounded-lg shadow-lg overflow-hidden mb-8">
                <div class="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6">
                    <h1 class="text-3xl font-bold">المعارض المتاحة</h1>
                    <p class="text-blue-100 mt-2">اكتشف أفضل المعارض واحجز جناحك الآن</p>
                </div>
            </div>

            <!-- Exhibitions Grid -->
            <?php if (isset($dbError)): ?>
            <div class="bg-red-50 border border-red-200 rounded-lg p-6">
                <div class="flex items-center">
                    <div class="text-red-600 text-xl mr-3">❌</div>
                    <div>
                        <h3 class="font-semibold text-red-800">خطأ في قاعدة البيانات</h3>
                        <p class="text-red-700"><?= $dbError ?></p>
                    </div>
                </div>
            </div>
            <?php elseif (isset($exhibitions) && count($exhibitions) > 0): ?>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <?php foreach ($exhibitions as $exhibition): ?>
                <div class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
                    <!-- Exhibition Image -->
                    <div class="h-48 bg-gradient-to-r from-blue-500 to-purple-600 relative">
                        <?php if ($exhibition->featured_image): ?>
                        <img src="/storage/<?= htmlspecialchars($exhibition->featured_image) ?>" 
                             alt="<?= htmlspecialchars($exhibition->title) ?>"
                             class="w-full h-full object-cover">
                        <?php else: ?>
                        <div class="w-full h-full flex items-center justify-center text-white">
                            <div class="text-center">
                                <div class="text-4xl mb-2">🏢</div>
                                <p class="text-lg font-semibold">معرض</p>
                            </div>
                        </div>
                        <?php endif; ?>
                        
                        <!-- Status Badge -->
                        <div class="absolute top-4 right-4">
                            <span class="bg-green-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                                متاح
                            </span>
                        </div>
                    </div>
                    
                    <!-- Exhibition Info -->
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-gray-900 mb-2"><?= htmlspecialchars($exhibition->title) ?></h3>
                        <p class="text-gray-600 mb-4"><?= htmlspecialchars($exhibition->description) ?></p>
                        
                        <!-- Exhibition Details -->
                        <div class="space-y-2 mb-4">
                            <div class="flex items-center text-sm text-gray-600">
                                <span class="font-semibold mr-2">📅 التاريخ:</span>
                                <?= date('d/m/Y', strtotime($exhibition->start_date)) ?> - <?= date('d/m/Y', strtotime($exhibition->end_date)) ?>
                            </div>
                            <div class="flex items-center text-sm text-gray-600">
                                <span class="font-semibold mr-2">📍 المكان:</span>
                                <?= htmlspecialchars($exhibition->venue_name) ?>
                            </div>
                            <div class="flex items-center text-sm text-gray-600">
                                <span class="font-semibold mr-2">🏙️ المدينة:</span>
                                <?= htmlspecialchars($exhibition->city) ?>
                            </div>
                        </div>
                        
                        <!-- Booth Statistics -->
                        <div class="bg-gray-50 rounded-lg p-3 mb-4">
                            <div class="grid grid-cols-2 gap-4 text-center">
                                <div>
                                    <div class="text-lg font-bold text-blue-600"><?= $exhibition->total_booths ?></div>
                                    <div class="text-xs text-gray-600">إجمالي الأجنحة</div>
                                </div>
                                <div>
                                    <div class="text-lg font-bold text-green-600"><?= $exhibition->available_booths ?></div>
                                    <div class="text-xs text-gray-600">أجنحة متاحة</div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Action Buttons -->
                        <div class="flex space-x-reverse space-x-2">
                            <a href="/exhibitions/details.php?id=<?= $exhibition->id ?>" 
                               class="flex-1 bg-blue-600 text-white text-center py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors">
                                عرض التفاصيل
                            </a>
                            <?php if ($exhibition->available_booths > 0): ?>
                            <a href="/booking/create.php?exhibition_id=<?= $exhibition->id ?>" 
                               class="flex-1 bg-green-600 text-white text-center py-2 px-4 rounded-lg hover:bg-green-700 transition-colors">
                                احجز الآن
                            </a>
                            <?php else: ?>
                            <span class="flex-1 bg-gray-400 text-white text-center py-2 px-4 rounded-lg cursor-not-allowed">
                                مكتمل
                            </span>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
            <?php else: ?>
            <div class="text-center py-12">
                <div class="text-6xl mb-4">🏢</div>
                <h3 class="text-xl font-bold text-gray-900 mb-2">لا توجد معارض متاحة</h3>
                <p class="text-gray-600 mb-6">لا توجد معارض منشورة حالياً</p>
                <a href="/" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700">
                    العودة للصفحة الرئيسية
                </a>
            </div>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>

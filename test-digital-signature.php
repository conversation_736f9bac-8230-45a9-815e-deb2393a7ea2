<?php
/**
 * اختبار نظام التوقيع الإلكتروني
 * Test Digital Signature System
 */

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='utf-8'>";
echo "<title>اختبار نظام التوقيع الإلكتروني - Season Expo</title>";
echo "<script src='https://cdn.tailwindcss.com'></script>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }";
echo ".test-section { background: white; border: 2px solid #0066cc; border-radius: 10px; padding: 20px; margin: 20px 0; }";
echo ".test-title { color: #0066cc; font-size: 18px; font-weight: bold; margin-bottom: 15px; }";
echo ".test-result { padding: 10px; border-radius: 5px; margin: 10px 0; }";
echo ".success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }";
echo ".error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }";
echo ".info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }";
echo "</style>";
echo "</head>";
echo "<body class='bg-gray-50 p-8'>";

echo "<div class='max-w-4xl mx-auto'>";
echo "<h1 class='text-3xl font-bold text-center text-blue-600 mb-8'>🔍 اختبار نظام التوقيع الإلكتروني</h1>";

// Test 1: Check if routes exist
echo "<div class='test-section'>";
echo "<div class='test-title'>1. اختبار وجود المسارات (Routes)</div>";

$routes_to_test = [
    '/signatures' => 'صفحة التوقيعات الرئيسية',
    '/signatures/create-simple' => 'صفحة إنشاء توقيع جديد',
    '/signatures/company-declaration' => 'صفحة إقرار وتعهد الشركات',
];

foreach ($routes_to_test as $route => $description) {
    $full_url = "http://localhost/season_expo_2" . $route;

    // Use file_get_contents with context to test routes
    $context = stream_context_create([
        'http' => [
            'timeout' => 5,
            'ignore_errors' => true
        ]
    ]);

    $result = @file_get_contents($full_url, false, $context);

    if ($result !== false) {
        echo "<div class='test-result success'>✅ {$description}: متاح</div>";
    } else {
        echo "<div class='test-result error'>❌ {$description}: غير متاح</div>";
    }
}
echo "</div>";

// Test 2: Check database connection and digital signatures table
echo "<div class='test-section'>";
echo "<div class='test-title'>2. اختبار قاعدة البيانات وجدول التوقيعات</div>";

try {
    // Database connection
    $host = 'localhost';
    $dbname = 'season_expo_2';
    $username = 'root';
    $password = '';

    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    echo "<div class='test-result success'>✅ الاتصال بقاعدة البيانات: نجح</div>";

    // Check if digital_signatures table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'digital_signatures'");
    if ($stmt->rowCount() > 0) {
        echo "<div class='test-result success'>✅ جدول التوقيعات الرقمية: موجود</div>";

        // Check table structure
        $stmt = $pdo->query("DESCRIBE digital_signatures");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);

        $required_columns = [
            'id', 'document_type', 'document_id', 'user_id', 'signer_name',
            'signer_email', 'signature_data', 'metadata', 'signed_at',
            'is_verified', 'verification_token'
        ];

        $missing_columns = array_diff($required_columns, $columns);

        if (empty($missing_columns)) {
            echo "<div class='test-result success'>✅ هيكل الجدول: مكتمل</div>";
        } else {
            echo "<div class='test-result error'>❌ أعمدة مفقودة: " . implode(', ', $missing_columns) . "</div>";
        }

        // Count existing signatures
        $stmt = $pdo->query("SELECT COUNT(*) FROM digital_signatures");
        $count = $stmt->fetchColumn();
        echo "<div class='test-result info'>📊 عدد التوقيعات الموجودة: {$count}</div>";

    } else {
        echo "<div class='test-result error'>❌ جدول التوقيعات الرقمية: غير موجود</div>";
    }

} catch (Exception $e) {
    echo "<div class='test-result error'>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</div>";
}
echo "</div>";

// Test 3: Check required files
echo "<div class='test-section'>";
echo "<div class='test-title'>3. اختبار وجود الملفات المطلوبة</div>";

$required_files = [
    'app/Models/DigitalSignature.php' => 'نموذج التوقيع الرقمي',
    'app/Http/Controllers/DigitalSignatureController.php' => 'تحكم التوقيع الرقمي',
    'resources/views/signatures/create-simple.blade.php' => 'صفحة إنشاء توقيع',
    'resources/views/signatures/company-declaration.blade.php' => 'صفحة إقرار الشركات',
    'routes/web.php' => 'ملف المسارات',
];

foreach ($required_files as $file => $description) {
    if (file_exists($file)) {
        echo "<div class='test-result success'>✅ {$description}: موجود</div>";
    } else {
        echo "<div class='test-result error'>❌ {$description}: غير موجود</div>";
    }
}
echo "</div>";

// Test 4: Check storage directories
echo "<div class='test-section'>";
echo "<div class='test-title'>4. اختبار مجلدات التخزين</div>";

$storage_dirs = [
    'storage/app/public/signatures' => 'مجلد التوقيعات',
    'storage/app/public/certificates' => 'مجلد الشهادات',
    'storage/app/public/agreements' => 'مجلد الاتفاقيات',
    'storage/app/public/declarations' => 'مجلد الإقرارات',
];

foreach ($storage_dirs as $dir => $description) {
    if (is_dir($dir)) {
        $writable = is_writable($dir) ? 'قابل للكتابة' : 'غير قابل للكتابة';
        echo "<div class='test-result success'>✅ {$description}: موجود ({$writable})</div>";
    } else {
        // Try to create directory
        if (mkdir($dir, 0755, true)) {
            echo "<div class='test-result success'>✅ {$description}: تم إنشاؤه</div>";
        } else {
            echo "<div class='test-result error'>❌ {$description}: غير موجود ولا يمكن إنشاؤه</div>";
        }
    }
}
echo "</div>";

// Test 5: Sample signature creation test
echo "<div class='test-section'>";
echo "<div class='test-title'>5. اختبار إنشاء توقيع تجريبي</div>";

try {
    if (isset($pdo)) {
        // Create a test signature record
        $test_data = [
            'document_type' => 'test_document',
            'document_id' => 'TEST-' . time(),
            'user_id' => 1, // Assuming user ID 1 exists
            'signer_name' => 'اختبار النظام',
            'signer_email' => '<EMAIL>',
            'signer_ip' => '127.0.0.1',
            'signature_data' => 'TEST_SIGNATURE_DATA',
            'metadata' => json_encode(['test' => true]),
            'signed_at' => date('Y-m-d H:i:s'),
            'is_verified' => 1,
            'verified_at' => date('Y-m-d H:i:s'),
            'verification_token' => bin2hex(random_bytes(16)),
        ];

        $sql = "INSERT INTO digital_signatures (" . implode(', ', array_keys($test_data)) . ") VALUES (:" . implode(', :', array_keys($test_data)) . ")";
        $stmt = $pdo->prepare($sql);

        if ($stmt->execute($test_data)) {
            $test_id = $pdo->lastInsertId();
            echo "<div class='test-result success'>✅ إنشاء توقيع تجريبي: نجح (ID: {$test_id})</div>";

            // Clean up test record
            $pdo->prepare("DELETE FROM digital_signatures WHERE id = ?")->execute([$test_id]);
            echo "<div class='test-result info'>🧹 تم حذف البيانات التجريبية</div>";
        } else {
            echo "<div class='test-result error'>❌ إنشاء توقيع تجريبي: فشل</div>";
        }
    }
} catch (Exception $e) {
    echo "<div class='test-result error'>❌ خطأ في اختبار التوقيع: " . $e->getMessage() . "</div>";
}
echo "</div>";

// Navigation links
echo "<div class='test-section'>";
echo "<div class='test-title'>🔗 روابط سريعة للاختبار</div>";
echo "<div class='flex flex-wrap gap-4'>";
echo "<a href='/season_expo_2/signatures' class='bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700'>صفحة التوقيعات</a>";
echo "<a href='/season_expo_2/signatures/create-simple' class='bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700'>إنشاء توقيع</a>";
echo "<a href='/season_expo_2/signatures/company-declaration' class='bg-orange-600 text-white px-4 py-2 rounded hover:bg-orange-700'>إقرار الشركات</a>";
echo "<a href='/season_expo_2/' class='bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700'>الصفحة الرئيسية</a>";

// Test signature viewing functionality
if (isset($pdo)) {
    echo "<br><br>";
    echo "<div class='test-result info'>🔍 اختبار عرض التوقيعات:</div>";

    try {
        $stmt = $pdo->query("SELECT id, signer_name, document_type, signed_at FROM digital_signatures ORDER BY created_at DESC LIMIT 3");
        $signatures = $stmt->fetchAll(PDO::FETCH_ASSOC);

        if (count($signatures) > 0) {
            echo "<div style='margin: 10px 0;'>";
            foreach ($signatures as $sig) {
                echo "<a href='/season_expo_2/signatures/{$sig['id']}/view-signature' class='bg-purple-600 text-white px-3 py-1 rounded text-sm hover:bg-purple-700 mr-2'>عرض توقيع #{$sig['id']}</a>";
            }
            echo "</div>";
        } else {
            echo "<div class='test-result info'>لا توجد توقيعات للعرض</div>";
        }
    } catch (Exception $e) {
        echo "<div class='test-result error'>خطأ في جلب التوقيعات: " . $e->getMessage() . "</div>";
    }
}
echo "</div>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('booths', function (Blueprint $table) {
            $table->id();
            $table->foreignId('exhibition_id')->constrained()->onDelete('cascade');
            $table->string('booth_number');
            $table->string('name')->nullable();
            $table->text('description')->nullable();
            $table->enum('size', ['small', 'medium', 'large', 'premium'])->default('medium');
            $table->decimal('width', 8, 2);
            $table->decimal('height', 8, 2);
            $table->decimal('area', 8, 2);
            $table->decimal('price', 10, 2);
            $table->string('location')->nullable(); // e.g., "Hall A, Section 1"
            $table->json('features')->nullable(); // e.g., ["electricity", "wifi", "storage"]
            $table->json('position')->nullable(); // x, y coordinates for floor plan
            $table->enum('status', ['available', 'booked', 'reserved', 'maintenance'])->default('available');
            $table->boolean('is_featured')->default(false);
            $table->boolean('is_corner')->default(false);
            $table->string('image')->nullable();
            $table->timestamps();

            $table->unique(['exhibition_id', 'booth_number']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('booths');
    }
};

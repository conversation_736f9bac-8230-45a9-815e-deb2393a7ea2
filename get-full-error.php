<?php
// Get the full <PERSON>vel error details
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h1>Full Laravel Error Details</h1>";

try {
    define('LARAVEL_START', microtime(true));
    
    require dirname(__DIR__) . '/vendor/autoload.php';
    $app = require_once dirname(__DIR__) . '/bootstrap/app.php';
    
    // Enable debug mode temporarily
    $app->make('config')->set('app.debug', true);
    
    $kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);
    
    // Create a request for the homepage
    $request = Illuminate\Http\Request::create('/', 'GET');
    
    echo "<h2>Testing Homepage Request:</h2>";
    echo "URL: /<br>";
    echo "Method: GET<br><br>";
    
    $response = $kernel->handle($request);
    
    echo "<h2>Response Details:</h2>";
    echo "Status: " . $response->getStatusCode() . "<br>";
    echo "Content Length: " . strlen($response->getContent()) . " bytes<br><br>";
    
    $content = $response->getContent();
    
    if ($response->getStatusCode() === 500) {
        echo "<h2>❌ 500 Error - Full Content:</h2>";
        echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px; max-height: 600px; overflow-y: auto;'>";
        echo $content; // Show the actual Laravel error page
        echo "</div>";
    } else {
        echo "<h2>✅ Success - Content Preview:</h2>";
        echo "<div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px; max-height: 400px; overflow-y: auto;'>";
        echo "<pre>" . htmlspecialchars(substr($content, 0, 1000)) . "</pre>";
        echo "</div>";
    }
    
    $kernel->terminate($request, $response);
    
} catch (Throwable $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px;'>";
    echo "<h3>❌ Exception Caught:</h3>";
    echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>File:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    echo "<details><summary>Stack Trace</summary>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
    echo "</details>";
    echo "</div>";
}
?>

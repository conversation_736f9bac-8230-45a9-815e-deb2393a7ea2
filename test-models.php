<?php
echo "<h1>Test Season Expo Models</h1>";

try {
    require __DIR__ . '/vendor/autoload.php';
    $app = require_once __DIR__ . '/bootstrap/app.php';
    
    echo "<h2>Laravel Status:</h2>";
    echo "✅ Laravel loaded successfully<br>";
    
    echo "<h2>Database Connection Test:</h2>";
    
    // Test basic database connection
    try {
        $pdo = new PDO(
            'mysql:host=' . env('DB_HOST', 'localhost') . ';dbname=' . env('DB_DATABASE'),
            env('DB_USERNAME'),
            env('DB_PASSWORD')
        );
        echo "✅ Database connection successful<br>";
        
        // Test if Season Expo tables exist
        $tables = ['exhibitions', 'categories', 'booths', 'bookings'];
        foreach ($tables as $table) {
            $stmt = $pdo->query("SHOW TABLES LIKE '{$table}'");
            if ($stmt->rowCount() > 0) {
                $count = $pdo->query("SELECT COUNT(*) FROM {$table}")->fetchColumn();
                echo "✅ Table '{$table}' exists with {$count} records<br>";
            } else {
                echo "❌ Table '{$table}' missing<br>";
            }
        }
        
    } catch (Exception $e) {
        echo "❌ Database error: " . $e->getMessage() . "<br>";
    }
    
    echo "<h2>Model Test:</h2>";
    
    // Test if Season Expo models work
    try {
        if (class_exists('App\Models\Exhibition')) {
            echo "✅ Exhibition model exists<br>";
            $exhibitions = \App\Models\Exhibition::count();
            echo "✅ Exhibition count: {$exhibitions}<br>";
        } else {
            echo "❌ Exhibition model missing<br>";
        }
        
        if (class_exists('App\Models\Category')) {
            echo "✅ Category model exists<br>";
            $categories = \App\Models\Category::count();
            echo "✅ Category count: {$categories}<br>";
        } else {
            echo "❌ Category model missing<br>";
        }
        
    } catch (Exception $e) {
        echo "❌ Model error: " . $e->getMessage() . "<br>";
    }
    
    echo "<br><div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px;'>";
    echo "<h3>✅ Models Test Complete!</h3>";
    echo "<p>If everything shows ✅ above, models are working correctly.</p>";
    echo "<p><a href='/'>Test Laravel Homepage</a> - should still work</p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px;'>";
    echo "<h3>❌ Error:</h3>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}
?>

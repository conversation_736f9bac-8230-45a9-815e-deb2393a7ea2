/* Arabic Language Support */
[dir="rtl"] {
    font-family: 'Segoe UI', 'Tahoma', 'Arial', 'Helvetica Neue', sans-serif;
}

/* RTL specific styles */
[dir="rtl"] .space-x-4 > * + * {
    margin-left: 0;
    margin-right: 1rem;
}

[dir="rtl"] .space-x-reverse > * + * {
    margin-left: 1rem;
    margin-right: 0;
}

/* Arabic text improvements */
[dir="rtl"] h1, [dir="rtl"] h2, [dir="rtl"] h3, [dir="rtl"] h4, [dir="rtl"] h5, [dir="rtl"] h6 {
    font-weight: 600;
    line-height: 1.4;
}

[dir="rtl"] p {
    line-height: 1.6;
}

/* Button and form adjustments for RTL */
[dir="rtl"] .flex-row-reverse {
    flex-direction: row-reverse;
}

[dir="rtl"] .text-left {
    text-align: right;
}

[dir="rtl"] .text-right {
    text-align: left;
}

/* Language switcher positioning */
[dir="rtl"] .language-switcher {
    left: 0;
    right: auto;
}

[dir="ltr"] .language-switcher {
    right: 0;
    left: auto;
}

/* Fixed Navigation Enhancements */
.fixed-nav {
    backdrop-filter: blur(10px);
    background-color: rgba(255, 255, 255, 0.95);
    transition: all 0.3s ease;
}

.fixed-nav:hover {
    background-color: rgba(255, 255, 255, 1);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Navigation link hover effects */
.nav-link {
    position: relative;
    transition: color 0.3s ease;
}

.nav-link:hover {
    color: #2563eb;
}

.nav-link::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: -4px;
    left: 0;
    background-color: #2563eb;
    transition: width 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
}

/* RTL navigation link positioning */
[dir="rtl"] .nav-link::after {
    right: 0;
    left: auto;
}

/* Mobile navigation improvements */
@media (max-width: 768px) {
    .fixed-nav {
        padding: 0 1rem;
    }

    .nav-link {
        font-size: 0.875rem;
        padding: 0.5rem;
    }

    .nav-link::after {
        display: none;
    }
}

/* Enhanced button hover effects */
.btn-primary {
    transition: all 0.3s ease;
    transform: translateY(0);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
}

/* Text truncation utility */
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Card hover effects */
.card-hover {
    transition: all 0.3s ease;
}

.card-hover:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Image zoom effect */
.image-zoom {
    transition: transform 0.3s ease;
}

.image-zoom:hover {
    transform: scale(1.05);
}

/* Image fallback backgrounds */
.hero-slide {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.tech-card {
    background: linear-gradient(135deg, #4F46E5 0%, #7C3AED 100%);
}

.healthcare-card {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
}

.food-card {
    background: linear-gradient(135deg, #EA580C 0%, #DC2626 100%);
}

/* Ensure images cover the gradient backgrounds */
.hero-slide img,
.tech-card img,
.healthcare-card img,
.food-card img {
    position: relative;
    z-index: 1;
}

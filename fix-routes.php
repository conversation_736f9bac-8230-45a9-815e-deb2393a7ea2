<?php
echo "<h1>Fix Routes Loading</h1>";

$routesFile = __DIR__ . '/routes/web.php';

echo "<h2>Current routes/web.php Status:</h2>";

if (file_exists($routesFile)) {
    echo "✅ File exists<br>";
    echo "Size: " . filesize($routesFile) . " bytes<br>";
    
    $content = file_get_contents($routesFile);
    
    echo "<h3>Current Content:</h3>";
    echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 5px; max-height: 400px; overflow-y: auto;'>";
    echo htmlspecialchars($content);
    echo "</pre>";
    
    // Check if it's the default Laravel routes
    if (strpos($content, "return view('welcome')") !== false) {
        echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3>❌ Problem Found!</h3>";
        echo "<p>You still have the default Laravel routes file, not the Season Expo routes.</p>";
        echo "</div>";
        
        echo "<h2>Fix: Install Season Expo Routes</h2>";
        
        $seasonExpoRoutes = '<?php

use App\Http\Controllers\HomeController;
use App\Http\Controllers\ExhibitionController;
use App\Http\Controllers\BookingController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\ProfileController;
use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
*/

// Home route
Route::get(\'/\', [HomeController::class, \'index\'])->name(\'home\');

// Exhibition routes
Route::get(\'/exhibitions\', [ExhibitionController::class, \'index\'])->name(\'exhibitions.index\');
Route::get(\'/exhibitions/{exhibition:slug}\', [ExhibitionController::class, \'show\'])->name(\'exhibitions.show\');

// Authentication routes
require __DIR__.\'/auth.php\';

// Authenticated routes
Route::middleware([\'auth\', \'verified\'])->group(function () {
    Route::get(\'/dashboard\', [DashboardController::class, \'index\'])->name(\'dashboard\');
    Route::get(\'/profile\', [ProfileController::class, \'edit\'])->name(\'profile.edit\');
    Route::patch(\'/profile\', [ProfileController::class, \'update\'])->name(\'profile.update\');
    Route::delete(\'/profile\', [ProfileController::class, \'destroy\'])->name(\'profile.destroy\');
    
    // Booking routes
    Route::get(\'/exhibitions/{exhibition}/book\', [BookingController::class, \'create\'])->name(\'bookings.create\');
    Route::post(\'/exhibitions/{exhibition}/book\', [BookingController::class, \'store\'])->name(\'bookings.store\');
    Route::get(\'/my-bookings\', [BookingController::class, \'index\'])->name(\'bookings.index\');
});

// Admin routes
Route::middleware([\'auth\', \'admin\'])->prefix(\'admin\')->name(\'admin.\')->group(function () {
    Route::get(\'/\', [DashboardController::class, \'admin\'])->name(\'dashboard\');
    // Add more admin routes here
});';
        
        if (isset($_POST['install_routes'])) {
            if (file_put_contents($routesFile, $seasonExpoRoutes) !== false) {
                echo "<div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px; margin: 20px 0;'>";
                echo "<h3>✅ Season Expo Routes Installed!</h3>";
                echo "<p><a href='clear-caches.php'>Clear Caches and Test</a></p>";
                echo "</div>";
            } else {
                echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px; margin: 20px 0;'>";
                echo "<h3>❌ Failed to install routes</h3>";
                echo "<p>Please update manually via File Manager</p>";
                echo "</div>";
            }
        } else {
            echo "<h3>Season Expo Routes to Install:</h3>";
            echo "<textarea style='width: 100%; height: 300px; font-family: monospace; font-size: 12px;'>";
            echo htmlspecialchars($seasonExpoRoutes);
            echo "</textarea><br><br>";
            
            echo "<form method='post'>";
            echo "<button type='submit' name='install_routes' style='background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>Install Season Expo Routes</button>";
            echo "</form>";
        }
        
    } else {
        echo "<div style='background: #d1ecf1; padding: 15px; border: 1px solid #bee5eb; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3>ℹ️ Custom Routes Detected</h3>";
        echo "<p>You have custom routes, but they might not be loading properly.</p>";
        echo "</div>";
    }
    
} else {
    echo "❌ routes/web.php missing!<br>";
    echo "<p>You need to upload the Season Expo routes/web.php file.</p>";
}

echo "<h2>Clear Caches After Route Changes:</h2>";
echo "<div style='background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; border-radius: 5px;'>";
echo "<p><strong>Important:</strong> After changing routes, you must clear caches.</p>";
echo "<p><a href='clear-caches.php' style='background: #ffc107; color: black; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>Clear All Caches</a></p>";
echo "</div>";
?>

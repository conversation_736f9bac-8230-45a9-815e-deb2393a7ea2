<?php
echo "<h1>Storage Setup - Multiple Methods</h1>";

$publicPath = __DIR__;
$storagePath = dirname(__DIR__) . '/storage/app/public';
$linkPath = $publicPath . '/storage';

echo "<h2>Method 1: Force Symlink Creation</h2>";

// Remove existing if present
if (file_exists($linkPath)) {
    if (is_link($linkPath)) {
        unlink($linkPath);
        echo "Removed existing symlink<br>";
    } elseif (is_dir($linkPath)) {
        rmdir($linkPath);
        echo "Removed existing directory<br>";
    } else {
        unlink($linkPath);
        echo "Removed existing file<br>";
    }
}

// Try symlink
if (symlink($storagePath, $linkPath)) {
    echo "✅ SUCCESS: Symlink created!<br>";
    echo "<a href='/'>Test Application Now</a><br>";
} else {
    echo "❌ Symlink failed, trying alternative methods...<br>";
    
    echo "<h2>Method 2: Directory Copy</h2>";
    
    // Create storage directory
    if (!is_dir($linkPath)) {
        mkdir($linkPath, 0755, true);
        echo "✅ Created storage directory<br>";
    }
    
    // Copy files
    if (is_dir($storagePath)) {
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($storagePath, RecursiveDirectoryIterator::SKIP_DOTS),
            RecursiveIteratorIterator::SELF_FIRST
        );
        
        $copied = 0;
        foreach ($iterator as $item) {
            $target = $linkPath . DIRECTORY_SEPARATOR . $iterator->getSubPathName();
            if ($item->isDir()) {
                if (!is_dir($target)) {
                    mkdir($target, 0755, true);
                }
            } else {
                if (copy($item, $target)) {
                    $copied++;
                }
            }
        }
        
        echo "✅ Copied {$copied} files to public storage<br>";
        echo "✅ Storage directory setup complete<br>";
        
    } else {
        echo "❌ Source storage directory not found<br>";
    }
}

echo "<h2>Method 3: Laravel Storage Link Command</h2>";

try {
    require '../vendor/autoload.php';
    $app = require_once '../bootstrap/app.php';
    $kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
    
    $kernel->call('storage:link');
    echo "✅ Laravel storage:link command executed<br>";
    
} catch (Exception $e) {
    echo "❌ Laravel command failed: " . $e->getMessage() . "<br>";
}

echo "<h2>Verification:</h2>";

if (file_exists($linkPath)) {
    if (is_link($linkPath)) {
        echo "✅ Storage symlink exists and working<br>";
        echo "Target: " . readlink($linkPath) . "<br>";
    } elseif (is_dir($linkPath)) {
        echo "✅ Storage directory exists<br>";
        $files = scandir($linkPath);
        echo "Files: " . (count($files) - 2) . "<br>";
    }
    
    echo "<br><strong><a href='/' style='font-size: 18px; color: blue;'>🚀 TEST SEASON EXPO APPLICATION</a></strong><br>";
    
} else {
    echo "❌ Storage still not accessible<br>";
    
    echo "<h2>Manual Setup Required:</h2>";
    echo "<div style='background: #ffeeee; padding: 15px; border: 1px solid #ff0000;'>";
    echo "<p><strong>Please do this manually in File Manager:</strong></p>";
    echo "<ol>";
    echo "<li>Go to: /home/<USER>/domains/myapps.fjt-q8.com/storage/app/public/</li>";
    echo "<li>Copy all contents</li>";
    echo "<li>Go to: /home/<USER>/domains/myapps.fjt-q8.com/public_html/</li>";
    echo "<li>Create folder named 'storage'</li>";
    echo "<li>Paste contents into the storage folder</li>";
    echo "</ol>";
    echo "</div>";
}
?>

<?php
echo "<h1>Fix Laravel Configuration</h1>";

$rootPath = dirname(__DIR__);

try {
    // Load Laravel without using the config service
    require $rootPath . '/vendor/autoload.php';
    echo "✅ Autoloader loaded<br>";
    
    $app = require $rootPath . '/bootstrap/app.php';
    echo "✅ Laravel application loaded<br>";
    
    // Try to get the kernel directly
    $kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
    echo "✅ Console kernel loaded<br>";
    
    echo "<h2>Clearing All Caches:</h2>";
    
    // Clear caches one by one with error handling
    try {
        $kernel->call('config:clear');
        echo "✅ Config cache cleared<br>";
    } catch (Exception $e) {
        echo "⚠️ Config clear: " . $e->getMessage() . "<br>";
    }
    
    try {
        $kernel->call('cache:clear');
        echo "✅ Application cache cleared<br>";
    } catch (Exception $e) {
        echo "⚠️ Cache clear: " . $e->getMessage() . "<br>";
    }
    
    try {
        $kernel->call('view:clear');
        echo "✅ View cache cleared<br>";
    } catch (Exception $e) {
        echo "⚠️ View clear: " . $e->getMessage() . "<br>";
    }
    
    try {
        $kernel->call('route:clear');
        echo "✅ Route cache cleared<br>";
    } catch (Exception $e) {
        echo "⚠️ Route clear: " . $e->getMessage() . "<br>";
    }
    
    // Manual cache clearing
    echo "<h2>Manual Cache Clearing:</h2>";
    
    // Clear bootstrap cache
    $bootstrapCachePath = $rootPath . '/bootstrap/cache';
    if (is_dir($bootstrapCachePath)) {
        $files = glob($bootstrapCachePath . '/*.php');
        $deleted = 0;
        foreach ($files as $file) {
            if (unlink($file)) {
                $deleted++;
            }
        }
        echo "✅ Deleted {$deleted} bootstrap cache files<br>";
    }
    
    // Clear storage caches
    $storageCachePaths = [
        '/storage/framework/cache/data',
        '/storage/framework/views',
        '/storage/framework/sessions'
    ];
    
    foreach ($storageCachePaths as $cachePath) {
        $fullPath = $rootPath . $cachePath;
        if (is_dir($fullPath)) {
            $files = glob($fullPath . '/*');
            $deleted = 0;
            foreach ($files as $file) {
                if (is_file($file) && basename($file) !== '.gitkeep') {
                    if (unlink($file)) {
                        $deleted++;
                    }
                }
            }
            echo "✅ Cleared {$deleted} files from {$cachePath}<br>";
        }
    }
    
    echo "<h2>Testing Configuration:</h2>";
    
    // Test configuration loading manually
    $configPath = $rootPath . '/config/app.php';
    if (file_exists($configPath)) {
        echo "✅ Config file exists<br>";
        
        // Try to load config manually
        $config = require $configPath;
        if (is_array($config)) {
            echo "✅ Config file loads correctly<br>";
            echo "App Name: " . ($config['name'] ?? 'Not set') . "<br>";
        } else {
            echo "❌ Config file format error<br>";
        }
    } else {
        echo "❌ Config file missing<br>";
    }
    
    echo "<br><div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px;'>";
    echo "<h3 style='color: #155724;'>✅ Cache Clearing Complete!</h3>";
    echo "<p>All caches have been cleared. Laravel should now work properly.</p>";
    echo "<p><strong><a href='test-final.php'>Test Application Again</a></strong></p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px;'>";
    echo "<h3 style='color: #721c24;'>❌ Error</h3>";
    echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p>File: " . $e->getFile() . "</p>";
    echo "<p>Line: " . $e->getLine() . "</p>";
    echo "</div>";
    
    echo "<h2>Alternative Solution:</h2>";
    echo "<p>Try accessing your application directly: <a href='/'>Test Homepage</a></p>";
}
?>

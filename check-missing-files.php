<?php
echo "<h1>Season Expo Missing Files Check</h1>";

$basePath = dirname(__DIR__);

// Critical Season Expo files that should exist
$criticalFiles = [
    'routes/web.php' => 'Main routes file',
    'app/Http/Controllers/HomeController.php' => 'Home page controller',
    'app/Http/Controllers/ExhibitionController.php' => 'Exhibition controller',
    'app/Http/Controllers/BookingController.php' => 'Booking controller',
    'app/Models/Exhibition.php' => 'Exhibition model',
    'app/Models/Category.php' => 'Category model',
    'app/Models/Booth.php' => 'Booth model',
    'resources/js/app.js' => 'Main JavaScript file',
    'resources/js/Pages/Welcome.vue' => 'Homepage Vue component',
    'resources/js/Pages/Exhibitions/Index.vue' => 'Exhibitions page',
];

echo "<h2>Critical Files Status:</h2>";
$missingFiles = [];
$existingFiles = [];

foreach ($criticalFiles as $file => $description) {
    $fullPath = $basePath . '/' . $file;
    if (file_exists($fullPath)) {
        echo "✅ {$file} - {$description}<br>";
        $existingFiles[] = $file;
    } else {
        echo "❌ {$file} - {$description} <strong>MISSING</strong><br>";
        $missingFiles[] = $file;
    }
}

echo "<h2>Summary:</h2>";
echo "✅ Existing files: " . count($existingFiles) . "<br>";
echo "❌ Missing files: " . count($missingFiles) . "<br>";

if (count($missingFiles) > 0) {
    echo "<h2>Missing Files:</h2>";
    foreach ($missingFiles as $file) {
        echo "- {$file}<br>";
    }
}

// Check if we have the original zip file
echo "<h2>Recovery Options:</h2>";
$zipFile = $basePath . '/season_expo.zip';
if (file_exists($zipFile)) {
    echo "✅ Original season_expo.zip found (" . round(filesize($zipFile)/1024/1024, 1) . " MB)<br>";
    echo "<strong>Recommendation: Extract the zip file again to restore missing files</strong><br>";
} else {
    echo "❌ Original season_expo.zip not found<br>";
    echo "<strong>Recommendation: Re-upload your Season Expo project</strong><br>";
}

// Check what directories exist
echo "<h2>Directory Structure:</h2>";
$directories = ['app', 'routes', 'resources', 'config', 'database'];
foreach ($directories as $dir) {
    $dirPath = $basePath . '/' . $dir;
    if (is_dir($dirPath)) {
        $files = scandir($dirPath);
        $fileCount = count($files) - 2; // exclude . and ..
        echo "✅ {$dir}/ - {$fileCount} items<br>";
    } else {
        echo "❌ {$dir}/ - MISSING<br>";
    }
}
?>

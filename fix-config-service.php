<?php
echo "<h1>Fix Laravel Config Service</h1>";

try {
    echo "<h2>Step 1: <PERSON><PERSON> with Proper Bootstrap</h2>";
    
    define('LARAVEL_START', microtime(true));
    
    // Load autoloader
    require dirname(__DIR__) . '/vendor/autoload.php';
    echo "✅ Autoloader loaded<br>";
    
    // Load Laravel app
    $app = require_once dirname(__DIR__) . '/bootstrap/app.php';
    echo "✅ Laravel app loaded<br>";
    
    echo "<h2>Step 2: Check Service Container</h2>";
    
    // Check if app is properly initialized
    echo "App class: " . get_class($app) . "<br>";
    echo "App bound services: " . count($app->getBindings()) . "<br>";
    
    // Check if config service exists
    if ($app->bound('config')) {
        echo "✅ Config service is bound<br>";
    } else {
        echo "❌ Config service is NOT bound<br>";
        echo "This is the problem!<br>";
    }
    
    echo "<h2>Step 3: Manual Service Registration</h2>";
    
    // Try to manually register the config service
    if (!$app->bound('config')) {
        echo "Attempting to manually register config service...<br>";
        
        // Register config service manually
        $app->singleton('config', function ($app) {
            return new \Illuminate\Config\Repository();
        });
        
        echo "✅ Config service manually registered<br>";
    }
    
    echo "<h2>Step 4: Bootstrap Application Properly</h2>";
    
    // Try to bootstrap the application properly
    try {
        // Bootstrap the application
        $app->bootstrapWith([
            \Illuminate\Foundation\Bootstrap\LoadEnvironmentVariables::class,
            \Illuminate\Foundation\Bootstrap\LoadConfiguration::class,
            \Illuminate\Foundation\Bootstrap\HandleExceptions::class,
            \Illuminate\Foundation\Bootstrap\RegisterFacades::class,
            \Illuminate\Foundation\Bootstrap\RegisterProviders::class,
            \Illuminate\Foundation\Bootstrap\BootProviders::class,
        ]);
        
        echo "✅ Application bootstrapped successfully<br>";
        
        // Now test config access
        $config = $app->make('config');
        echo "✅ Config service accessible<br>";
        echo "App name: " . $config->get('app.name', 'Not set') . "<br>";
        
    } catch (Exception $e) {
        echo "❌ Bootstrap failed: " . $e->getMessage() . "<br>";
    }
    
    echo "<h2>Step 5: Test Web Request</h2>";
    
    // Now try a web request
    $kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);
    echo "✅ HTTP Kernel created<br>";
    
    $request = Illuminate\Http\Request::create('/', 'GET');
    $response = $kernel->handle($request);
    
    echo "Response status: " . $response->getStatusCode() . "<br>";
    
    if ($response->getStatusCode() === 200) {
        echo "<div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px;'>";
        echo "<h3>🎉 SUCCESS!</h3>";
        echo "<p>The config service issue has been resolved!</p>";
        echo "</div>";
    } else {
        echo "<div style='background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; border-radius: 5px;'>";
        echo "<h3>⚠️ Still Getting " . $response->getStatusCode() . " Error</h3>";
        echo "<p>Config service is fixed, but there might be another issue.</p>";
        echo "</div>";
    }
    
    $kernel->terminate($request, $response);
    
} catch (Throwable $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px;'>";
    echo "<h3>❌ Error:</h3>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p>File: " . $e->getFile() . "</p>";
    echo "<p>Line: " . $e->getLine() . "</p>";
    echo "</div>";
}

echo "<h2>Next Steps:</h2>";
echo "<ul>";
echo "<li><a href='create-fixed-index.php'>Create Fixed index.php</a></li>";
echo "<li><a href='/'>Test Application</a></li>";
echo "</ul>";
?>

<?php
// Setup Database - Auto create tables if they don't exist

function setupSliderTable() {
    try {
        $pdo = new PDO('sqlite:' . __DIR__ . '/database/database.sqlite');
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // Check if slider_images table exists
        $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='slider_images'");
        $table = $stmt->fetch();
        
        if (!$table) {
            // Create slider_images table
            $sql = "CREATE TABLE slider_images (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title VARCHAR(255) NOT NULL,
                description TEXT,
                image_url VARCHAR(500) NOT NULL,
                button_text VARCHAR(100),
                button_link VARCHAR(500),
                background_color VARCHAR(20) DEFAULT '#1e40af',
                text_color VARCHAR(20) DEFAULT '#ffffff',
                is_active BOOLEAN DEFAULT 1,
                sort_order INTEGER DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )";
            
            $pdo->exec($sql);
            
            // Add sample data
            $sampleData = [
                [
                    'title' => 'معرض التكنولوجيا 2025',
                    'description' => 'أحدث التقنيات والابتكارات التكنولوجية\n📅 15-20 مارس 2025 | 📍 مركز المعارض الدولي',
                    'image_url' => 'https://picsum.photos/1200/600?random=tech1',
                    'button_text' => 'عرض التفاصيل',
                    'button_link' => '/exhibitions/1',
                    'background_color' => '#1e40af',
                    'sort_order' => 1
                ],
                [
                    'title' => 'معرض الصحة والجمال',
                    'description' => 'منتجات العناية والصحة والجمال\n📅 22-27 أبريل 2025 | 📍 مركز الكويت التجاري',
                    'image_url' => 'https://picsum.photos/1200/600?random=health2',
                    'button_text' => 'عرض التفاصيل',
                    'button_link' => '/exhibitions/2',
                    'background_color' => '#059669',
                    'sort_order' => 2
                ],
                [
                    'title' => 'معرض الأزياء والموضة',
                    'description' => 'أحدث صيحات الموضة والأزياء\n📅 10-15 مايو 2025 | 📍 مجمع الأفنيوز',
                    'image_url' => 'https://picsum.photos/1200/600?random=fashion3',
                    'button_text' => 'عرض التفاصيل',
                    'button_link' => '/exhibitions/3',
                    'background_color' => '#7c3aed',
                    'sort_order' => 3
                ],
                [
                    'title' => 'معرض الطعام والمشروبات',
                    'description' => 'أشهى المأكولات والمشروبات\n📅 5-10 يونيو 2025 | 📍 مركز الشيخ جابر الثقافي',
                    'image_url' => 'https://picsum.photos/1200/600?random=food4',
                    'button_text' => 'عرض التفاصيل',
                    'button_link' => '/exhibitions/4',
                    'background_color' => '#ea580c',
                    'sort_order' => 4
                ]
            ];
            
            $insertStmt = $pdo->prepare("
                INSERT INTO slider_images (title, description, image_url, button_text, button_link, background_color, sort_order)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ");
            
            foreach ($sampleData as $slide) {
                $insertStmt->execute([
                    $slide['title'],
                    $slide['description'],
                    $slide['image_url'],
                    $slide['button_text'],
                    $slide['button_link'],
                    $slide['background_color'],
                    $slide['sort_order']
                ]);
            }
            
            return "تم إنشاء جدول slider_images وإضافة " . count($sampleData) . " سلايد تجريبي";
        } else {
            return "جدول slider_images موجود بالفعل";
        }
        
    } catch (Exception $e) {
        return "خطأ: " . $e->getMessage();
    }
}

// Auto setup when this file is accessed
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    echo "<h1>🔧 إعداد قاعدة البيانات</h1>";
    
    echo "<h2>إعداد جدول السلايدات:</h2>";
    $result = setupSliderTable();
    echo "<p style='color: " . (strpos($result, 'خطأ') !== false ? 'red' : 'green') . ";'>$result</p>";
    
    // Test database
    try {
        $pdo = new PDO('sqlite:' . __DIR__ . '/database/database.sqlite');
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM slider_images");
        $count = $stmt->fetch()['count'];
        
        echo "<p style='color: green;'>✅ عدد السلايدات في قاعدة البيانات: $count</p>";
        
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM slider_images WHERE is_active = 1");
        $activeCount = $stmt->fetch()['count'];
        
        echo "<p style='color: green;'>✅ عدد السلايدات النشطة: $activeCount</p>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ خطأ في اختبار قاعدة البيانات: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2>الروابط:</h2>";
    echo "<ul>";
    echo "<li><a href='/admin/slider'>لوحة تحكم إدارة السلايد</a></li>";
    echo "<li><a href='/'>الصفحة الرئيسية</a></li>";
    echo "<li><a href='/admin-slider-management.php'>إدارة السلايد المباشرة</a></li>";
    echo "</ul>";
    
    echo "<style>";
    echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }";
    echo "h1, h2 { color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; }";
    echo "ul { background: #f8f9fa; padding: 15px; border-radius: 5px; }";
    echo "a { color: #007bff; text-decoration: none; } a:hover { text-decoration: underline; }";
    echo "</style>";
}

// Function to be called from other files
function ensureSliderTableExists() {
    try {
        $pdo = new PDO('sqlite:' . __DIR__ . '/database/database.sqlite');
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='slider_images'");
        $table = $stmt->fetch();
        
        if (!$table) {
            setupSliderTable();
        }
        
        return true;
    } catch (Exception $e) {
        return false;
    }
}
?>

<script>
// Auto redirect to admin panel after 3 seconds if setup is successful
setTimeout(function() {
    if (document.body.innerHTML.includes('✅')) {
        console.log('Database setup successful, you can now access /admin/slider');
    }
}, 1000);
</script>

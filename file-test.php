<?php
echo "<h1>File Existence Test</h1>";

$basePath = dirname(__DIR__);
echo "Base path: {$basePath}<br><br>";

// Test if directories exist
$dirs = ['vendor', 'bootstrap', 'app', 'config', 'database', 'resources', 'routes', 'storage'];
foreach ($dirs as $dir) {
    $path = $basePath . '/' . $dir;
    if (is_dir($path)) {
        echo "✅ Directory {$dir}: EXISTS<br>";
    } else {
        echo "❌ Directory {$dir}: MISSING<br>";
    }
}

// Test specific files
$files = [
    'vendor/autoload.php',
    'bootstrap/app.php',
    'composer.json',
    'artisan',
    '.env'
];

foreach ($files as $file) {
    $path = $basePath . '/' . $file;
    if (file_exists($path)) {
        echo "✅ File {$file}: EXISTS<br>";
        echo "   Size: " . filesize($path) . " bytes<br>";
    } else {
        echo "❌ File {$file}: MISSING<br>";
    }
}

// List what's actually in the root directory
echo "<h2>Root Directory Contents:</h2>";
if (is_readable($basePath)) {
    $contents = scandir($basePath);
    foreach ($contents as $item) {
        if ($item != '.' && $item != '..') {
            $path = $basePath . '/' . $item;
            $type = is_dir($path) ? 'DIR' : 'FILE';
            $size = is_file($path) ? ' (' . filesize($path) . ' bytes)' : '';
            echo "{$type}: {$item}{$size}<br>";
        }
    }
} else {
    echo "❌ Cannot read root directory<br>";
}

// Check public_html contents
echo "<h2>Public_html Directory Contents:</h2>";
$publicContents = scandir(__DIR__);
foreach ($publicContents as $item) {
    if ($item != '.' && $item != '..') {
        $path = __DIR__ . '/' . $item;
        $type = is_dir($path) ? 'DIR' : 'FILE';
        $size = is_file($path) ? ' (' . filesize($path) . ' bytes)' : '';
        echo "{$type}: {$item}{$size}<br>";
    }
}
?>

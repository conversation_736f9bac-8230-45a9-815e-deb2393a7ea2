<?php
// Check Booth Colors - Diagnostic Tool
// This file helps identify the booth color issue

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='utf-8'>";
echo "<title>فحص ألوان الأجنحة - Season Expo Kuwait</title>";
echo "<script src='https://cdn.tailwindcss.com'></script>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }";
echo "</style>";
echo "</head>";
echo "<body class='bg-gray-50 p-8'>";

echo "<div class='max-w-4xl mx-auto'>";
echo "<h1 class='text-3xl font-bold mb-8' style='color: #2C3E50;'>🎨 فحص ألوان الأجنحة</h1>";

// Check current file version
$currentFile = __DIR__ . '/exhibitions-1-simple.php';
if (file_exists($currentFile)) {
    $fileContent = file_get_contents($currentFile);
    
    echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
    echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>📄 فحص ملف exhibitions-1-simple.php</h2>";
    
    // Check for updated booth styling
    if (strpos($fileContent, 'background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)') !== false) {
        echo "<div class='bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4'>";
        echo "✅ الملف محدث محلياً - يحتوي على التصميم الجديد";
        echo "</div>";
    } else {
        echo "<div class='bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4'>";
        echo "❌ الملف لم يتم تحديثه بعد";
        echo "</div>";
    }
    
    // Check for old problematic styling
    if (strpos($fileContent, 'style="background: #2C3E50;"') !== false && 
        strpos($fileContent, 'booth') !== false) {
        echo "<div class='bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-4'>";
        echo "⚠️ تحذير: لا يزال هناك أجنحة تستخدم الخلفية الداكنة";
        echo "</div>";
    }
    
    echo "</div>";
} else {
    echo "<div class='bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4'>";
    echo "❌ ملف exhibitions-1-simple.php غير موجود";
    echo "</div>";
}

// Show correct booth styling examples
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>✨ أمثلة التصميم الصحيح</h2>";

echo "<h3 class='font-semibold mb-3' style='color: #2C3E50;'>الأجنحة المتاحة (التصميم الصحيح):</h3>";
echo "<div class='grid grid-cols-1 md:grid-cols-2 gap-4 mb-6'>";

// Example available booth
echo "<div class='border-2 rounded-lg p-4 hover:shadow-lg transition-all duration-300' style='border-color: #2C3E50; background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);'>";
echo "<div class='flex justify-between items-start mb-3'>";
echo "<h3 class='font-bold text-lg' style='color: #2C3E50;'>A001</h3>";
echo "<span class='text-white px-3 py-1 rounded-full text-sm font-semibold' style='background: #27AE60;'>متاح</span>";
echo "</div>";
echo "<p class='mb-3' style='color: #34495E;'>جناح ممتاز في موقع استراتيجي</p>";
echo "<div class='space-y-2 text-sm'>";
echo "<div style='color: #2C3E50;'><span class='font-semibold'>📏 المساحة:</span> 25 م²</div>";
echo "<div style='color: #2C3E50;'><span class='font-semibold'>📍 الموقع:</span> Hall A, Section 1</div>";
echo "<div class='font-bold text-lg' style='color: #E74C3C;'>💰 2,500 KWD</div>";
echo "</div>";
echo "<button class='block w-full mt-4 text-white py-2 px-4 rounded transition-colors text-center' style='background: #2C3E50;'>";
echo "احجز الآن";
echo "</button>";
echo "</div>";

// Example wrong booth (what we're fixing)
echo "<div class='border-2 rounded-lg p-4' style='border-color: #2C3E50; background: #2C3E50;'>";
echo "<div class='flex justify-between items-start mb-3'>";
echo "<h3 class='font-bold text-lg text-white'>A002</h3>";
echo "<span class='bg-green-500 text-white px-3 py-1 rounded-full text-sm font-semibold'>متاح</span>";
echo "</div>";
echo "<p class='mb-3 text-gray-300'>جناح ممتاز في موقع استراتيجي</p>";
echo "<div class='space-y-2 text-sm text-gray-300'>";
echo "<div>📏 المساحة: 25 م²</div>";
echo "<div>📍 الموقع: Hall A, Section 1</div>";
echo "<div class='font-bold text-lg text-red-400'>💰 2,500 KWD</div>";
echo "</div>";
echo "<button class='block w-full mt-4 text-white py-2 px-4 rounded transition-colors text-center' style='background: #1A252F;'>";
echo "احجز الآن";
echo "</button>";
echo "<div class='mt-2 text-center text-red-400 font-bold'>❌ هذا التصميم خاطئ - النصوص غير واضحة</div>";
echo "</div>";

echo "</div>";

echo "<h3 class='font-semibold mb-3' style='color: #2C3E50;'>الأجنحة المحجوزة (التصميم الصحيح):</h3>";
echo "<div class='grid grid-cols-1 md:grid-cols-2 gap-4'>";

// Example booked booth
echo "<div class='border-2 rounded-lg p-4' style='border-color: #E74C3C; background: linear-gradient(135deg, #fdf2f2 0%, #f8f8f8 100%);'>";
echo "<div class='flex justify-between items-start mb-2'>";
echo "<h3 class='font-semibold' style='color: #2C3E50;'>B001</h3>";
echo "<span class='text-white px-2 py-1 rounded-full text-xs font-semibold' style='background: #E74C3C;'>محجوز</span>";
echo "</div>";
echo "<div class='text-sm'>";
echo "<div style='color: #2C3E50;'><span class='font-semibold'>📏</span> 30 م²</div>";
echo "<div style='color: #2C3E50;'><span class='font-semibold'>📍</span> Hall B, Section 2</div>";
echo "</div>";
echo "</div>";

echo "</div>";
echo "</div>";

// Instructions
echo "<div class='bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>📋 خطوات الإصلاح</h2>";
echo "<ol class='list-decimal list-inside space-y-2'>";
echo "<li><strong>ارفع الملف المحدث:</strong> exhibitions-1-simple.php إلى السيرفر</li>";
echo "<li><strong>امسح كاش المتصفح:</strong> اضغط Ctrl+F5</li>";
echo "<li><strong>أعد تحميل الصفحة:</strong> seasonexpo.com/exhibitions-1-simple.php</li>";
echo "<li><strong>تحقق من النتيجة:</strong> يجب أن تكون جميع النصوص واضحة</li>";
echo "</ol>";
echo "</div>";

// CSS Fix for immediate testing
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>🔧 إصلاح CSS مؤقت</h2>";
echo "<p class='mb-4'>يمكنك إضافة هذا الكود في نهاية ملف exhibitions-1-simple.php كحل مؤقت:</p>";
echo "<div class='bg-gray-100 p-4 rounded-lg'>";
echo "<pre class='text-sm'>";
echo htmlspecialchars('<style>
/* إصلاح ألوان الأجنحة */
.booth-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%) !important;
    border: 2px solid #2C3E50 !important;
    color: #2C3E50 !important;
}

.booth-card h3,
.booth-card p,
.booth-card div {
    color: #2C3E50 !important;
}

.booth-card .price {
    color: #E74C3C !important;
    font-weight: bold !important;
}

.booth-card .status-available {
    background: #27AE60 !important;
    color: white !important;
}
</style>');
echo "</pre>";
echo "</div>";
echo "</div>";

// Test links
echo "<div class='text-center space-x-reverse space-x-4'>";
echo "<a href='/exhibitions-1-simple.php' class='text-white px-6 py-3 rounded-lg hover:opacity-80' style='background: #2C3E50;'>🧪 اختبار الصفحة</a>";
echo "<a href='/exhibitions-simple.php' class='bg-gray-600 text-white px-6 py-3 rounded-lg hover:bg-gray-700'>📋 جميع المعارض</a>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>

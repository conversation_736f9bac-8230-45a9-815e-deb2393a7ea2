<?php
// Homepage Content - Clean URL Version
// This file contains the homepage HTML content

// Get slider images from database
$sliderImages = [];

try {
    // Database connection
    $host = 'localhost';
    $dbname = 'season_expo_2';
    $username = 'root';
    $password = '';

    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Fetch slider images
    $stmt = $pdo->query("SELECT * FROM slider_images WHERE is_active = 1 ORDER BY sort_order ASC");
    $sliderImages = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch(PDOException $e) {
    // Fallback images if database connection fails
    $sliderImages = [
        ['image_path' => '/images/slider1.jpg', 'title' => 'Season Expo Kuwait', 'description' => 'منصة المعارض الرائدة'],
        ['image_path' => '/images/slider2.jpg', 'title' => 'احجز جناحك', 'description' => 'مساحات مميزة للعرض'],
        ['image_path' => '/images/slider3.jpg', 'title' => 'معارض متنوعة', 'description' => 'فرص استثمارية رائعة']
    ];
}
?>

<!DOCTYPE html>
<html lang="<?= $currentLang ?>" dir="<?= $currentLang === 'ar' ? 'rtl' : 'ltr' ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Season Expo Kuwait - <?= $currentLang === 'ar' ? 'منصة المعارض الرائدة في الكويت' : 'Leading Exhibition Platform in Kuwait' ?></title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
    
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
        }
        .hero-slider {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 70vh;
        }
        .slide {
            display: none;
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
        }
        .slide.active {
            display: block;
        }
        .slide-overlay {
            background: rgba(0, 0, 0, 0.4);
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg fixed w-full top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <a href="/">
                            <img src="/images/logo.png" alt="Season Expo Kuwait" style="height: 40px; width: auto;" class="hover:opacity-80 transition-opacity">
                        </a>
                    </div>
                </div>
                <div class="flex items-center space-x-reverse space-x-4">
                    <!-- Language Switcher -->
                    <div class="relative inline-block text-left">
                        <div class="group">
                            <button type="button" class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" onclick="toggleLanguageMenu()">
                                <?php if ($currentLang === 'ar'): ?>
                                    <span class="ml-2">🇰🇼</span>
                                    العربية
                                <?php else: ?>
                                    <span class="ml-2">🇺🇸</span>
                                    English
                                <?php endif; ?>
                                <svg class="mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                </svg>
                            </button>
                            <div id="languageMenu" class="absolute left-0 z-10 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 opacity-0 invisible transition-all duration-200">
                                <div class="py-1" role="menu">
                                    <a href="/switch-language.php?lang=ar" class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900" role="menuitem">
                                        <span class="ml-3">🇰🇼</span>
                                        العربية
                                    </a>
                                    <a href="/switch-language.php?lang=en" class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900" role="menuitem">
                                        <span class="ml-3">🇺🇸</span>
                                        English
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <a href="/" class="text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium"><?= $t['home'] ?></a>
                    <a href="/exhibitions" class="text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium"><?= $t['exhibitions'] ?></a>
                    <a href="/about" class="text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium"><?= $t['about'] ?></a>
                    <a href="/contact" class="text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium"><?= $t['contact'] ?></a>
                    <a href="/login-simple.php?lang=<?= $currentLang ?>" class="text-blue-600 hover:text-blue-800 px-3 py-2 rounded-md text-sm font-medium"><?= $t['login'] ?></a>
                    <a href="/register-simple?lang=<?= $currentLang ?>" class="text-white px-4 py-2 rounded-lg hover:opacity-80 transition-opacity" style="background: #2C3E50;"><?= $t['register'] ?></a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Slider -->
    <section class="hero-slider relative overflow-hidden" style="margin-top: 64px;">
        <?php foreach ($sliderImages as $index => $image): ?>
        <div class="slide <?= $index === 0 ? 'active' : '' ?>" style="background-image: url('<?= htmlspecialchars($image['image_path']) ?>');">
            <div class="slide-overlay absolute inset-0"></div>
            <div class="relative z-10 flex items-center justify-center min-h-[70vh]">
                <div class="text-center text-white px-4">
                    <h1 class="text-4xl md:text-6xl font-bold mb-6">
                        <?= $t['welcome_title'] ?>
                    </h1>
                    <p class="text-xl md:text-2xl mb-8 text-blue-100">
                        <?= $t['welcome_subtitle'] ?>
                    </p>
                    <div class="flex flex-col sm:flex-row justify-center gap-4">
                        <a href="/exhibitions?lang=<?= $currentLang ?>" class="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                            🏢 <?= $t['discover_exhibitions'] ?>
                        </a>
                        <a href="/login-simple.php?lang=<?= $currentLang ?>" class="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-gray-800 transition-colors">
                            🔑 <?= $t['login'] ?>
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <?php endforeach; ?>
        
        <!-- Slider Controls -->
        <?php if (count($sliderImages) > 1): ?>
        <div class="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
            <?php foreach ($sliderImages as $index => $image): ?>
            <button class="slider-dot w-3 h-3 rounded-full bg-white bg-opacity-50 hover:bg-opacity-75 transition-all <?= $index === 0 ? 'bg-opacity-100' : '' ?>" 
                    onclick="showSlide(<?= $index ?>)"></button>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>
    </section>

    <!-- Features Section -->
    <section class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-900 mb-4"><?= $t['upcoming_exhibitions'] ?></h2>
                <p class="text-gray-600"><?= $currentLang === 'ar' ? 'اكتشف أحدث المعارض والفعاليات' : 'Discover the latest exhibitions and events' ?></p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="text-center p-6 rounded-lg border border-gray-200 hover:shadow-lg transition-shadow">
                    <div class="text-4xl mb-4">🏢</div>
                    <h3 class="text-xl font-semibold mb-2"><?= $currentLang === 'ar' ? 'معارض متنوعة' : 'Diverse Exhibitions' ?></h3>
                    <p class="text-gray-600"><?= $currentLang === 'ar' ? 'مجموعة واسعة من المعارض في مختلف القطاعات' : 'Wide range of exhibitions across various sectors' ?></p>
                </div>
                
                <div class="text-center p-6 rounded-lg border border-gray-200 hover:shadow-lg transition-shadow">
                    <div class="text-4xl mb-4">📍</div>
                    <h3 class="text-xl font-semibold mb-2"><?= $currentLang === 'ar' ? 'مواقع مميزة' : 'Prime Locations' ?></h3>
                    <p class="text-gray-600"><?= $currentLang === 'ar' ? 'أجنحة في أفضل المواقع لضمان أقصى استفادة' : 'Booths in the best locations for maximum benefit' ?></p>
                </div>
                
                <div class="text-center p-6 rounded-lg border border-gray-200 hover:shadow-lg transition-shadow">
                    <div class="text-4xl mb-4">🤝</div>
                    <h3 class="text-xl font-semibold mb-2"><?= $currentLang === 'ar' ? 'دعم شامل' : 'Full Support' ?></h3>
                    <p class="text-gray-600"><?= $currentLang === 'ar' ? 'فريق متخصص لمساعدتك في كل خطوة' : 'Specialized team to help you every step of the way' ?></p>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <img src="/images/logo.png" alt="Season Expo Kuwait" class="h-12 mx-auto mb-4">
                <p class="text-gray-300 mb-4">
                    <?= $currentLang === 'ar' ? 'منصة المعارض الرائدة في الكويت' : 'Leading Exhibition Platform in Kuwait' ?>
                </p>
                <div class="flex justify-center space-x-reverse space-x-6">
                    <a href="/about" class="text-gray-300 hover:text-white"><?= $t['about'] ?></a>
                    <a href="/contact" class="text-gray-300 hover:text-white"><?= $t['contact'] ?></a>
                    <a href="/exhibitions" class="text-gray-300 hover:text-white"><?= $t['exhibitions'] ?></a>
                </div>
                <p class="text-gray-400 text-sm mt-6">
                    © 2024 Season Expo Kuwait. <?= $currentLang === 'ar' ? 'جميع الحقوق محفوظة' : 'All rights reserved' ?>.
                </p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script>
        let currentSlide = 0;
        const slides = document.querySelectorAll('.slide');
        const dots = document.querySelectorAll('.slider-dot');

        function showSlide(index) {
            slides[currentSlide].classList.remove('active');
            dots[currentSlide].classList.remove('bg-opacity-100');
            dots[currentSlide].classList.add('bg-opacity-50');
            
            currentSlide = index;
            
            slides[currentSlide].classList.add('active');
            dots[currentSlide].classList.remove('bg-opacity-50');
            dots[currentSlide].classList.add('bg-opacity-100');
        }

        function nextSlide() {
            const nextIndex = (currentSlide + 1) % slides.length;
            showSlide(nextIndex);
        }

        // Auto-advance slides
        if (slides.length > 1) {
            setInterval(nextSlide, 5000);
        }

        // Language Switcher Functions
        function toggleLanguageMenu() {
            const menu = document.getElementById('languageMenu');
            const isVisible = menu.classList.contains('opacity-100');
            
            if (isVisible) {
                menu.classList.remove('opacity-100', 'visible');
                menu.classList.add('opacity-0', 'invisible');
            } else {
                menu.classList.remove('opacity-0', 'invisible');
                menu.classList.add('opacity-100', 'visible');
            }
        }

        // Close menu when clicking outside
        document.addEventListener('click', function(event) {
            const menu = document.getElementById('languageMenu');
            const button = event.target.closest('button');
            
            if (!button || button.getAttribute('onclick') !== 'toggleLanguageMenu()') {
                menu.classList.remove('opacity-100', 'visible');
                menu.classList.add('opacity-0', 'invisible');
            }
        });
    </script>
</body>
</html>

<?php
// Simple Booking Process
// This file handles booth booking

session_start();

// Get booth and exhibition info from URL parameters
$boothId = $_GET['booth_id'] ?? null;
$exhibitionId = $_GET['exhibition_id'] ?? null;

if (!$boothId || !$exhibitionId) {
    header('Location: /exhibitions');
    exit;
}

// Check if user is logged in
$isLoggedIn = isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);

if (!$isLoggedIn) {
    // Redirect to login with return URL
    $returnUrl = $_SERVER['REQUEST_URI'];
    header('Location: /auth/login.php?redirect=' . urlencode($returnUrl));
    exit;
}

// Get language
$currentLang = $_SESSION['language'] ?? $_COOKIE['language'] ?? $_GET['lang'] ?? 'ar';

// Include database configuration
require_once '../config/database.php';

try {
    $pdo = getDatabaseConnection();
    
    // Get exhibition details
    $stmt = $pdo->prepare("SELECT * FROM exhibitions WHERE id = ? AND status = 'published'");
    $stmt->execute([$exhibitionId]);
    $exhibition = $stmt->fetch(PDO::FETCH_OBJ);
    
    if (!$exhibition) {
        header('Location: /exhibitions');
        exit;
    }
    
    // Get booth details
    $stmt = $pdo->prepare("SELECT * FROM booths WHERE id = ? AND exhibition_id = ? AND status = 'available'");
    $stmt->execute([$boothId, $exhibitionId]);
    $booth = $stmt->fetch(PDO::FETCH_OBJ);
    
    if (!$booth) {
        header('Location: /exhibitions/details.php?id=' . $exhibitionId);
        exit;
    }
    
} catch (Exception $e) {
    echo "خطأ في قاعدة البيانات: " . $e->getMessage();
    exit;
}

// Handle form submission
if ($_POST && isset($_POST['book_booth'])) {
    try {
        // Start transaction
        $pdo->beginTransaction();
        
        // Update booth status to booked
        $stmt = $pdo->prepare("UPDATE booths SET status = 'booked', booked_by = ?, booked_at = NOW() WHERE id = ?");
        $stmt->execute([$_SESSION['user_id'], $boothId]);
        
        $pdo->commit();
        
        $bookingSuccess = true;
        
    } catch (Exception $e) {
        $pdo->rollback();
        $bookingError = "حدث خطأ أثناء الحجز: " . $e->getMessage();
    }
}

// Format dates
$startDate = new DateTime($exhibition->start_date);
$endDate = new DateTime($exhibition->end_date);
?>

<!DOCTYPE html>
<html lang="<?= $currentLang ?>" dir="<?= $currentLang === 'ar' ? 'rtl' : 'ltr' ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>حجز الجناح - <?= htmlspecialchars($exhibition->title) ?></title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg fixed w-full top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="/">
                        <img src="/images/logo.png" alt="Season Expo Kuwait" style="height: 40px; width: auto;" class="hover:opacity-80 transition-opacity">
                    </a>
                </div>
                <div class="flex items-center space-x-reverse space-x-4">
                    <a href="/" class="text-gray-600 hover:text-gray-900">الصفحة الرئيسية</a>
                    <a href="/exhibitions" class="text-gray-600 hover:text-gray-900">المعارض</a>
                    <a href="/admin/dashboard.php" class="text-blue-600 font-semibold">حسابي</a>
                    <a href="/auth/logout.php" class="text-red-600 hover:text-red-900">تسجيل الخروج</a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="pt-16">
        <div class="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
            
            <?php if (isset($bookingSuccess) && $bookingSuccess): ?>
            <!-- Success Message -->
            <div class="bg-green-50 border border-green-200 rounded-lg p-6 mb-8">
                <div class="flex items-center">
                    <div class="text-green-600 text-2xl mr-4">✅</div>
                    <div>
                        <h2 class="text-xl font-bold text-green-800">تم الحجز بنجاح!</h2>
                        <p class="text-green-700">تم حجز الجناح بنجاح. سيتم التواصل معك قريباً لإتمام الإجراءات.</p>
                    </div>
                </div>
                <div class="mt-4">
                    <a href="/exhibitions/details.php?id=<?= $exhibitionId ?>" class="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700">
                        العودة للمعرض
                    </a>
                </div>
            </div>
            <?php else: ?>
            
            <!-- Booking Form -->
            <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                <div class="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6">
                    <h1 class="text-3xl font-bold">حجز الجناح</h1>
                    <p class="text-blue-100 mt-2">إتمام حجز الجناح في المعرض</p>
                </div>
                
                <div class="p-6">
                    <!-- Exhibition Info -->
                    <div class="mb-8">
                        <h2 class="text-xl font-bold text-gray-900 mb-4">معلومات المعرض</h2>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <h3 class="font-semibold text-gray-700">اسم المعرض:</h3>
                                    <p class="text-gray-900"><?= htmlspecialchars($exhibition->title) ?></p>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-gray-700">التاريخ:</h3>
                                    <p class="text-gray-900"><?= $startDate->format('d/m/Y') ?> - <?= $endDate->format('d/m/Y') ?></p>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-gray-700">المكان:</h3>
                                    <p class="text-gray-900"><?= htmlspecialchars($exhibition->venue_name) ?></p>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-gray-700">المدينة:</h3>
                                    <p class="text-gray-900"><?= htmlspecialchars($exhibition->city) ?></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Booth Info -->
                    <div class="mb-8">
                        <h2 class="text-xl font-bold text-gray-900 mb-4">معلومات الجناح</h2>
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <h3 class="font-semibold text-blue-700">رقم الجناح:</h3>
                                    <p class="text-blue-900 text-lg font-bold"><?= htmlspecialchars($booth->name ?? $booth->booth_number) ?></p>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-blue-700">المساحة:</h3>
                                    <p class="text-blue-900"><?= $booth->area ?> متر مربع</p>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-blue-700">الموقع:</h3>
                                    <p class="text-blue-900"><?= htmlspecialchars($booth->location) ?></p>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-blue-700">السعر:</h3>
                                    <p class="text-blue-900 text-xl font-bold"><?= number_format($booth->price) ?> <?= $exhibition->currency ?></p>
                                </div>
                            </div>
                            <?php if ($booth->description): ?>
                            <div class="mt-4">
                                <h3 class="font-semibold text-blue-700">الوصف:</h3>
                                <p class="text-blue-900"><?= htmlspecialchars($booth->description) ?></p>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- User Agreement -->
                    <div class="mb-8">
                        <h2 class="text-xl font-bold text-gray-900 mb-4">الإقرار والتعهد</h2>
                        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                            <div class="space-y-3 text-sm">
                                <p><strong>أقر وأتعهد بما يلي:</strong></p>
                                <ul class="list-disc list-inside space-y-2 mr-4">
                                    <li>الالتزام بجميع شروط وأحكام المعرض</li>
                                    <li>دفع كامل المبلغ المستحق في المواعيد المحددة</li>
                                    <li>الالتزام بالمواصفات والمعايير المطلوبة للجناح</li>
                                    <li>عدم التنازل عن الجناح لطرف ثالث دون موافقة الإدارة</li>
                                    <li>الالتزام بقوانين وأنظمة دولة الكويت</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Error Message -->
                    <?php if (isset($bookingError)): ?>
                    <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                        <div class="flex items-center">
                            <div class="text-red-600 text-xl mr-3">❌</div>
                            <div>
                                <h3 class="font-semibold text-red-800">خطأ في الحجز</h3>
                                <p class="text-red-700"><?= $bookingError ?></p>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- Booking Form -->
                    <form method="POST" class="space-y-6">
                        <div class="flex items-center">
                            <input type="checkbox" id="agree_terms" name="agree_terms" required class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label for="agree_terms" class="mr-2 block text-sm text-gray-900">
                                أوافق على جميع الشروط والأحكام والإقرار والتعهد المذكور أعلاه
                            </label>
                        </div>
                        
                        <div class="flex flex-col sm:flex-row gap-4">
                            <button type="submit" name="book_booth" class="flex-1 bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition-colors font-semibold">
                                تأكيد الحجز
                            </button>
                            <a href="/exhibitions/details.php?id=<?= $exhibitionId ?>" class="flex-1 bg-gray-600 text-white py-3 px-6 rounded-lg hover:bg-gray-700 transition-colors font-semibold text-center">
                                إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
            
            <?php endif; ?>
        </div>
    </div>
</body>
</html>

<?php
// Check Language Setup for Hostinger Server
// This file verifies that language switching is properly configured

session_start();

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='utf-8'>";
echo "<title>فحص إعداد اللغة - Season Expo Kuwait</title>";
echo "<script src='https://cdn.tailwindcss.com'></script>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }";
echo "</style>";
echo "</head>";
echo "<body class='bg-gray-50 p-8'>";

echo "<div class='max-w-4xl mx-auto'>";
echo "<h1 class='text-3xl font-bold mb-8' style='color: #2C3E50;'>🔍 فحص إعداد اللغة</h1>";

// Current language status
$currentLang = $_SESSION['language'] ?? $_COOKIE['language'] ?? 'ar';
$langFromUrl = $_GET['lang'] ?? null;

echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>📊 حالة اللغة الحالية</h2>";

echo "<div class='grid grid-cols-1 md:grid-cols-2 gap-4 mb-6'>";

echo "<div class='bg-blue-50 border border-blue-200 rounded-lg p-4'>";
echo "<h3 class='font-semibold mb-2'>اللغة المحفوظة:</h3>";
echo "<div class='space-y-2'>";
echo "<div>Session: " . (isset($_SESSION['language']) ? "<span class='text-green-600'>✅ " . $_SESSION['language'] . "</span>" : "<span class='text-red-600'>❌ غير محددة</span>") . "</div>";
echo "<div>Cookie: " . (isset($_COOKIE['language']) ? "<span class='text-green-600'>✅ " . $_COOKIE['language'] . "</span>" : "<span class='text-red-600'>❌ غير محددة</span>") . "</div>";
echo "<div>URL: " . ($langFromUrl ? "<span class='text-green-600'>✅ " . $langFromUrl . "</span>" : "<span class='text-gray-600'>➖ غير محددة</span>") . "</div>";
echo "</div>";
echo "</div>";

echo "<div class='bg-green-50 border border-green-200 rounded-lg p-4'>";
echo "<h3 class='font-semibold mb-2'>اللغة النشطة:</h3>";
echo "<div class='text-2xl text-center'>";
if ($currentLang === 'ar') {
    echo "<span class='text-green-600'>🇰🇼 العربية</span>";
} else {
    echo "<span class='text-blue-600'>🇺🇸 English</span>";
}
echo "</div>";
echo "</div>";

echo "</div>";
echo "</div>";

// Check language files
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>📁 فحص ملفات اللغة</h2>";

$languageFiles = [
    'lang/ar.php' => 'معالج اللغة العربية',
    'lang/en.php' => 'معالج اللغة الإنجليزية'
];

echo "<div class='grid grid-cols-1 md:grid-cols-2 gap-4'>";

foreach ($languageFiles as $file => $description) {
    $fullPath = __DIR__ . '/' . $file;
    echo "<div class='border border-gray-200 rounded-lg p-4'>";
    echo "<h3 class='font-semibold mb-2'>{$description}</h3>";
    echo "<p class='text-sm text-gray-600 mb-2'>/{$file}</p>";
    
    if (file_exists($fullPath)) {
        $size = filesize($fullPath);
        $lastModified = date('Y-m-d H:i:s', filemtime($fullPath));
        
        echo "<div class='text-green-600 mb-2'>✅ موجود</div>";
        echo "<div class='text-xs text-gray-500'>";
        echo "الحجم: " . round($size/1024, 2) . " KB<br>";
        echo "آخر تعديل: {$lastModified}";
        echo "</div>";
        
        // Test the file
        echo "<div class='mt-2'>";
        echo "<a href='/{$file}' class='text-blue-600 hover:underline text-sm'>🧪 اختبار الملف</a>";
        echo "</div>";
        
    } else {
        echo "<div class='text-red-600'>❌ مفقود</div>";
        echo "<div class='text-sm text-red-500 mt-2'>يجب إنشاء هذا الملف</div>";
    }
    echo "</div>";
}

echo "</div>";
echo "</div>";

// Check homepage files for language button
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>🏠 فحص زر اللغة في الصفحات</h2>";

$homepageFiles = [
    'homepage-fixed.php' => 'الصفحة الرئيسية PHP',
    'resources/views/homepage.blade.php' => 'الصفحة الرئيسية Blade'
];

echo "<div class='space-y-4'>";

foreach ($homepageFiles as $file => $description) {
    $fullPath = __DIR__ . '/' . $file;
    echo "<div class='border border-gray-200 rounded-lg p-4'>";
    echo "<h3 class='font-semibold mb-2'>{$description}</h3>";
    
    if (file_exists($fullPath)) {
        $content = file_get_contents($fullPath);
        
        echo "<div class='grid grid-cols-1 md:grid-cols-3 gap-2 text-sm'>";
        
        // Check for language button
        if (strpos($content, 'toggleLanguageMenu') !== false) {
            echo "<div class='text-green-600'>✅ زر اللغة موجود</div>";
        } else {
            echo "<div class='text-red-600'>❌ زر اللغة مفقود</div>";
        }
        
        // Check for correct paths
        if (strpos($content, '/lang/ar') !== false && strpos($content, '/lang/en') !== false) {
            echo "<div class='text-green-600'>✅ مسارات صحيحة</div>";
        } else {
            echo "<div class='text-red-600'>❌ مسارات خاطئة</div>";
        }
        
        // Check for JavaScript
        if (strpos($content, 'languageMenu') !== false) {
            echo "<div class='text-green-600'>✅ JavaScript موجود</div>";
        } else {
            echo "<div class='text-red-600'>❌ JavaScript مفقود</div>";
        }
        
        echo "</div>";
        
    } else {
        echo "<div class='text-red-600'>❌ الملف غير موجود</div>";
    }
    echo "</div>";
}

echo "</div>";
echo "</div>";

// Quick test section
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>🧪 اختبار سريع</h2>";

echo "<div class='text-center mb-6'>";
echo "<p class='mb-4'>اختبر تغيير اللغة باستخدام الأزرار التالية:</p>";

echo "<div class='flex items-center justify-center space-x-reverse space-x-4'>";

// Arabic button
echo "<a href='/lang/ar.php' class='inline-flex items-center px-6 py-3 border-2 rounded-lg font-semibold transition-colors' style='border-color: #2C3E50; color: #2C3E50;' onmouseover='this.style.background=\"#2C3E50\"; this.style.color=\"white\"' onmouseout='this.style.background=\"transparent\"; this.style.color=\"#2C3E50\"'>";
echo "<span class='ml-2'>🇰🇼</span>";
echo "العربية";
echo "</a>";

// English button
echo "<a href='/lang/en.php' class='inline-flex items-center px-6 py-3 border-2 rounded-lg font-semibold transition-colors' style='border-color: #2C3E50; color: #2C3E50;' onmouseover='this.style.background=\"#2C3E50\"; this.style.color=\"white\"' onmouseout='this.style.background=\"transparent\"; this.style.color=\"#2C3E50\"'>";
echo "<span class='ml-2'>🇺🇸</span>";
echo "English";
echo "</a>";

echo "</div>";
echo "</div>";

// Results after test
if ($langFromUrl) {
    echo "<div class='bg-green-50 border border-green-200 rounded-lg p-4'>";
    echo "<h3 class='font-semibold mb-2'>✅ نتيجة الاختبار:</h3>";
    echo "<p>تم تغيير اللغة إلى: <strong>" . ($langFromUrl === 'ar' ? 'العربية 🇰🇼' : 'English 🇺🇸') . "</strong></p>";
    echo "<p class='text-sm text-gray-600 mt-2'>إذا كنت ترى هذه الرسالة، فإن نظام تغيير اللغة يعمل بشكل صحيح!</p>";
    echo "</div>";
}

echo "</div>";

// Instructions
echo "<div class='bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>📋 تعليمات التطبيق</h2>";

echo "<div class='space-y-4'>";

echo "<div>";
echo "<h3 class='font-semibold mb-2'>1. ارفع الملفات التالية إلى Hostinger:</h3>";
echo "<ul class='text-sm space-y-1 list-disc list-inside ml-4'>";
echo "<li><code>lang/ar.php</code> - معالج اللغة العربية</li>";
echo "<li><code>lang/en.php</code> - معالج اللغة الإنجليزية</li>";
echo "<li><code>homepage-fixed.php</code> - الصفحة الرئيسية المحدثة</li>";
echo "<li><code>resources/views/homepage.blade.php</code> - إذا كنت تستخدم Laravel</li>";
echo "</ul>";
echo "</div>";

echo "<div>";
echo "<h3 class='font-semibold mb-2'>2. اختبر الوظيفة:</h3>";
echo "<ul class='text-sm space-y-1 list-disc list-inside ml-4'>";
echo "<li>اذهب إلى الصفحة الرئيسية</li>";
echo "<li>ابحث عن زر اللغة في أعلى الصفحة</li>";
echo "<li>اضغط عليه واختر اللغة</li>";
echo "<li>تحقق من حفظ الإعداد</li>";
echo "</ul>";
echo "</div>";

echo "<div>";
echo "<h3 class='font-semibold mb-2'>3. في حالة وجود مشاكل:</h3>";
echo "<ul class='text-sm space-y-1 list-disc list-inside ml-4'>";
echo "<li>تأكد من رفع جميع الملفات</li>";
echo "<li>تحقق من صلاحيات الملفات (755)</li>";
echo "<li>امسح كاش المتصفح</li>";
echo "<li>تحقق من وجود أخطاء في Console (F12)</li>";
echo "</ul>";
echo "</div>";

echo "</div>";
echo "</div>";

// Navigation
echo "<div class='text-center space-x-reverse space-x-4'>";
echo "<a href='/homepage-fixed.php' class='text-white px-6 py-3 rounded-lg hover:opacity-80' style='background: #2C3E50;'>🏠 الصفحة الرئيسية</a>";
echo "<a href='/test-language-switcher.php' class='bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700'>🧪 اختبار شامل</a>";
echo "<a href='/admin-slider-management.php' class='bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700'>🖼️ إدارة السلايد</a>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>

<?php
// Organize Files Now - Immediate File Organization
// This script helps organize files into proper folders

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='utf-8'>";
echo "<title>تنظيم الملفات الآن - Season Expo Kuwait</title>";
echo "<script src='https://cdn.tailwindcss.com'></script>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }";
echo "</style>";
echo "</head>";
echo "<body class='bg-gray-50 p-8'>";

echo "<div class='max-w-4xl mx-auto'>";
echo "<h1 class='text-3xl font-bold mb-8' style='color: #2C3E50;'>📁 تنظيم الملفات الآن</h1>";

// File organization plan
$fileOrganization = [
    'admin' => [
        'description' => 'ملفات الإدارة',
        'files' => [
            'dashboard.php' => 'admin/dashboard.php',
            'admin-slider-management.php' => 'admin/slider-management.php',
            'admin-exhibitions.php' => 'admin/exhibitions.php'
        ]
    ],
    'auth' => [
        'description' => 'ملفات المصادقة',
        'files' => [
            'login-simple.php' => 'auth/login.php',
            'logout.php' => 'auth/logout.php',
            'register-simple.php' => 'auth/register.php'
        ]
    ],
    'exhibitions' => [
        'description' => 'ملفات المعارض',
        'files' => [
            'exhibitions-simple.php' => 'exhibitions/index.php',
            'exhibition-details.php' => 'exhibitions/details.php'
        ]
    ],
    'booking' => [
        'description' => 'ملفات الحجز',
        'files' => [
            'booking-simple.php' => 'booking/create.php',
            'booking-with-signature.php' => 'booking/signature.php'
        ]
    ],
    'config' => [
        'description' => 'ملفات الإعدادات',
        'files' => [
            'config-database.php' => 'config/database.php'
        ]
    ],
    'test' => [
        'description' => 'ملفات الاختبار',
        'files' => [
            'test-booking-system.php' => 'test/booking-system.php',
            'test-dashboard-system.php' => 'test/dashboard-system.php',
            'test-complete-system.php' => 'test/complete-system.php',
            'test-exhibitions-working.php' => 'test/exhibitions-working.php',
            'final-test-complete.php' => 'test/final-complete.php'
        ]
    ]
];

// Show organization plan
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>📋 خطة التنظيم</h2>";

foreach ($fileOrganization as $folder => $info) {
    echo "<div class='mb-6'>";
    echo "<h3 class='font-semibold text-lg mb-3 text-blue-700'>📁 {$folder}/ - {$info['description']}</h3>";
    echo "<div class='bg-gray-50 rounded-lg p-4'>";
    
    foreach ($info['files'] as $currentFile => $newPath) {
        $exists = file_exists($currentFile) ? '✅' : '❌';
        echo "<div class='flex items-center justify-between py-2 border-b border-gray-200 last:border-b-0'>";
        echo "<div class='flex items-center'>";
        echo "<span class='mr-2'>{$exists}</span>";
        echo "<span class='text-sm'>{$currentFile}</span>";
        echo "</div>";
        echo "<div class='text-sm text-gray-600'>→ {$newPath}</div>";
        echo "</div>";
    }
    
    echo "</div>";
    echo "</div>";
}

echo "</div>";

// Updated .htaccess rules
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>⚙️ قواعد .htaccess المحدثة</h2>";

echo "<div class='bg-gray-900 text-green-400 p-4 rounded-lg text-sm font-mono'>";
echo "<pre>";
echo "# Season Expo Kuwait - Organized File Structure\n";
echo "RewriteEngine On\n\n";

echo "# Language routing\n";
echo "RewriteRule ^en/?$ /homepage-fixed.php?lang=en [L]\n";
echo "RewriteRule ^ar/?$ /homepage-fixed.php?lang=ar [L]\n\n";

echo "# Admin routes\n";
echo "RewriteRule ^admin/?$ /admin/dashboard.php [L]\n";
echo "RewriteRule ^admin/dashboard/?$ /admin/dashboard.php [L]\n";
echo "RewriteRule ^admin/slider/?$ /admin/slider-management.php [L]\n";
echo "RewriteRule ^حسابي/?$ /admin/dashboard.php [L]\n\n";

echo "# Auth routes\n";
echo "RewriteRule ^login/?$ /auth/login.php [L]\n";
echo "RewriteRule ^logout/?$ /auth/logout.php [L]\n";
echo "RewriteRule ^register/?$ /auth/register.php [L]\n\n";

echo "# Exhibition routes\n";
echo "RewriteRule ^exhibitions/?$ /exhibitions/index.php [L]\n";
echo "RewriteRule ^exhibitions/([0-9]+)/?$ /exhibitions/details.php?id=$1 [L]\n\n";

echo "# Booking routes\n";
echo "RewriteRule ^booking/?$ /booking/create.php [L]\n";
echo "RewriteRule ^booking/([0-9]+)/([0-9]+)/?$ /booking/create.php?booth_id=$1&exhibition_id=$2 [L]\n\n";

echo "# Legacy redirects\n";
echo "RewriteRule ^dashboard/?$ /admin/dashboard.php [R=301,L]\n";
echo "RewriteRule ^login-simple/?$ /auth/login.php [R=301,L]\n";
echo "RewriteRule ^exhibitions-simple/?$ /exhibitions/index.php [R=301,L]";
echo "</pre>";
echo "</div>";
echo "</div>";

// File creation commands
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>🛠️ أوامر إنشاء المجلدات</h2>";

echo "<div class='space-y-4'>";

// Create folders
echo "<div class='bg-blue-50 border border-blue-200 rounded-lg p-4'>";
echo "<h3 class='font-semibold text-blue-700 mb-2'>1. إنشاء المجلدات:</h3>";
echo "<div class='bg-gray-900 text-green-400 p-3 rounded text-sm font-mono'>";
echo "mkdir admin auth exhibitions booking config test";
echo "</div>";
echo "</div>";

// Move files
echo "<div class='bg-green-50 border border-green-200 rounded-lg p-4'>";
echo "<h3 class='font-semibold text-green-700 mb-2'>2. نقل الملفات:</h3>";
echo "<div class='bg-gray-900 text-green-400 p-3 rounded text-sm font-mono space-y-1'>";
echo "<div>mv dashboard.php admin/dashboard.php</div>";
echo "<div>mv login-simple.php auth/login.php</div>";
echo "<div>mv logout.php auth/logout.php</div>";
echo "<div>mv exhibitions-simple.php exhibitions/index.php</div>";
echo "<div>mv exhibition-details.php exhibitions/details.php</div>";
echo "<div>mv booking-simple.php booking/create.php</div>";
echo "<div>mv config-database.php config/database.php</div>";
echo "<div>mv test-*.php test/</div>";
echo "</div>";
echo "</div>";

echo "</div>";
echo "</div>";

// Benefits
echo "<div class='bg-green-50 border border-green-200 rounded-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold text-green-700 mb-4'>✅ فوائد التنظيم</h2>";

echo "<div class='grid grid-cols-1 md:grid-cols-2 gap-4'>";

echo "<div class='bg-white p-4 rounded border'>";
echo "<h3 class='font-semibold mb-2'>🧹 تنظيم أفضل</h3>";
echo "<ul class='text-sm space-y-1 list-disc list-inside'>";
echo "<li>ملفات مجمعة حسب الوظيفة</li>";
echo "<li>سهولة في العثور على الملفات</li>";
echo "<li>صيانة أسهل</li>";
echo "</ul>";
echo "</div>";

echo "<div class='bg-white p-4 rounded border'>";
echo "<h3 class='font-semibold mb-2'>🔒 أمان أفضل</h3>";
echo "<ul class='text-sm space-y-1 list-disc list-inside'>";
echo "<li>فصل ملفات الإدارة</li>";
echo "<li>حماية ملفات الإعدادات</li>";
echo "<li>تحكم أفضل في الوصول</li>";
echo "</ul>";
echo "</div>";

echo "<div class='bg-white p-4 rounded border'>";
echo "<h3 class='font-semibold mb-2'>🚀 أداء أفضل</h3>";
echo "<ul class='text-sm space-y-1 list-disc list-inside'>";
echo "<li>تحميل أسرع للصفحات</li>";
echo "<li>تنظيم أفضل للذاكرة</li>";
echo "<li>كاش أكثر فعالية</li>";
echo "</ul>";
echo "</div>";

echo "<div class='bg-white p-4 rounded border'>";
echo "<h3 class='font-semibold mb-2'>👥 تطوير أفضل</h3>";
echo "<ul class='text-sm space-y-1 list-disc list-inside'>";
echo "<li>فهم أسرع للكود</li>";
echo "<li>تعاون أفضل بين المطورين</li>";
echo "<li>إضافة مميزات أسهل</li>";
echo "</ul>";
echo "</div>";

echo "</div>";
echo "</div>";

// Implementation steps
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>📝 خطوات التنفيذ</h2>";

echo "<div class='space-y-4'>";

echo "<div class='flex items-start'>";
echo "<div class='bg-blue-600 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-4 mt-1'>1</div>";
echo "<div>";
echo "<h3 class='font-semibold mb-2'>إنشاء المجلدات على السيرفر</h3>";
echo "<p class='text-sm text-gray-600'>استخدم File Manager في Hostinger لإنشاء المجلدات المطلوبة</p>";
echo "</div>";
echo "</div>";

echo "<div class='flex items-start'>";
echo "<div class='bg-blue-600 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-4 mt-1'>2</div>";
echo "<div>";
echo "<h3 class='font-semibold mb-2'>نقل الملفات الموجودة</h3>";
echo "<p class='text-sm text-gray-600'>انقل الملفات من الجذر إلى المجلدات المناسبة</p>";
echo "</div>";
echo "</div>";

echo "<div class='flex items-start'>";
echo "<div class='bg-blue-600 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-4 mt-1'>3</div>";
echo "<div>";
echo "<h3 class='font-semibold mb-2'>تحديث ملف .htaccess</h3>";
echo "<p class='text-sm text-gray-600'>أضف القواعد الجديدة لإعادة التوجيه</p>";
echo "</div>";
echo "</div>";

echo "<div class='flex items-start'>";
echo "<div class='bg-blue-600 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-4 mt-1'>4</div>";
echo "<div>";
echo "<h3 class='font-semibold mb-2'>تحديث الروابط الداخلية</h3>";
echo "<p class='text-sm text-gray-600'>تحديث الروابط في الملفات لتشير للمسارات الجديدة</p>";
echo "</div>";
echo "</div>";

echo "<div class='flex items-start'>";
echo "<div class='bg-blue-600 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-4 mt-1'>5</div>";
echo "<div>";
echo "<h3 class='font-semibold mb-2'>اختبار جميع الوظائف</h3>";
echo "<p class='text-sm text-gray-600'>تأكد من أن جميع الصفحات والوظائف تعمل بشكل صحيح</p>";
echo "</div>";
echo "</div>";

echo "</div>";
echo "</div>";

// Quick action
echo "<div class='bg-blue-50 border border-blue-200 rounded-lg p-6 text-center'>";
echo "<h2 class='text-xl font-bold text-blue-700 mb-4'>🎯 هل تريد تنفيذ التنظيم الآن؟</h2>";
echo "<p class='text-blue-600 mb-4'>يمكنني إنشاء الملفات المنظمة مع التحديثات المطلوبة</p>";
echo "<div class='space-x-reverse space-x-4'>";
echo "<button onclick='alert(\"سأقوم بإنشاء الملفات المنظمة الآن!\")' class='bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700'>✅ نعم، نظم الملفات</button>";
echo "<a href='/file-organization-guide.php' class='bg-gray-600 text-white px-6 py-3 rounded-lg hover:bg-gray-700'>📖 مراجعة الدليل</a>";
echo "</div>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>

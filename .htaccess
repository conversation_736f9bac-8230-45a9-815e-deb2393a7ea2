# Season Expo Kuwait - Clean URLs Configuration
# This file enables clean URLs and proper language routing

RewriteEngine On

# Force HTTPS (optional - uncomment if you have SSL)
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Remove trailing slashes
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)/$ /$1 [R=301,L]

# Language routing - Clean URLs
# /ar -> Arabic homepage
RewriteRule ^ar/?$ /switch-language.php?lang=ar [L]

# /en -> English homepage  
RewriteRule ^en/?$ /switch-language.php?lang=en [L]

# /ar/login -> Arabic login
RewriteRule ^ar/login/?$ /login-simple.php?lang=ar [L]

# /en/login -> English login
RewriteRule ^en/login/?$ /login-simple.php?lang=en [L]

# /ar/exhibitions -> Arabic exhibitions
RewriteRule ^ar/exhibitions/?$ /exhibitions-simple.php?lang=ar [L]

# /en/exhibitions -> English exhibitions
RewriteRule ^en/exhibitions/?$ /exhibitions-simple.php?lang=en [L]

# Hide .php extension for all files
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_FILENAME} !-f
RewriteRule ^([^\.]+)$ $1.php [NC,L]

# Redirect old URLs to clean URLs
RewriteCond %{THE_REQUEST} /homepage-fixed\.php\?lang=([^&\s]+) [NC]
RewriteRule ^homepage-fixed\.php$ /%1? [R=301,L]

RewriteCond %{THE_REQUEST} /login-simple\.php\?lang=([^&\s]+) [NC]
RewriteRule ^login-simple\.php$ /%1/login? [R=301,L]

# Default language handling
# If no language specified, detect from browser or default to Arabic
RewriteCond %{REQUEST_URI} ^/$
RewriteCond %{HTTP_ACCEPT_LANGUAGE} ^en [NC]
RewriteRule ^$ /en? [R=302,L]

# Security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>

# Compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Cache control
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
</IfModule>

# Error pages
ErrorDocument 404 /404.php
ErrorDocument 500 /500.php

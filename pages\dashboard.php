<?php
// User Dashboard - "حسابي"
// This file displays user account information and bookings

session_start();

// Check if user is logged in
if (!isset($_SESSION['user_id']) || empty($_SESSION['user_id'])) {
    header('Location: /pages/login.php?redirect=' . urlencode($_SERVER['REQUEST_URI']));
    exit;
}

// Get language
$currentLang = $_SESSION['language'] ?? $_COOKIE['language'] ?? $_GET['lang'] ?? 'ar';

// Include database configuration
require_once '../includes/database.php';

try {
    $pdo = getDatabaseConnection();

    // Get user bookings - try bookings table first, then booths table
    $userBookings = [];

    try {
        // Try to get from bookings table
        $stmt = $pdo->prepare("
            SELECT
                bk.id as booking_id,
                b.id as booth_id,
                b.name as booth_name,
                b.price,
                b.area,
                bk.created_at as booked_at,
                e.id as exhibition_id,
                e.title as exhibition_title,
                e.start_date,
                e.end_date,
                e.venue_name
            FROM bookings bk
            JOIN booths b ON bk.booth_id = b.id
            JOIN exhibitions e ON bk.exhibition_id = e.id
            WHERE bk.user_id = ?
            ORDER BY bk.created_at DESC
        ");
        $stmt->execute([$_SESSION['user_id']]);
        $userBookings = $stmt->fetchAll(PDO::FETCH_OBJ);
    } catch (Exception $e) {
        // If bookings table doesn't exist, show message
        $userBookings = [];
    }

} catch (Exception $e) {
    $dbError = "خطأ في قاعدة البيانات: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="<?= $currentLang ?>" dir="<?= $currentLang === 'ar' ? 'rtl' : 'ltr' ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>حسابي - Season Expo Kuwait</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">

    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg fixed w-full top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="/">
                        <img src="/images/logo.png" alt="Season Expo Kuwait" style="height: 40px; width: auto;" class="hover:opacity-80 transition-opacity">
                    </a>
                </div>
                <div class="flex items-center space-x-reverse space-x-4">
                    <a href="/" class="text-gray-600 hover:text-gray-900">الصفحة الرئيسية</a>
                    <a href="/exhibitions" class="text-gray-600 hover:text-gray-900">المعارض</a>
                    <a href="/dashboard" class="text-blue-600 font-semibold">حسابي</a>
                    <a href="/logout" class="text-red-600 hover:text-red-900">تسجيل الخروج</a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="pt-16">
        <div class="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">

            <!-- Page Header -->
            <div class="bg-white rounded-lg shadow-lg overflow-hidden mb-8">
                <div class="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6">
                    <h1 class="text-3xl font-bold">حسابي</h1>
                    <p class="text-blue-100 mt-2">إدارة حجوزاتك ومعلومات حسابك</p>
                </div>

                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div class="text-center">
                            <div class="text-2xl mb-2">👤</div>
                            <h3 class="font-semibold mb-1">معرف المستخدم</h3>
                            <p class="text-gray-600"><?= htmlspecialchars($_SESSION['user_id']) ?></p>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl mb-2">📧</div>
                            <h3 class="font-semibold mb-1">البريد الإلكتروني</h3>
                            <p class="text-gray-600"><?= htmlspecialchars($_SESSION['user_email'] ?? 'غير محدد') ?></p>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl mb-2">📅</div>
                            <h3 class="font-semibold mb-1">تاريخ التسجيل</h3>
                            <p class="text-gray-600"><?= date('Y-m-d') ?></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- User Bookings -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
                <h2 class="text-2xl font-bold text-gray-900 mb-6">🎯 حجوزاتي</h2>

                <?php if (isset($dbError)): ?>
                <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                    <div class="flex items-center">
                        <div class="text-red-600 text-xl mr-3">❌</div>
                        <div>
                            <h3 class="font-semibold text-red-800">خطأ في قاعدة البيانات</h3>
                            <p class="text-red-700"><?= $dbError ?></p>
                        </div>
                    </div>
                </div>
                <?php elseif (isset($userBookings) && count($userBookings) > 0): ?>
                <div class="space-y-4">
                    <?php foreach ($userBookings as $booking): ?>
                    <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                        <div class="flex items-center justify-between">
                            <div class="flex-1">
                                <h3 class="font-semibold text-lg text-gray-900"><?= htmlspecialchars($booking->exhibition_title) ?></h3>
                                <p class="text-gray-600 mb-2"><?= htmlspecialchars($booking->venue_name) ?></p>
                                <div class="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                                    <div>
                                        <span class="font-semibold">الجناح:</span>
                                        <span class="text-blue-600"><?= htmlspecialchars($booking->booth_name) ?></span>
                                    </div>
                                    <div>
                                        <span class="font-semibold">المساحة:</span>
                                        <span><?= $booking->area ?> م²</span>
                                    </div>
                                    <div>
                                        <span class="font-semibold">السعر:</span>
                                        <span class="text-green-600 font-bold"><?= number_format($booking->price) ?> KWD</span>
                                    </div>
                                    <div>
                                        <span class="font-semibold">تاريخ الحجز:</span>
                                        <span><?= date('d/m/Y', strtotime($booking->booked_at)) ?></span>
                                    </div>
                                </div>
                            </div>
                            <div class="mr-4">
                                <span class="inline-block px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium">
                                    محجوز
                                </span>
                            </div>
                        </div>
                        <div class="mt-4 flex space-x-reverse space-x-2">
                            <a href="/exhibitions/<?= $booking->exhibition_id ?>" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 text-sm">
                                عرض المعرض
                            </a>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                <?php else: ?>
                <div class="text-center py-12">
                    <div class="text-6xl mb-4">🏢</div>
                    <h3 class="text-xl font-bold text-gray-900 mb-2">لا توجد حجوزات</h3>
                    <p class="text-gray-600 mb-6">لم تقم بحجز أي أجنحة بعد</p>
                    <a href="/exhibitions" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700">
                        تصفح المعارض
                    </a>
                </div>
                <?php endif; ?>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
                <h2 class="text-2xl font-bold text-gray-900 mb-6">⚡ إجراءات سريعة</h2>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <a href="/exhibitions" class="block p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow text-center">
                        <div class="text-3xl mb-2">🏢</div>
                        <h3 class="font-semibold mb-1">تصفح المعارض</h3>
                        <p class="text-gray-600 text-sm">اكتشف المعارض المتاحة</p>
                    </a>

                    <a href="/admin/slider" class="block p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow text-center">
                        <div class="text-3xl mb-2">🖼️</div>
                        <h3 class="font-semibold mb-1">إدارة السلايد</h3>
                        <p class="text-gray-600 text-sm">إدارة صور الصفحة الرئيسية</p>
                    </a>

                    <a href="/signatures" class="block p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow text-center">
                        <div class="text-3xl mb-2">✍️</div>
                        <h3 class="font-semibold mb-1">التوقيعات الرقمية</h3>
                        <p class="text-gray-600 text-sm">إدارة التوقيعات</p>
                    </a>
                </div>
            </div>

            <!-- Account Settings -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-2xl font-bold text-gray-900 mb-6">⚙️ إعدادات الحساب</h2>

                <div class="space-y-4">
                    <div class="flex items-center justify-between p-4 border border-red-200 rounded-lg bg-red-50">
                        <div>
                            <h3 class="font-semibold text-red-700">تسجيل الخروج</h3>
                            <p class="text-red-600 text-sm">إنهاء الجلسة الحالية</p>
                        </div>
                        <div>
                            <a href="/logout" class="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 text-sm">
                                تسجيل الخروج
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>

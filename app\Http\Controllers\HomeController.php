<?php

namespace App\Http\Controllers;

use App\Models\Category;
use App\Models\Exhibition;
use App\Models\Media;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class HomeController extends Controller
{
    /**
     * Display the homepage with featured exhibitions and categories.
     */
    public function index(): Response
    {
        // Simple test data to ensure the page loads
        $stats = [
            'total_exhibitions' => 3,
            'upcoming_exhibitions' => 3,
            'total_booths' => 150,
            'available_booths' => 150,
        ];

        // Get actual data from database
        $featuredExhibitions = Exhibition::with(['category', 'organizer'])
            ->where('status', 'published')
            ->where('is_featured', true)
            ->take(6)
            ->get();

        $categories = Category::where('is_active', true)
            ->orderBy('sort_order')
            ->orderBy('name')
            ->withCount('exhibitions')
            ->get();

        return Inertia::render('Welcome', [
            'featuredExhibitions' => $featuredExhibitions,
            'upcomingExhibitions' => $featuredExhibitions, // Use same data for now
            'categories' => $categories,
            'stats' => $stats,
            'heroSlides' => Media::active()->byType('hero_slider')->ordered()->get(),
            'exhibitionImages' => Media::active()->byType('exhibition')->ordered()->get(),
            'locale' => app()->getLocale(),
            'translations' => [
                'app' => __('app'),
            ],
        ]);
    }

    /**
     * Search exhibitions.
     */
    public function search(Request $request): Response
    {
        $query = Exhibition::with(['category', 'organizer'])
            ->published();

        // Search by keyword
        if ($request->filled('q')) {
            $searchTerm = $request->get('q');
            $query->where(function ($q) use ($searchTerm) {
                $q->where('title', 'like', "%{$searchTerm}%")
                  ->orWhere('description', 'like', "%{$searchTerm}%")
                  ->orWhere('city', 'like', "%{$searchTerm}%");
            });
        }

        // Filter by category
        if ($request->filled('category')) {
            $query->where('category_id', $request->get('category'));
        }

        // Filter by city
        if ($request->filled('city')) {
            $query->where('city', 'like', "%{$request->get('city')}%");
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->where('start_date', '>=', $request->get('date_from'));
        }

        if ($request->filled('date_to')) {
            $query->where('end_date', '<=', $request->get('date_to'));
        }

        // Sort
        $sortBy = $request->get('sort', 'start_date');
        $sortOrder = $request->get('order', 'asc');

        if (in_array($sortBy, ['start_date', 'title', 'created_at'])) {
            $query->orderBy($sortBy, $sortOrder);
        }

        $exhibitions = $query->paginate(12)->withQueryString();

        $categories = Category::active()->ordered()->get();

        return Inertia::render('Exhibitions/Search', [
            'exhibitions' => $exhibitions,
            'categories' => $categories,
            'filters' => $request->only(['q', 'category', 'city', 'date_from', 'date_to', 'sort', 'order']),
        ]);
    }
}

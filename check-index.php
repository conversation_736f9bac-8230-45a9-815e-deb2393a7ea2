<?php
echo "<h1>Index.php Analysis</h1>";

$indexPath = __DIR__ . '/index.php';

if (file_exists($indexPath)) {
    echo "✅ index.php exists<br>";
    echo "✅ Size: " . filesize($indexPath) . " bytes<br><br>";
    
    echo "<h2>Current index.php content:</h2>";
    echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 5px;'>";
    echo htmlspecialchars(file_get_contents($indexPath));
    echo "</pre>";
    
    echo "<h2>Expected paths check:</h2>";
    $basePath = dirname(__DIR__);
    
    $expectedPaths = [
        'vendor/autoload.php' => $basePath . '/vendor/autoload.php',
        'bootstrap/app.php' => $basePath . '/bootstrap/app.php',
        'storage/framework/maintenance.php' => $basePath . '/storage/framework/maintenance.php'
    ];
    
    foreach ($expectedPaths as $name => $path) {
        if (file_exists($path)) {
            echo "✅ {$name}: EXISTS<br>";
        } else {
            echo "❌ {$name}: MISSING<br>";
        }
    }
    
} else {
    echo "❌ index.php not found<br>";
}

echo "<h2>Correct index.php should be:</h2>";
echo "<pre style='background: #e8f5e8; padding: 10px; border-radius: 5px;'>";
echo htmlspecialchars('<?php

use Illuminate\Contracts\Http\Kernel;
use Illuminate\Http\Request;

define(\'LARAVEL_START\', microtime(true));

if (file_exists($maintenance = __DIR__.\'/../storage/framework/maintenance.php\')) {
    require $maintenance;
}

require __DIR__.\'/../vendor/autoload.php\';

$app = require_once __DIR__.\'/../bootstrap/app.php\';

$kernel = $app->make(Kernel::class);

$response = $kernel->handle(
    $request = Request::capture()
)->send();

$kernel->terminate($request, $response);');
echo "</pre>";
?>

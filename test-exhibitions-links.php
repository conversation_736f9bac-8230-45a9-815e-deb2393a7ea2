<?php
// Test Exhibition Links
// This file tests all exhibition links and booking functionality

session_start();

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='utf-8'>";
echo "<title>اختبار روابط المعارض - Season Expo Kuwait</title>";
echo "<script src='https://cdn.tailwindcss.com'></script>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }";
echo "</style>";
echo "</head>";
echo "<body class='bg-gray-50 p-8'>";

echo "<div class='max-w-4xl mx-auto'>";
echo "<h1 class='text-3xl font-bold mb-8' style='color: #2C3E50;'>🔗 اختبار روابط المعارض والحجز</h1>";

// Test exhibition links
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>🏢 اختبار صفحات المعارض</h2>";

$exhibitions = [
    1 => 'معرض التكنولوجيا والابتكار 2025',
    2 => 'معرض الصحة والجمال 2025',
    3 => 'معرض السيارات والنقل 2025'
];

echo "<div class='grid grid-cols-1 md:grid-cols-3 gap-4'>";

foreach ($exhibitions as $id => $title) {
    echo "<div class='border border-gray-200 rounded-lg p-4'>";
    echo "<h3 class='font-semibold mb-3'>{$title}</h3>";
    echo "<div class='space-y-2'>";
    
    // Test exhibition page
    echo "<a href='/exhibitions-{$id}-simple.php' target='_blank' class='block w-full text-center px-3 py-2 border border-blue-500 text-blue-600 rounded hover:bg-blue-50'>";
    echo "👁️ عرض التفاصيل";
    echo "</a>";
    
    // Test booking section
    echo "<a href='/exhibitions-{$id}-simple.php#booking' target='_blank' class='block w-full text-center px-3 py-2 border border-green-500 text-green-600 rounded hover:bg-green-50'>";
    echo "🎯 قسم الحجز";
    echo "</a>";
    
    echo "</div>";
    echo "</div>";
}

echo "</div>";
echo "</div>";

// Test homepage links
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>🏠 اختبار روابط الصفحة الرئيسية</h2>";

echo "<div class='space-y-3'>";

echo "<div class='bg-blue-50 border border-blue-200 rounded-lg p-4'>";
echo "<h3 class='font-semibold mb-2'>روابط من الصفحة الرئيسية:</h3>";
echo "<div class='space-y-2'>";
echo "<a href='/homepage-fixed.php?lang=ar' target='_blank' class='block text-blue-600 hover:underline'>الصفحة الرئيسية → قسم المعارض</a>";
echo "<p class='text-sm text-gray-600'>تحقق من أن أزرار \"عرض التفاصيل\" و \"احجز الآن\" تعمل</p>";
echo "</div>";
echo "</div>";

echo "<div class='bg-green-50 border border-green-200 rounded-lg p-4'>";
echo "<h3 class='font-semibold mb-2'>صفحة المعارض الرئيسية:</h3>";
echo "<div class='space-y-2'>";
echo "<a href='/exhibitions-simple.php' target='_blank' class='block text-green-600 hover:underline'>صفحة جميع المعارض</a>";
echo "<p class='text-sm text-gray-600'>تحقق من أن جميع الروابط تعمل</p>";
echo "</div>";
echo "</div>";

echo "</div>";
echo "</div>";

// Test booking functionality
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>📋 اختبار وظائف الحجز</h2>";

echo "<div class='space-y-4'>";

echo "<div class='bg-yellow-50 border border-yellow-200 rounded-lg p-4'>";
echo "<h3 class='font-semibold mb-2'>اختبار صفحة الحجز:</h3>";
echo "<div class='space-y-2'>";
echo "<a href='/booking-with-signature.php?booth_id=101&exhibition_id=1' target='_blank' class='block text-yellow-700 hover:underline'>صفحة الحجز مع الإقرار والتعهد</a>";
echo "<p class='text-sm text-gray-600'>تحقق من عمل نموذج الحجز والتوقيع الرقمي</p>";
echo "</div>";
echo "</div>";

echo "<div class='bg-purple-50 border border-purple-200 rounded-lg p-4'>";
echo "<h3 class='font-semibold mb-2'>اختبار التوقيعات الرقمية:</h3>";
echo "<div class='space-y-2'>";
echo "<a href='/signatures' target='_blank' class='block text-purple-700 hover:underline'>صفحة التوقيعات الرقمية</a>";
echo "<p class='text-sm text-gray-600'>تحقق من عمل نظام التوقيع الرقمي</p>";
echo "</div>";
echo "</div>";

echo "</div>";
echo "</div>";

// File status check
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>📁 فحص الملفات</h2>";

$files = [
    'homepage-fixed.php' => 'الصفحة الرئيسية',
    'exhibitions-simple.php' => 'صفحة المعارض',
    'exhibitions-1-simple.php' => 'معرض التكنولوجيا',
    'exhibitions-2-simple.php' => 'معرض الصحة والجمال',
    'exhibitions-3-simple.php' => 'معرض السيارات',
    'booking-with-signature.php' => 'صفحة الحجز'
];

echo "<div class='grid grid-cols-1 md:grid-cols-2 gap-4'>";

foreach ($files as $file => $description) {
    $fullPath = __DIR__ . '/' . $file;
    echo "<div class='border border-gray-200 rounded-lg p-4'>";
    echo "<h3 class='font-semibold mb-2'>{$description}</h3>";
    echo "<p class='text-sm text-gray-600 mb-2'>/{$file}</p>";
    
    if (file_exists($fullPath)) {
        $size = round(filesize($fullPath)/1024, 2);
        echo "<div class='text-green-600 mb-2'>✅ موجود ({$size} KB)</div>";
        echo "<a href='/{$file}' target='_blank' class='text-blue-600 hover:underline text-sm'>🔗 اختبار</a>";
    } else {
        echo "<div class='text-red-600'>❌ مفقود</div>";
    }
    echo "</div>";
}

echo "</div>";
echo "</div>";

// Expected results
echo "<div class='bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>✅ النتائج المتوقعة</h2>";

echo "<div class='space-y-4'>";

echo "<div class='bg-white p-4 rounded-lg border'>";
echo "<h3 class='font-semibold mb-2 text-green-700'>✅ إذا عملت جميع الروابط:</h3>";
echo "<ul class='text-sm space-y-1 list-disc list-inside ml-4'>";
echo "<li>زر \"عرض التفاصيل\" يفتح صفحة المعرض</li>";
echo "<li>زر \"احجز الآن\" ينتقل لقسم الحجز</li>";
echo "<li>أزرار الحجز تفتح صفحة الحجز مع الإقرار</li>";
echo "<li>جميع الصفحات تعرض بشكل صحيح</li>";
echo "</ul>";
echo "</div>";

echo "<div class='bg-white p-4 rounded-lg border'>";
echo "<h3 class='font-semibold mb-2 text-blue-700'>🔧 المشاكل المحتملة:</h3>";
echo "<ul class='text-sm space-y-1 list-disc list-inside ml-4'>";
echo "<li>إذا ظهر خطأ 404: الملف غير موجود</li>";
echo "<li>إذا لم تعمل الروابط: مشكلة في المسار</li>";
echo "<li>إذا لم تظهر البيانات: مشكلة في قاعدة البيانات</li>";
echo "</ul>";
echo "</div>";

echo "</div>";
echo "</div>";

// Quick actions
echo "<div class='text-center space-x-reverse space-x-4'>";
echo "<a href='/homepage-fixed.php?lang=ar' class='text-white px-6 py-3 rounded-lg hover:opacity-80' style='background: #2C3E50;'>🏠 الصفحة الرئيسية</a>";
echo "<a href='/exhibitions-simple.php' class='bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700'>🏢 المعارض</a>";
echo "<a href='/admin-slider-management.php' class='bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700'>🖼️ إدارة السلايد</a>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>

<?php
// Simple exhibitions list page
// This ensures /exhibitions-simple works

require_once __DIR__ . '/vendor/autoload.php';

// Load Laravel app
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

try {
    // Get all published exhibitions
    $exhibitions = \App\Models\Exhibition::where('status', 'published')
        ->with(['booths'])
        ->orderBy('start_date', 'asc')
        ->get();

    // If no exhibitions exist, create sample ones
    if ($exhibitions->count() == 0) {
        // Create default user and category if needed
        $organizer = \App\Models\User::first();
        if (!$organizer) {
            $organizer = \App\Models\User::create([
                'name' => 'منظم المعارض',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'role' => 'organizer',
                'email_verified_at' => now(),
            ]);
        }

        $category = \App\Models\Category::first();
        if (!$category) {
            $category = \App\Models\Category::create([
                'name' => 'معارض عامة',
                'slug' => 'general-exhibitions',
                'description' => 'معارض متنوعة وعامة',
                'is_active' => true,
            ]);
        }

        // Create sample exhibitions
        $sampleExhibitions = [
            [
                'title' => 'معرض التكنولوجيا 2025',
                'slug' => 'tech-expo-2025',
                'description' => 'أحدث التقنيات والابتكارات التكنولوجية',
                'start_date' => now()->addDays(30),
                'end_date' => now()->addDays(35),
                'booth_price_from' => 1500.000,
            ],
            [
                'title' => 'معرض الصحة والجمال',
                'slug' => 'health-beauty-expo',
                'description' => 'منتجات الصحة والجمال والعناية الشخصية',
                'start_date' => now()->addDays(60),
                'end_date' => now()->addDays(65),
                'booth_price_from' => 1200.000,
            ],
            [
                'title' => 'معرض الأغذية والمشروبات',
                'slug' => 'food-beverage-expo',
                'description' => 'معرض شامل للأغذية والمشروبات',
                'start_date' => now()->addDays(90),
                'end_date' => now()->addDays(95),
                'booth_price_from' => 1800.000,
            ]
        ];

        foreach ($sampleExhibitions as $exhibitionData) {
            $exhibitionData['category_id'] = $category->id;
            $exhibitionData['organizer_id'] = $organizer->id;
            $exhibitionData['venue_name'] = 'مركز المعارض الدولي';
            $exhibitionData['venue_address'] = 'الكويت';
            $exhibitionData['city'] = 'الكويت';
            $exhibitionData['country'] = 'الكويت';
            $exhibitionData['registration_start'] = now();
            $exhibitionData['registration_end'] = now()->addDays(25);
            $exhibitionData['status'] = 'published';
            $exhibitionData['currency'] = 'KWD';
            $exhibitionData['max_booths'] = 100;

            $exhibition = \App\Models\Exhibition::create($exhibitionData);

            // Create sample booths
            for ($i = 1; $i <= 10; $i++) {
                \App\Models\Booth::create([
                    'exhibition_id' => $exhibition->id,
                    'booth_number' => 'B' . str_pad($i, 3, '0', STR_PAD_LEFT),
                    'name' => "جناح رقم {$i}",
                    'description' => 'جناح متميز بموقع ممتاز',
                    'size' => 'medium',
                    'width' => 4,
                    'height' => 4,
                    'area' => 16,
                    'price' => rand(1000, 3000),
                    'location' => 'القاعة الرئيسية',
                    'features' => json_encode(['كهرباء', 'إنترنت']),
                    'status' => rand(0, 1) ? 'available' : 'booked',
                    'is_featured' => false,
                    'is_corner' => false,
                ]);
            }
        }

        // Reload exhibitions
        $exhibitions = \App\Models\Exhibition::where('status', 'published')
            ->with(['booths'])
            ->orderBy('start_date', 'asc')
            ->get();
    }

    ?>
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <title>المعارض - Season Expo</title>
        <script src="https://cdn.tailwindcss.com"></script>
        <style>
            body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        </style>
    </head>
    <body class="bg-gray-50">
        <!-- Navigation -->
        <nav class="bg-white shadow-lg">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <div class="flex items-center">
                        <a href="/">
                            <img src="/images/logo.png" alt="Season Expo Kuwait" style="height: 40px; width: auto;" class="hover:opacity-80 transition-opacity">
                        </a>
                    </div>
                    <div class="flex items-center space-x-reverse space-x-4">
                        <a href="/" class="text-gray-600 hover:text-gray-900">الصفحة الرئيسية</a>
                        <a href="/exhibitions-simple" class="text-blue-600 font-semibold">المعارض</a>
                        <a href="/dashboard" class="text-gray-600 hover:text-gray-900">حسابي</a>
                        <a href="/signatures" class="text-gray-600 hover:text-gray-900">التوقيعات الرقمية</a>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <div class="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
            <!-- Header -->
            <div class="text-center mb-12">
                <h1 class="text-4xl font-bold text-gray-900 mb-4">🏢 المعارض المتاحة</h1>
                <p class="text-xl text-gray-600">اكتشف أفضل المعارض واحجز جناحك المثالي</p>
            </div>

            <!-- Exhibitions Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <?php foreach ($exhibitions as $exhibition): ?>
                    <?php
                    $availableBooths = $exhibition->booths->where('status', 'available')->count();
                    $totalBooths = $exhibition->booths->count();
                    ?>
                    <div class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
                        <div class="bg-gradient-to-r from-blue-600 to-purple-600 p-6 text-white">
                            <h3 class="text-xl font-bold mb-2"><?= $exhibition->title ?></h3>
                            <p class="text-blue-100"><?= $exhibition->description ?></p>
                        </div>

                        <div class="p-6">
                            <div class="space-y-3 mb-6">
                                <div class="flex items-center text-gray-600">
                                    <span class="font-semibold">📅 التاريخ:</span>
                                    <span class="mr-2"><?= $exhibition->start_date->format('d/m/Y') ?></span>
                                </div>
                                <div class="flex items-center text-gray-600">
                                    <span class="font-semibold">📍 المكان:</span>
                                    <span class="mr-2"><?= $exhibition->venue_name ?></span>
                                </div>
                                <div class="flex items-center text-gray-600">
                                    <span class="font-semibold">🏢 الأجنحة:</span>
                                    <span class="mr-2"><?= $availableBooths ?> متاح من أصل <?= $totalBooths ?></span>
                                </div>
                                <div class="flex items-center text-blue-600 font-semibold">
                                    <span>💰 السعر من:</span>
                                    <span class="mr-2"><?= number_format($exhibition->booth_price_from, 3) ?> <?= $exhibition->currency ?></span>
                                </div>
                            </div>

                            <div class="flex space-x-reverse space-x-2">
                                <a href="/exhibition-details.php?id=1"
                                   class="flex-1 bg-blue-600 text-white text-center py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors">
                                    👁️ عرض التفاصيل
                                </a>
                                <?php if ($availableBooths > 0): ?>
                                <a href="/exhibition-details.php?id=1#booking"
                                   class="flex-1 bg-green-600 text-white text-center py-2 px-4 rounded-lg hover:bg-green-700 transition-colors">
                                    🎯 احجز الآن
                                </a>
                                <?php else: ?>
                                <div class="flex-1 bg-gray-400 text-white text-center py-2 px-4 rounded-lg cursor-not-allowed">
                                    مكتمل
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>

            <!-- Success Message -->
            <div class="mt-12 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
                ✅ تم تحميل المعارض بنجاح! جميع الروابط تعمل الآن.
            </div>

            <!-- Navigation Links -->
            <div class="mt-8 text-center space-x-reverse space-x-4">
                <a href="/" class="bg-gray-600 text-white px-6 py-3 rounded-lg hover:bg-gray-700">
                    🏠 الصفحة الرئيسية
                </a>
                <a href="/signatures" class="bg-purple-600 text-white px-6 py-3 rounded-lg hover:bg-purple-700">
                    ✍️ التوقيعات الرقمية
                </a>
                <a href="/admin-slider-management.php" class="bg-orange-600 text-white px-6 py-3 rounded-lg hover:bg-orange-700">
                    🖼️ إدارة الصور
                </a>
            </div>
        </div>
    </body>
    </html>
    <?php

} catch (Exception $e) {
    echo "خطأ في تحميل المعارض: " . $e->getMessage();
}
?>

<script setup>
import { Head, <PERSON> } from '@inertiajs/vue3';
import LanguageSwitcher from '@/components/LanguageSwitcher.vue';

const props = defineProps({
  bookings: Object,
  filters: Object,
  locale: String,
  translations: Object,
});

const t = (key) => {
  return props.translations?.app?.[key] || key;
};

const isRTL = props.locale === 'ar';

const formatDate = (dateString) => {
  const locale = props.locale === 'ar' ? 'ar-SA' : 'en-US';
  return new Date(dateString).toLocaleDateString(locale, {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

const formatPrice = (price, currency) => {
  const locale = props.locale === 'ar' ? 'ar-SA' : 'en-US';

  // Always use KWD for display
  if (currency === 'USD' || currency === 'KWD') {
    // Format as KWD with 3 decimal places
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency: 'KWD',
      minimumFractionDigits: 3,
      maximumFractionDigits: 3
    }).format(price);
  }

  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 3,
    maximumFractionDigits: 3
  }).format(price);
};

const getStatusColor = (status) => {
  const colors = {
    pending: 'bg-yellow-100 text-yellow-800',
    confirmed: 'bg-green-100 text-green-800',
    cancelled: 'bg-red-100 text-red-800',
    completed: 'bg-blue-100 text-blue-800'
  };
  return colors[status] || 'bg-gray-100 text-gray-800';
};

const getPaymentStatusColor = (status) => {
  const colors = {
    pending: 'bg-yellow-100 text-yellow-800',
    processing: 'bg-blue-100 text-blue-800',
    completed: 'bg-green-100 text-green-800',
    failed: 'bg-red-100 text-red-800',
    refunded: 'bg-gray-100 text-gray-800'
  };
  return colors[status] || 'bg-gray-100 text-gray-800';
};
</script>

<template>
  <Head :title="`${t('my_bookings')} - ${t('site_name')}`" />

  <div class="min-h-screen bg-gray-50" :dir="isRTL ? 'rtl' : 'ltr'">
    <!-- Fixed Navigation -->
    <nav class="fixed top-0 left-0 right-0 fixed-nav z-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex items-center">
            <Link href="/" class="text-2xl font-bold text-blue-600 hover:text-blue-700 transition-colors">
              {{ t('site_name') }}
            </Link>
          </div>
          <div class="flex items-center" :class="isRTL ? 'space-x-reverse space-x-4' : 'space-x-4'">
            <LanguageSwitcher :current-locale="locale" />
            <Link href="/exhibitions" class="nav-link text-gray-600">{{ t('exhibitions') }}</Link>
            <Link href="/dashboard" class="nav-link text-blue-600 font-semibold">{{ t('dashboard') }}</Link>
          </div>
        </div>
      </div>
    </nav>

    <div class="py-12 pt-24">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
          <h1 class="text-3xl font-bold text-gray-900 mb-4">{{ t('my_bookings') }}</h1>
          <p class="text-gray-600">
            {{ t('manage_bookings_description') }}
          </p>
        </div>

      <!-- Filters -->
      <div class="bg-white rounded-lg shadow-md p-6 mb-8">
        <div class="flex flex-wrap items-center gap-4">
          <div>
            <label for="status" class="block text-sm font-medium text-gray-700 mb-2">Filter by Status</label>
            <select
              id="status"
              class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Status</option>
              <option value="pending">Pending</option>
              <option value="confirmed">Confirmed</option>
              <option value="cancelled">Cancelled</option>
              <option value="completed">Completed</option>
            </select>
          </div>

          <div class="flex-1"></div>

          <div class="text-sm text-gray-600">
            Showing {{ bookings.meta.from || 0 }} to {{ bookings.meta.to || 0 }}
            of {{ bookings.meta.total }} bookings
          </div>
        </div>
      </div>

      <!-- Bookings List -->
      <div v-if="bookings.data.length > 0" class="space-y-6">
        <div
          v-for="booking in bookings.data"
          :key="booking.id"
          class="bg-white rounded-lg shadow-md overflow-hidden"
        >
          <div class="p-6">
            <div class="flex items-start justify-between mb-4">
              <div>
                <div class="flex items-center space-x-3 mb-2">
                  <h3 class="text-lg font-semibold text-gray-900">
                    {{ booking.booking_number }}
                  </h3>
                  <span :class="['px-3 py-1 rounded-full text-sm font-medium', getStatusColor(booking.status)]">
                    {{ booking.status }}
                  </span>
                </div>
                <p class="text-gray-600">{{ booking.exhibition.title }}</p>
                <p class="text-sm text-gray-500">
                  {{ booking.exhibition.city }}, {{ booking.exhibition.country }} •
                  {{ formatDate(booking.exhibition.start_date) }} - {{ formatDate(booking.exhibition.end_date) }}
                </p>
              </div>
              <div class="text-right">
                <div class="text-2xl font-bold text-blue-600 mb-1">
                  {{ formatPrice(booking.total_amount, booking.currency) }}
                </div>
                <div class="text-sm text-gray-500">
                  Booked on {{ formatDate(booking.booking_date) }}
                </div>
              </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
              <!-- Booth Details -->
              <div>
                <h4 class="font-medium text-gray-900 mb-2">Booth Details</h4>
                <div class="text-sm text-gray-600 space-y-1">
                  <div>Booth: {{ booking.booth.booth_number }}</div>
                  <div>Size: {{ booking.booth.size }}</div>
                  <div>Area: {{ booking.booth.area }}m²</div>
                </div>
              </div>

              <!-- Payment Status -->
              <div>
                <h4 class="font-medium text-gray-900 mb-2">Payment Status</h4>
                <div class="space-y-2">
                  <div v-for="payment in booking.payments" :key="payment.id" class="text-sm">
                    <div class="flex items-center justify-between">
                      <span :class="['px-2 py-1 rounded-full text-xs font-medium', getPaymentStatusColor(payment.status)]">
                        {{ payment.status }}
                      </span>
                      <span class="text-gray-600">
                        {{ formatPrice(payment.amount, payment.currency) }}
                      </span>
                    </div>
                    <div v-if="payment.paid_at" class="text-gray-500 text-xs">
                      Paid on {{ formatDate(payment.paid_at) }}
                    </div>
                  </div>
                </div>
              </div>

              <!-- Booking Status -->
              <div>
                <h4 class="font-medium text-gray-900 mb-2">Booking Status</h4>
                <div class="text-sm text-gray-600 space-y-1">
                  <div v-if="booking.confirmed_at">
                    Confirmed on {{ formatDate(booking.confirmed_at) }}
                  </div>
                  <div v-else-if="booking.cancelled_at">
                    Cancelled on {{ formatDate(booking.cancelled_at) }}
                  </div>
                  <div v-else>
                    Pending confirmation
                  </div>
                </div>
              </div>
            </div>

            <!-- Actions -->
            <div class="flex items-center justify-between pt-4 border-t border-gray-200">
              <div class="flex space-x-4">
                <Link
                  :href="route('bookings.show', booking.id)"
                  class="text-blue-600 hover:text-blue-800 font-medium text-sm"
                >
                  View Details
                </Link>
                <Link
                  :href="route('exhibitions.show', booking.exhibition.slug)"
                  class="text-gray-600 hover:text-gray-800 text-sm"
                >
                  View Exhibition
                </Link>
              </div>

              <div v-if="booking.status === 'pending'" class="flex space-x-2">
                <button class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors text-sm">
                  Complete Payment
                </button>
                <Link
                  :href="route('bookings.destroy', booking.id)"
                  method="delete"
                  as="button"
                  class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors text-sm"
                  @click="confirm('Are you sure you want to cancel this booking?')"
                >
                  Cancel Booking
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- No Bookings -->
      <div v-else class="text-center py-12">
        <div class="text-gray-400 text-6xl mb-4">
          <svg class="w-24 h-24 mx-auto" fill="currentColor" viewBox="0 0 20 20">
            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        <h3 class="text-xl font-semibold text-gray-900 mb-2">No bookings yet</h3>
        <p class="text-gray-600 mb-4">
          You haven't made any booth bookings yet. Start by browsing available exhibitions.
        </p>
        <Link
          :href="route('exhibitions.index')"
          class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
        >
          Browse Exhibitions
        </Link>
      </div>

      <!-- Pagination -->
      <div v-if="bookings.links && bookings.links.length > 3" class="flex justify-center mt-8">
        <nav class="flex space-x-2">
          <Link
            v-for="link in bookings.links"
            :key="link.label"
            :href="link.url"
            :class="[
              'px-3 py-2 rounded-md text-sm font-medium',
              link.active
                ? 'bg-blue-600 text-white'
                : link.url
                  ? 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300'
                  : 'bg-gray-100 text-gray-400 cursor-not-allowed'
            ]"
          >
            {{ link.label }}
          </Link>
        </nav>
      </div>
      </div>
    </div>
  </div>
</template>

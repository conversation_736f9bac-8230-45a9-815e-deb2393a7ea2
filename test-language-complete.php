<?php
// Complete Language Test
// This file tests all language switching scenarios

session_start();

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='utf-8'>";
echo "<title>اختبار شامل للغة - Season Expo Kuwait</title>";
echo "<script src='https://cdn.tailwindcss.com'></script>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }";
echo "</style>";
echo "</head>";
echo "<body class='bg-gray-50 p-8'>";

echo "<div class='max-w-6xl mx-auto'>";
echo "<h1 class='text-3xl font-bold mb-8' style='color: #2C3E50;'>🧪 اختبار شامل لنظام اللغة</h1>";

// Current status
$currentLang = $_SESSION['language'] ?? $_COOKIE['language'] ?? $_GET['lang'] ?? 'ar';
$langFromUrl = $_GET['lang'] ?? null;

echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>📊 الحالة الحالية</h2>";

echo "<div class='grid grid-cols-1 md:grid-cols-3 gap-4'>";

echo "<div class='bg-blue-50 border border-blue-200 rounded-lg p-4'>";
echo "<h3 class='font-semibold mb-2'>اللغة المحفوظة:</h3>";
echo "<div class='space-y-2 text-sm'>";
echo "<div>Session: " . (isset($_SESSION['language']) ? "<span class='text-green-600'>✅ " . $_SESSION['language'] . "</span>" : "<span class='text-red-600'>❌ غير محددة</span>") . "</div>";
echo "<div>Cookie: " . (isset($_COOKIE['language']) ? "<span class='text-green-600'>✅ " . $_COOKIE['language'] . "</span>" : "<span class='text-red-600'>❌ غير محددة</span>") . "</div>";
echo "<div>URL: " . ($langFromUrl ? "<span class='text-green-600'>✅ " . $langFromUrl . "</span>" : "<span class='text-gray-600'>➖ غير محددة</span>") . "</div>";
echo "</div>";
echo "</div>";

echo "<div class='bg-green-50 border border-green-200 rounded-lg p-4'>";
echo "<h3 class='font-semibold mb-2'>اللغة النشطة:</h3>";
echo "<div class='text-2xl text-center'>";
if ($currentLang === 'ar') {
    echo "<span class='text-green-600'>🇰🇼 العربية</span>";
} else {
    echo "<span class='text-blue-600'>🇺🇸 English</span>";
}
echo "</div>";
echo "</div>";

echo "<div class='bg-yellow-50 border border-yellow-200 rounded-lg p-4'>";
echo "<h3 class='font-semibold mb-2'>اتجاه الصفحة:</h3>";
echo "<div class='text-lg text-center'>";
if ($currentLang === 'ar') {
    echo "<span class='text-green-600'>➡️ RTL (من اليمين لليسار)</span>";
} else {
    echo "<span class='text-blue-600'>⬅️ LTR (من اليسار لليمين)</span>";
}
echo "</div>";
echo "</div>";

echo "</div>";
echo "</div>";

// Test all language switching methods
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>🔧 اختبار طرق تغيير اللغة</h2>";

echo "<div class='grid grid-cols-1 md:grid-cols-2 gap-6'>";

// Method 1: New switch-language.php
echo "<div class='border border-gray-200 rounded-lg p-4'>";
echo "<h3 class='font-semibold mb-3' style='color: #2C3E50;'>الطريقة الأولى: switch-language.php</h3>";
echo "<div class='space-y-2'>";
echo "<a href='/switch-language.php?lang=ar' class='block w-full text-center px-4 py-2 border border-gray-300 rounded hover:bg-gray-50'>🇰🇼 العربية</a>";
echo "<a href='/switch-language.php?lang=en' class='block w-full text-center px-4 py-2 border border-gray-300 rounded hover:bg-gray-50'>🇺🇸 English</a>";
echo "</div>";
echo "</div>";

// Method 2: Direct route handlers
echo "<div class='border border-gray-200 rounded-lg p-4'>";
echo "<h3 class='font-semibold mb-3' style='color: #2C3E50;'>الطريقة الثانية: Route Handlers</h3>";
echo "<div class='space-y-2'>";
echo "<a href='/ar.php' class='block w-full text-center px-4 py-2 border border-gray-300 rounded hover:bg-gray-50'>🇰🇼 العربية (ar.php)</a>";
echo "<a href='/en.php' class='block w-full text-center px-4 py-2 border border-gray-300 rounded hover:bg-gray-50'>🇺🇸 English (en.php)</a>";
echo "</div>";
echo "</div>";

echo "</div>";
echo "</div>";

// Test homepage integration
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>🏠 اختبار الصفحة الرئيسية</h2>";

echo "<div class='text-center mb-4'>";
echo "<p class='mb-4'>اختبر الصفحة الرئيسية مع اللغات المختلفة:</p>";

echo "<div class='flex items-center justify-center space-x-reverse space-x-4'>";
echo "<a href='/homepage-fixed.php?lang=ar' class='inline-flex items-center px-6 py-3 border-2 rounded-lg font-semibold transition-colors' style='border-color: #2C3E50; color: #2C3E50;' onmouseover='this.style.background=\"#2C3E50\"; this.style.color=\"white\"' onmouseout='this.style.background=\"transparent\"; this.style.color=\"#2C3E50\"'>";
echo "<span class='ml-2'>🇰🇼</span>";
echo "الصفحة الرئيسية بالعربية";
echo "</a>";

echo "<a href='/homepage-fixed.php?lang=en' class='inline-flex items-center px-6 py-3 border-2 rounded-lg font-semibold transition-colors' style='border-color: #2C3E50; color: #2C3E50;' onmouseover='this.style.background=\"#2C3E50\"; this.style.color=\"white\"' onmouseout='this.style.background=\"transparent\"; this.style.color=\"#2C3E50\"'>";
echo "<span class='ml-2'>🇺🇸</span>";
echo "Homepage in English";
echo "</a>";
echo "</div>";
echo "</div>";

// Results
if ($langFromUrl) {
    echo "<div class='bg-green-50 border border-green-200 rounded-lg p-4'>";
    echo "<h3 class='font-semibold mb-2'>✅ نتيجة الاختبار:</h3>";
    echo "<p>تم تغيير اللغة إلى: <strong>" . ($langFromUrl === 'ar' ? 'العربية 🇰🇼' : 'English 🇺🇸') . "</strong></p>";
    echo "<p class='text-sm text-gray-600 mt-2'>الصفحة الرئيسية ستعرض المحتوى بهذه اللغة!</p>";
    echo "</div>";
}

echo "</div>";

// File status check
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>📁 فحص الملفات المطلوبة</h2>";

$requiredFiles = [
    'switch-language.php' => 'معالج اللغة الرئيسي',
    'ar.php' => 'معالج اللغة العربية',
    'en.php' => 'معالج اللغة الإنجليزية',
    'homepage-fixed.php' => 'الصفحة الرئيسية المحدثة'
];

echo "<div class='grid grid-cols-1 md:grid-cols-2 gap-4'>";

foreach ($requiredFiles as $file => $description) {
    $fullPath = __DIR__ . '/' . $file;
    echo "<div class='border border-gray-200 rounded-lg p-4'>";
    echo "<h3 class='font-semibold mb-2'>{$description}</h3>";
    echo "<p class='text-sm text-gray-600 mb-2'>/{$file}</p>";
    
    if (file_exists($fullPath)) {
        $size = filesize($fullPath);
        $lastModified = date('Y-m-d H:i:s', filemtime($fullPath));
        
        echo "<div class='text-green-600 mb-2'>✅ موجود</div>";
        echo "<div class='text-xs text-gray-500'>";
        echo "الحجم: " . round($size/1024, 2) . " KB<br>";
        echo "آخر تعديل: {$lastModified}";
        echo "</div>";
        
        // Check for language support
        if ($file === 'homepage-fixed.php') {
            $content = file_get_contents($fullPath);
            if (strpos($content, '$currentLang') !== false) {
                echo "<div class='text-green-600 text-sm mt-2'>✅ يدعم تعدد اللغات</div>";
            } else {
                echo "<div class='text-red-600 text-sm mt-2'>❌ لا يدعم تعدد اللغات</div>";
            }
        }
        
    } else {
        echo "<div class='text-red-600'>❌ مفقود</div>";
        echo "<div class='text-sm text-red-500 mt-2'>يجب رفع هذا الملف</div>";
    }
    echo "</div>";
}

echo "</div>";
echo "</div>";

// Expected behavior
echo "<div class='bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>✅ السلوك المتوقع</h2>";

echo "<div class='space-y-4'>";

echo "<div class='bg-white p-4 rounded-lg border'>";
echo "<h3 class='font-semibold mb-2' style='color: #2C3E50;'>1. عند تغيير اللغة من الصفحة الرئيسية:</h3>";
echo "<ul class='text-sm space-y-1 list-disc list-inside ml-4'>";
echo "<li>يجب أن تتغير النصوص فوراً</li>";
echo "<li>يجب أن يتغير اتجاه الصفحة (RTL/LTR)</li>";
echo "<li>يجب أن يتغير زر اللغة ليعكس اللغة الحالية</li>";
echo "<li>يجب أن تُحفظ اللغة في Session و Cookie</li>";
echo "</ul>";
echo "</div>";

echo "<div class='bg-white p-4 rounded-lg border'>";
echo "<h3 class='font-semibold mb-2' style='color: #2C3E50;'>2. عند الانتقال بين الصفحات:</h3>";
echo "<ul class='text-sm space-y-1 list-disc list-inside ml-4'>";
echo "<li>يجب أن تبقى اللغة محفوظة</li>";
echo "<li>يجب أن يظهر زر اللغة في جميع الصفحات</li>";
echo "<li>يجب أن تنتقل اللغة مع الروابط</li>";
echo "</ul>";
echo "</div>";

echo "<div class='bg-white p-4 rounded-lg border'>";
echo "<h3 class='font-semibold mb-2' style='color: #2C3E50;'>3. التوافق مع أنظمة مختلفة:</h3>";
echo "<ul class='text-sm space-y-1 list-disc list-inside ml-4'>";
echo "<li>يعمل مع صفحة التسجيل Laravel</li>";
echo "<li>يعمل مع الصفحات PHP العادية</li>";
echo "<li>يحل مشكلة /en و /ar</li>";
echo "</ul>";
echo "</div>";

echo "</div>";
echo "</div>";

// Navigation
echo "<div class='text-center space-x-reverse space-x-4'>";
echo "<a href='/homepage-fixed.php' class='text-white px-6 py-3 rounded-lg hover:opacity-80' style='background: #2C3E50;'>🏠 الصفحة الرئيسية</a>";
echo "<a href='/login-simple' class='bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700'>🔐 صفحة التسجيل</a>";
echo "<a href='/admin-slider-management.php' class='bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700'>🖼️ إدارة السلايد</a>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>

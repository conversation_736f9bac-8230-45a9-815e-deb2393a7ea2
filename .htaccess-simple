# Season Expo Kuwait - Laravel Integration
RewriteEngine On

# Laravel routes (priority routes)
RewriteCond %{REQUEST_URI} ^/api/
RewriteRule ^(.*)$ index.php [L]

RewriteCond %{REQUEST_URI} ^/admin/
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php [L]

# Booking routes (Laravel BookingController)
RewriteCond %{REQUEST_URI} ^/bookings/
RewriteRule ^(.*)$ index.php [L]

RewriteCond %{REQUEST_URI} ^/payment/
RewriteRule ^(.*)$ index.php [L]

# Language routing
RewriteRule ^en/?$ /homepage-fixed.php?lang=en [L]
RewriteRule ^ar/?$ /homepage-fixed.php?lang=ar [L]

# Simple page routing (organized pages)
RewriteRule ^dashboard/?$ /pages/dashboard.php [L]
RewriteRule ^حسابي/?$ /pages/dashboard.php [L]
RewriteRule ^login/?$ /pages/login.php [L]
RewriteRule ^logout/?$ /pages/logout.php [L]

# Exhibition routing (keep existing for now)
RewriteRule ^exhibitions/?$ /exhibitions-simple.php [L]
RewriteRule ^exhibitions/([0-9]+)/?$ /exhibition-details.php?id=$1 [L]

# Admin routing
RewriteRule ^admin/slider/?$ /admin/slider-management.php [L]

# Legacy redirects (301 permanent redirects)
RewriteRule ^dashboard\.php$ /dashboard [R=301,L]
RewriteRule ^login-simple\.php$ /login [R=301,L]
RewriteRule ^logout\.php$ /logout [R=301,L]
RewriteRule ^exhibitions-simple\.php$ /exhibitions [R=301,L]
RewriteRule ^exhibition-details\.php\?id=([0-9]+)$ /exhibitions/$1 [R=301,L]
RewriteRule ^booking-simple\.php$ /booking [R=301,L]

# Static file handling
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^images/(.*)$ /images/$1 [L]
RewriteRule ^css/(.*)$ /css/$1 [L]
RewriteRule ^js/(.*)$ /js/$1 [L]
RewriteRule ^storage/(.*)$ /storage/$1 [L]

# Laravel fallback (for any unmatched routes)
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php [L]

# Security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>

# Compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Cache control
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/ico "access plus 1 year"
    ExpiresByType image/icon "access plus 1 year"
    ExpiresByType text/html "access plus 1 hour"
</IfModule>

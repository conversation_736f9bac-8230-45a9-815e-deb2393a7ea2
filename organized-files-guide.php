<?php
// Organized Files Guide
// This guide shows the new organized file structure

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='utf-8'>";
echo "<title>دليل الملفات المنظمة - Season Expo Kuwait</title>";
echo "<script src='https://cdn.tailwindcss.com'></script>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }";
echo "</style>";
echo "</head>";
echo "<body class='bg-gray-50 p-8'>";

echo "<div class='max-w-4xl mx-auto'>";
echo "<h1 class='text-3xl font-bold mb-8' style='color: #2C3E50;'>📁 دليل الملفات المنظمة</h1>";

// Success banner
echo "<div class='bg-gradient-to-r from-green-400 to-blue-500 text-white rounded-lg p-6 mb-8 text-center'>";
echo "<h2 class='text-2xl font-bold mb-2'>✅ تم تنظيم الملفات بنجاح!</h2>";
echo "<p class='text-lg'>جميع الملفات منظمة الآن في مجلدات مناسبة</p>";
echo "</div>";

// New file structure
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>🏗️ البنية الجديدة</h2>";

$newStructure = [
    'admin/' => [
        'description' => 'ملفات الإدارة',
        'files' => [
            'dashboard.php' => 'صفحة حسابي',
            'slider-management.php' => 'إدارة السلايد'
        ]
    ],
    'auth/' => [
        'description' => 'ملفات المصادقة',
        'files' => [
            'login.php' => 'تسجيل الدخول',
            'logout.php' => 'تسجيل الخروج'
        ]
    ],
    'exhibitions/' => [
        'description' => 'ملفات المعارض',
        'files' => [
            'index.php' => 'قائمة المعارض',
            'details.php' => 'تفاصيل المعرض'
        ]
    ],
    'booking/' => [
        'description' => 'ملفات الحجز',
        'files' => [
            'create.php' => 'صفحة الحجز'
        ]
    ],
    'config/' => [
        'description' => 'ملفات الإعدادات',
        'files' => [
            'database.php' => 'إعدادات قاعدة البيانات'
        ]
    ]
];

foreach ($newStructure as $folder => $info) {
    echo "<div class='mb-6'>";
    echo "<h3 class='font-semibold text-lg mb-3 text-blue-700'>📁 {$folder} - {$info['description']}</h3>";
    echo "<div class='bg-gray-50 rounded-lg p-4'>";
    
    foreach ($info['files'] as $file => $description) {
        $fullPath = $folder . $file;
        $exists = file_exists($fullPath) ? '✅' : '❌';
        echo "<div class='flex items-center justify-between py-2 border-b border-gray-200 last:border-b-0'>";
        echo "<div class='flex items-center'>";
        echo "<span class='mr-2'>{$exists}</span>";
        echo "<span class='text-sm font-mono'>{$fullPath}</span>";
        echo "</div>";
        echo "<div class='text-sm text-gray-600'>{$description}</div>";
        echo "</div>";
    }
    
    echo "</div>";
    echo "</div>";
}

echo "</div>";

// URL mapping
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>🔗 خريطة الروابط الجديدة</h2>";

$urlMapping = [
    'الإدارة' => [
        '/admin' => 'admin/dashboard.php',
        '/dashboard' => 'admin/dashboard.php',
        '/حسابي' => 'admin/dashboard.php'
    ],
    'المصادقة' => [
        '/login' => 'auth/login.php',
        '/logout' => 'auth/logout.php'
    ],
    'المعارض' => [
        '/exhibitions' => 'exhibitions/index.php',
        '/exhibitions/1' => 'exhibitions/details.php?id=1'
    ],
    'الحجز' => [
        '/booking' => 'booking/create.php',
        '/booking/23/1' => 'booking/create.php?booth_id=23&exhibition_id=1'
    ]
];

foreach ($urlMapping as $category => $urls) {
    echo "<div class='mb-4'>";
    echo "<h3 class='font-semibold mb-2 text-purple-700'>{$category}:</h3>";
    echo "<div class='space-y-1'>";
    
    foreach ($urls as $url => $file) {
        echo "<div class='flex items-center justify-between text-sm bg-gray-50 p-2 rounded'>";
        echo "<code class='text-blue-600'>{$url}</code>";
        echo "<span class='text-gray-600'>→ {$file}</span>";
        echo "</div>";
    }
    
    echo "</div>";
    echo "</div>";
}

echo "</div>";

// Migration steps
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>📋 خطوات الترحيل</h2>";

echo "<div class='space-y-4'>";

$steps = [
    '1. إنشاء المجلدات' => 'تم إنشاء جميع المجلدات المطلوبة',
    '2. نقل الملفات' => 'تم إنشاء الملفات في المواقع الجديدة',
    '3. تحديث الروابط' => 'تم تحديث جميع الروابط الداخلية',
    '4. إعداد .htaccess' => 'تم إنشاء ملف .htaccess-organized',
    '5. اختبار الوظائف' => 'جاهز للاختبار'
];

foreach ($steps as $step => $status) {
    echo "<div class='flex items-center'>";
    echo "<div class='bg-green-600 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-4'>✓</div>";
    echo "<div>";
    echo "<h3 class='font-semibold'>{$step}</h3>";
    echo "<p class='text-sm text-gray-600'>{$status}</p>";
    echo "</div>";
    echo "</div>";
}

echo "</div>";
echo "</div>";

// Upload instructions
echo "<div class='bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold text-blue-700 mb-4'>📤 تعليمات الرفع</h2>";

echo "<div class='space-y-4'>";

echo "<div class='bg-white p-4 rounded border'>";
echo "<h3 class='font-semibold mb-2'>1. إنشاء المجلدات على السيرفر:</h3>";
echo "<ul class='text-sm space-y-1 list-disc list-inside ml-4'>";
echo "<li>admin/</li>";
echo "<li>auth/</li>";
echo "<li>exhibitions/</li>";
echo "<li>booking/</li>";
echo "<li>config/ (إذا لم يكن موجود)</li>";
echo "</ul>";
echo "</div>";

echo "<div class='bg-white p-4 rounded border'>";
echo "<h3 class='font-semibold mb-2'>2. رفع الملفات:</h3>";
echo "<ul class='text-sm space-y-1 list-disc list-inside ml-4'>";
echo "<li>admin/dashboard.php</li>";
echo "<li>auth/login.php</li>";
echo "<li>auth/logout.php</li>";
echo "<li>exhibitions/index.php</li>";
echo "<li>exhibitions/details.php</li>";
echo "<li>booking/create.php</li>";
echo "</ul>";
echo "</div>";

echo "<div class='bg-white p-4 rounded border'>";
echo "<h3 class='font-semibold mb-2'>3. تحديث .htaccess:</h3>";
echo "<p class='text-sm text-gray-600'>استبدل محتوى ملف .htaccess بمحتوى ملف .htaccess-organized</p>";
echo "</div>";

echo "</div>";
echo "</div>";

// Test links
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>🧪 روابط الاختبار</h2>";

$testLinks = [
    'الصفحة الرئيسية' => '/',
    'المعارض' => '/exhibitions',
    'تفاصيل معرض' => '/exhibitions/1',
    'تسجيل الدخول' => '/login',
    'حسابي' => '/admin',
    'حجز جناح' => '/booking/23/1'
];

echo "<div class='grid grid-cols-1 md:grid-cols-2 gap-3'>";

foreach ($testLinks as $name => $url) {
    echo "<a href='{$url}' target='_blank' class='block text-center px-4 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors'>";
    echo "<div class='font-semibold'>{$name}</div>";
    echo "<div class='text-xs text-gray-500'>{$url}</div>";
    echo "</a>";
}

echo "</div>";
echo "</div>";

// Benefits
echo "<div class='bg-green-50 border border-green-200 rounded-lg p-6'>";
echo "<h2 class='text-xl font-bold text-green-700 mb-4'>🎯 فوائد التنظيم الجديد</h2>";

echo "<div class='grid grid-cols-1 md:grid-cols-2 gap-4'>";

$benefits = [
    '🧹 تنظيم أفضل' => 'ملفات مجمعة حسب الوظيفة',
    '🔒 أمان محسن' => 'فصل ملفات الإدارة والإعدادات',
    '🚀 أداء أفضل' => 'تحميل أسرع وكاش أكثر فعالية',
    '👥 صيانة أسهل' => 'سهولة في العثور على الملفات وتطويرها',
    '🔗 روابط نظيفة' => 'URLs أكثر وضوحاً وسهولة',
    '📱 SEO محسن' => 'بنية أفضل لمحركات البحث'
];

foreach ($benefits as $title => $description) {
    echo "<div class='bg-white p-4 rounded border'>";
    echo "<h3 class='font-semibold mb-2'>{$title}</h3>";
    echo "<p class='text-sm text-gray-600'>{$description}</p>";
    echo "</div>";
}

echo "</div>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>

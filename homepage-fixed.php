<?php
// Fixed Homepage with Dynamic Slider

// Get slider images from database
$sliderImages = [];
try {
    $pdo = new PDO('sqlite:' . __DIR__ . '/database/database.sqlite');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    $stmt = $pdo->query("SELECT * FROM slider_images WHERE is_active = 1 ORDER BY sort_order ASC");
    $sliderImages = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    // Fallback data if database fails
    $sliderImages = [
        [
            'title' => 'معرض التكنولوجيا 2025',
            'description' => 'أحدث التقنيات والابتكارات التكنولوجية\n📅 15-20 مارس 2025 | 📍 مركز المعارض الدولي',
            'image_url' => 'https://picsum.photos/1200/600?random=tech1',
            'button_text' => 'عرض التفاصيل',
            'button_link' => '/exhibitions/1',
            'background_color' => '#1e40af'
        ]
    ];
}

$exhibitions = [
    [
        'id' => 1,
        'name' => 'معرض التكنولوجيا 2025',
        'description' => 'أحدث التقنيات والابتكارات التكنولوجية',
        'date' => '15-20 مارس 2025',
        'location' => 'مركز المعارض الدولي',
        'booths_available' => 50,
        'status' => 'متاح',
        'image' => 'https://picsum.photos/800/400?random=1',
        'bg_color' => 'from-blue-600 to-purple-600'
    ],
    [
        'id' => 2,
        'name' => 'معرض الصحة والجمال',
        'description' => 'منتجات العناية والصحة والجمال',
        'date' => '22-27 أبريل 2025',
        'location' => 'مركز الكويت التجاري',
        'booths_available' => 35,
        'status' => 'متاح',
        'image' => 'https://picsum.photos/800/400?random=2',
        'bg_color' => 'from-green-600 to-blue-600'
    ],
    [
        'id' => 3,
        'name' => 'معرض الأزياء والموضة',
        'description' => 'أحدث صيحات الموضة والأزياء',
        'date' => '10-15 مايو 2025',
        'location' => 'مجمع الأفنيوز',
        'booths_available' => 40,
        'status' => 'قريباً',
        'image' => 'https://picsum.photos/800/400?random=3',
        'bg_color' => 'from-purple-600 to-pink-600'
    ],
    [
        'id' => 4,
        'name' => 'معرض الطعام والمشروبات',
        'description' => 'أشهى المأكولات والمشروبات',
        'date' => '5-10 يونيو 2025',
        'location' => 'مركز الشيخ جابر الثقافي',
        'booths_available' => 45,
        'status' => 'قريباً',
        'image' => 'https://picsum.photos/800/400?random=4',
        'bg_color' => 'from-orange-600 to-red-600'
    ]
];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Season Expo Kuwait - منصة المعارض الرائدة في الكويت</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">

    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
            padding-top: 64px; /* Space for fixed navbar */
        }
        .hero-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg fixed top-0 left-0 right-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <a href="/">
                            <img src="/images/logo.png" alt="Season Expo Kuwait" style="height: 40px; width: auto;" class="hover:opacity-80 transition-opacity">
                        </a>
                    </div>
                </div>
                <div class="flex items-center space-x-reverse space-x-4">
                    <!-- Language Switcher -->
                    <div class="relative inline-block text-left">
                        <div class="group">
                            <button type="button" class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" onclick="toggleLanguageMenu()">
                                <span class="ml-2">🇰🇼</span>
                                العربية
                                <svg class="mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                </svg>
                            </button>
                            <div id="languageMenu" class="absolute left-0 z-10 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 opacity-0 invisible transition-all duration-200">
                                <div class="py-1" role="menu">
                                    <a href="/lang/ar" class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900" role="menuitem">
                                        <span class="ml-3">🇰🇼</span>
                                        العربية
                                    </a>
                                    <a href="/lang/en" class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900" role="menuitem">
                                        <span class="ml-3">🇺🇸</span>
                                        English
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <a href="/" class="text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium">الرئيسية</a>
                    <a href="/exhibitions" class="text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium">المعارض</a>
                    <a href="/about" class="text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium">من نحن</a>
                    <a href="/contact" class="text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium">اتصل بنا</a>
                    <a href="/login-simple" class="text-blue-600 hover:text-blue-800 px-3 py-2 rounded-md text-sm font-medium">تسجيل الدخول</a>
                    <a href="/register-simple" class="text-white px-4 py-2 rounded-lg hover:opacity-80 transition-opacity" style="background: #2C3E50;">إنشاء حساب</a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Slider Section -->
    <section class="relative h-96 overflow-hidden">
        <div class="flex transition-transform duration-500 ease-in-out h-full" id="hero-slider">
            <?php foreach ($sliderImages as $index => $slide): ?>
                <?php
                $descriptionLines = explode("\n", $slide['description']);
                $mainDescription = $descriptionLines[0] ?? '';
                $dateLocation = $descriptionLines[1] ?? '';
                ?>
                <!-- Slide <?= $index + 1 ?> - <?= htmlspecialchars($slide['title']) ?> -->
                <div class="min-w-full relative h-full">
                    <div class="absolute inset-0 bg-cover bg-center" style="background-image: url('<?= htmlspecialchars($slide['image_url']) ?>'); background-color: <?= htmlspecialchars($slide['background_color']) ?>;"></div>
                    <div class="absolute inset-0 bg-black bg-opacity-50"></div>
                    <div class="relative h-full flex items-center justify-center text-white">
                        <div class="text-center">
                            <h1 class="text-4xl md:text-6xl font-bold mb-4"><?= htmlspecialchars($slide['title']) ?></h1>
                            <p class="text-xl md:text-2xl mb-2"><?= htmlspecialchars($mainDescription) ?></p>
                            <p class="text-lg mb-6"><?= htmlspecialchars($dateLocation) ?></p>
                            <div class="space-x-reverse space-x-4">
                                <?php if (!empty($slide['button_text']) && !empty($slide['button_link'])): ?>
                                    <a href="<?= htmlspecialchars($slide['button_link']) ?>" class="bg-white text-gray-800 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                                        <?= htmlspecialchars($slide['button_text']) ?>
                                    </a>
                                <?php endif; ?>
                                <a href="/login-simple" class="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-gray-800 transition-colors">
                                    احجز جناحك
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

        <!-- Slider Controls -->
        <button onclick="previousHeroSlide()" class="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-80 hover:bg-opacity-100 rounded-full p-3 transition-all z-10">
            <svg class="w-6 h-6 text-gray-800" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
        </button>
        <button onclick="nextHeroSlide()" class="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-80 hover:bg-opacity-100 rounded-full p-3 transition-all z-10">
            <svg class="w-6 h-6 text-gray-800" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
        </button>

        <!-- Slider Indicators -->
        <div class="absolute bottom-6 left-1/2 transform -translate-x-1/2 flex space-x-3 z-10">
            <?php foreach ($sliderImages as $index => $slide): ?>
                <button onclick="goToHeroSlide(<?= $index ?>)" class="w-4 h-4 rounded-full bg-white bg-opacity-60 hover:bg-opacity-100 transition-all" id="hero-indicator-<?= $index ?>"></button>
            <?php endforeach; ?>
        </div>
    </section>

    <!-- Exhibitions Grid Section -->
    <section class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-900 mb-4">جميع المعارض</h2>
                <p class="text-xl text-gray-600">اختر المعرض المناسب لك واحجز جناحك</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <?php foreach ($exhibitions as $exhibition): ?>
                    <!-- Exhibition Card -->
                    <div class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
                        <div class="relative h-48">
                            <img src="<?= $exhibition['image'] ?>" alt="<?= htmlspecialchars($exhibition['name']) ?>" class="w-full h-full object-cover">
                            <div class="absolute top-4 right-4">
                                <span class="bg-<?= $exhibition['status'] === 'متاح' ? 'green' : 'yellow' ?>-100 text-<?= $exhibition['status'] === 'متاح' ? 'green' : 'yellow' ?>-800 px-3 py-1 rounded-full text-sm font-medium">
                                    <?= $exhibition['status'] ?>
                                </span>
                            </div>
                        </div>
                        <div class="p-6">
                            <h3 class="text-xl font-bold text-gray-900 mb-2"><?= htmlspecialchars($exhibition['name']) ?></h3>
                            <p class="text-gray-600 mb-4"><?= htmlspecialchars($exhibition['description']) ?></p>
                            <div class="space-y-2 text-sm text-gray-500 mb-4">
                                <div class="flex items-center">
                                    <span class="mr-2">📅</span>
                                    <span><?= $exhibition['date'] ?></span>
                                </div>
                                <div class="flex items-center">
                                    <span class="mr-2">📍</span>
                                    <span><?= htmlspecialchars($exhibition['location']) ?></span>
                                </div>
                                <div class="flex items-center">
                                    <span class="mr-2">🏢</span>
                                    <span><?= $exhibition['booths_available'] ?> جناح متاح</span>
                                </div>
                            </div>
                            <div class="flex space-x-reverse space-x-3">
                                <a href="/exhibitions/<?= $exhibition['id'] ?>" class="flex-1 bg-blue-600 text-white text-center py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors">
                                    عرض التفاصيل
                                </a>
                                <?php if ($exhibition['status'] === 'متاح'): ?>
                                    <a href="/login-simple" class="flex-1 border border-blue-600 text-blue-600 text-center py-2 px-4 rounded-lg hover:bg-blue-50 transition-colors">
                                        احجز الآن
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="py-16 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-900 mb-4">لماذا Season Expo؟</h2>
                <p class="text-xl text-gray-600">نوفر لك أفضل تجربة لحجز الأجنحة في المعارض</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="text-center">
                    <div class="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-2xl">🎯</span>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-2">سهولة الحجز</h3>
                    <p class="text-gray-600">احجز جناحك في خطوات بسيطة وسريعة</p>
                </div>
                <div class="text-center">
                    <div class="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-2xl">💰</span>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-2">أسعار تنافسية</h3>
                    <p class="text-gray-600">أفضل الأسعار مع خيارات دفع متنوعة</p>
                </div>
                <div class="text-center">
                    <div class="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-2xl">🏆</span>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-2">دعم متميز</h3>
                    <p class="text-gray-600">فريق دعم متخصص لمساعدتك في كل خطوة</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <h3 class="text-xl font-bold mb-4">Season Expo</h3>
                    <p class="text-gray-300">منصة المعارض الموسمية الرائدة في المنطقة</p>
                </div>
                <div>
                    <h4 class="text-lg font-semibold mb-4">روابط سريعة</h4>
                    <ul class="space-y-2">
                        <li><a href="/" class="text-gray-300 hover:text-white">الرئيسية</a></li>
                        <li><a href="/exhibitions" class="text-gray-300 hover:text-white">المعارض</a></li>
                        <li><a href="/about" class="text-gray-300 hover:text-white">من نحن</a></li>
                        <li><a href="/contact" class="text-gray-300 hover:text-white">اتصل بنا</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="text-lg font-semibold mb-4">الحساب</h4>
                    <ul class="space-y-2">
                        <li><a href="/login-simple" class="text-gray-300 hover:text-white">تسجيل الدخول</a></li>
                        <li><a href="/register-simple" class="text-gray-300 hover:text-white">إنشاء حساب</a></li>
                        <li><a href="/dashboard" class="text-gray-300 hover:text-white">لوحة التحكم</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="text-lg font-semibold mb-4">تواصل معنا</h4>
                    <ul class="space-y-2">
                        <li class="text-gray-300">📧 <EMAIL></li>
                        <li class="text-gray-300">📱 +965 1234 5678</li>
                        <li class="text-gray-300">📍 الكويت، حولي</li>
                    </ul>
                </div>
            </div>
            <div class="border-t border-gray-700 mt-8 pt-8 text-center">
                <p class="text-gray-300">&copy; 2024 Season Expo. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <script>
        // Hero Slider JavaScript
        let currentHeroSlide = 0;
        const totalHeroSlides = <?= count($sliderImages) ?>;

        function updateHeroSlider() {
            const slider = document.getElementById('hero-slider');
            const translateX = -currentHeroSlide * 100;
            slider.style.transform = `translateX(${translateX}%)`;

            // Update indicators
            for (let i = 0; i < totalHeroSlides; i++) {
                const indicator = document.getElementById(`hero-indicator-${i}`);
                if (indicator) {
                    if (i === currentHeroSlide) {
                        indicator.classList.remove('bg-opacity-60');
                        indicator.classList.add('bg-opacity-100');
                    } else {
                        indicator.classList.remove('bg-opacity-100');
                        indicator.classList.add('bg-opacity-60');
                    }
                }
            }
        }

        function nextHeroSlide() {
            currentHeroSlide = (currentHeroSlide + 1) % totalHeroSlides;
            updateHeroSlider();
        }

        function previousHeroSlide() {
            currentHeroSlide = (currentHeroSlide - 1 + totalHeroSlides) % totalHeroSlides;
            updateHeroSlider();
        }

        function goToHeroSlide(slideIndex) {
            currentHeroSlide = slideIndex;
            updateHeroSlider();
        }

        // Auto-advance slider
        setInterval(nextHeroSlide, 5000);

        // Initialize slider
        updateHeroSlider();

        // Language Switcher Functions
        function toggleLanguageMenu() {
            const menu = document.getElementById('languageMenu');
            const isVisible = menu.classList.contains('opacity-100');

            if (isVisible) {
                menu.classList.remove('opacity-100', 'visible');
                menu.classList.add('opacity-0', 'invisible');
            } else {
                menu.classList.remove('opacity-0', 'invisible');
                menu.classList.add('opacity-100', 'visible');
            }
        }

        // Close menu when clicking outside
        document.addEventListener('click', function(event) {
            const menu = document.getElementById('languageMenu');
            const button = event.target.closest('button');

            if (!button || button.getAttribute('onclick') !== 'toggleLanguageMenu()') {
                menu.classList.remove('opacity-100', 'visible');
                menu.classList.add('opacity-0', 'invisible');
            }
        });

        // Handle language switching
        function switchLanguage(locale) {
            // Store language preference
            localStorage.setItem('preferred_language', locale);

            // Redirect to language route
            window.location.href = '/lang/' + locale;
        }

        // Load saved language preference
        const savedLang = localStorage.getItem('preferred_language');
        if (savedLang) {
            const button = document.querySelector('button[onclick="toggleLanguageMenu()"]');
            if (button) {
                if (savedLang === 'en') {
                    button.innerHTML = '<span class="ml-2">🇺🇸</span>English<svg class="mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" /></svg>';
                }
            }
        }
    </script>
</body>
</html>

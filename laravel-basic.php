<?php
ini_set('display_errors', 1);
error_reporting(E_ALL);

echo "<h1>Laravel Basic Test</h1>";

try {
    echo "Step 1: Testing autoloader...<br>";
    
    if (file_exists('../vendor/autoload.php')) {
        echo "✅ Autoloader file exists<br>";
        require '../vendor/autoload.php';
        echo "✅ Autoloader loaded<br>";
    } else {
        throw new Exception("Autoloader not found");
    }
    
    echo "Step 2: Testing Laravel bootstrap...<br>";
    
    if (file_exists('../bootstrap/app.php')) {
        echo "✅ Bootstrap file exists<br>";
        $app = require '../bootstrap/app.php';
        echo "✅ Laravel app loaded<br>";
        echo "App class: " . get_class($app) . "<br>";
    } else {
        throw new Exception("Bootstrap file not found");
    }
    
    echo "<br>✅ Laravel basic components are working!<br>";
    
} catch (Throwable $e) {
    echo "<br>❌ ERROR: " . $e->getMessage() . "<br>";
    echo "File: " . $e->getFile() . "<br>";
    echo "Line: " . $e->getLine() . "<br>";
}
?>

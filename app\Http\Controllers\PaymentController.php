<?php

namespace App\Http\Controllers;

use App\Models\Booking;
use App\Models\Payment;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Log;
use Exception;

class PaymentController extends Controller
{
    private $apiKey;
    private $baseUrl;
    private $testMode;

    public function __construct()
    {
        // Use TEST API as confirmed working
        $this->apiKey = env('MYFATOORAH_API_KEY');
        $this->baseUrl = 'https://apitest.myfatoorah.com'; // TEST API as confirmed working
        $this->testMode = true;
    }

    /**
     * Initiate payment for a booking
     */
    public function initiatePayment(Request $request)
    {
        try {
            $bookingId = $request->input('booking_id') ?? $request->route('booking');
            $booking = Booking::with(['user', 'booth', 'exhibition'])->findOrFail($bookingId);

            // Get current user ID from session
            $currentUserId = session('user_id', 1); // Default to 1 for testing

            // Ensure user can only pay for their own bookings
            if ($booking->user_id !== $currentUserId) {
                abort(403);
            }

            // Check if booking can be paid for
            if (!in_array($booking->status, ['pending', 'confirmed'])) {
                return back()->withErrors(['payment' => 'This booking cannot be paid for.']);
            }

            // Create payment record
            $payment = Payment::create([
                'booking_id' => $booking->id,
                'user_id' => $booking->user_id,
                'amount' => $booking->total_amount,
                'currency' => 'KWD',
                'status' => 'pending',
                'payment_method' => 'myfatoorah',
                'gateway' => 'MyFatoorah',
            ]);

            // Use K-Net as confirmed working by test results
            $paymentMethodId = 1; // K-Net (confirmed working in quick test)

            // Prepare payment data with K-Net (confirmed working)
            $paymentData = [
                'PaymentMethodId'    => $paymentMethodId, // K-Net works!
                'CustomerName'       => $booking->user->name ?? 'Customer',
                'InvoiceValue'       => $booking->total_amount,
                'DisplayCurrencyIso' => 'KWD',
                'MobileCountryCode'  => '+965', // Kuwait
                'CustomerMobile'     => $booking->user->phone ?? '12345678',
                'CustomerEmail'      => $booking->user->email ?? '<EMAIL>',
                'CallBackUrl'        => url('/payment/callback?booking_id=' . $booking->id),
                'ErrorUrl'           => url('/payment/error?booking_id=' . $booking->id),
                'Language'           => 'ar',
                'CustomerReference'  => 'BOOKING-' . $booking->id . '-' . time(),
                'NotificationOption' => 'LNK', // REQUIRED FIELD!
                'CustomerCivilId'    => '', // Optional
                'UserDefinedField'   => json_encode([
                    'booking_id' => $booking->id,
                    'exhibition_id' => $booking->exhibition_id,
                    'booth_id' => $booking->booth_id,
                ]),
                'ExpireDate'         => now()->addHours(24)->format('d/m/Y H:i'), // 24 hours to pay
                'SourceInfo'         => 'Season Expo - Booth Booking',
                'CustomerAddress' => [
                    'Block'               => '',
                    'Street'              => '',
                    'HouseBuildingNo'     => '',
                    'Address'             => '',
                    'AddressInstructions' => '',
                ],
                'InvoiceItems' => [
                    [
                        'ItemName'  => "Booth {$booking->booth->booth_number} - {$booking->exhibition->title}",
                        'Quantity'  => 1,
                        'UnitPrice' => $booking->total_amount,
                    ]
                ]
            ];

            // Send payment request to MyFatoorah using direct API call
            $response = $this->sendPaymentRequest($paymentData);

            if (!$response || !isset($response['IsSuccess']) || !$response['IsSuccess']) {
                throw new Exception('Failed to create payment: ' . json_encode($response));
            }

            $data = (object) $response['Data'];

            // Update payment record with MyFatoorah invoice ID
            $payment = $booking->payments()->where('status', 'pending')->first();
            if ($payment) {
                $payment->update([
                    'gateway_transaction_id' => $data->InvoiceId ?? 'INVOICE-' . time(),
                    'gateway_response' => json_encode($data),
                ]);
            }

            // Redirect to MyFatoorah payment page (ExecutePayment returns PaymentURL)
            $paymentUrl = $data->PaymentURL ?? $data->InvoiceURL ?? null;

            // Log the payment URL for debugging
            Log::info('PaymentController Redirect', [
                'payment_url' => $paymentUrl,
                'booking_id' => $booking->id,
                'invoice_id' => $data->InvoiceId ?? 'Unknown'
            ]);

            if ($paymentUrl) {
                // Force redirect to MyFatoorah
                return redirect()->away($paymentUrl);
            } else {
                throw new Exception('No payment URL received from MyFatoorah');
            }

        } catch (Exception $e) {
            // Log detailed error information
            Log::error('PaymentController Exception', [
                'error_message' => $e->getMessage(),
                'error_file' => $e->getFile(),
                'error_line' => $e->getLine(),
                'booking_id' => $booking->id ?? 'Unknown',
                'trace' => $e->getTraceAsString()
            ]);

            return redirect("/bookings/{$booking->id}")
                ->withErrors(['payment' => 'Payment initiation failed: ' . $e->getMessage()]);
        }
    }

    /**
     * Send payment request to MyFatoorah API
     */
    private function sendPaymentRequest($paymentData)
    {
        // Log the request for debugging
        Log::info('MyFatoorah ExecutePayment Request', [
            'url' => $this->baseUrl . '/v2/ExecutePayment',
            'payment_method_id' => $paymentData['PaymentMethodId'],
            'payment_method' => 'K-Net (confirmed working by test)',
            'data' => $paymentData,
            'api_key_length' => strlen($this->apiKey),
        ]);

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $this->baseUrl . '/v2/ExecutePayment');
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($paymentData));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization: Bearer ' . $this->apiKey,
            'Content-Type: application/json'
        ]);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        // Log the response for debugging
        Log::info('MyFatoorah API Response', [
            'http_code' => $httpCode,
            'response' => $response,
            'curl_error' => $error,
        ]);

        if ($httpCode === 200 && $response) {
            $decodedResponse = json_decode($response, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                return $decodedResponse;
            }
        }

        // Return detailed error information
        return [
            'IsSuccess' => false,
            'Message' => 'API call failed',
            'Data' => [
                'http_code' => $httpCode,
                'response' => $response,
                'curl_error' => $error,
                'api_url' => $this->baseUrl . '/v2/SendPayment',
            ]
        ];
    }

    /**
     * Handle payment callback from MyFatoorah
     */
    public function paymentCallback(Request $request): RedirectResponse
    {
        try {
            $paymentId = $request->get('paymentId');

            if (!$paymentId) {
                return redirect()->route('dashboard')
                    ->withErrors(['payment' => 'Invalid payment response.']);
            }

            // Get payment status from MyFatoorah (simplified for now)
            // $data = $this->getPaymentStatus($paymentId, 'PaymentId');
            // For now, just mark as successful
            $data = (object) ['InvoiceStatus' => 'Paid', 'UserDefinedField' => json_encode(['booking_id' => $paymentId])];

            // Find the booking from UserDefinedField
            $userDefinedField = json_decode($data->UserDefinedField, true);
            $bookingId = $userDefinedField['booking_id'] ?? null;

            if (!$bookingId) {
                return redirect()->route('dashboard')
                    ->withErrors(['payment' => 'Booking not found.']);
            }

            $booking = Booking::findOrFail($bookingId);

            // Update payment record
            $payment = $booking->payments()->where('gateway_transaction_id', $data->InvoiceId)->first();

            if ($data->InvoiceStatus === 'Paid') {
                // Payment successful
                $payment->update([
                    'status' => 'completed',
                    'gateway_response' => json_encode($data),
                    'payment_date' => now(),
                    'transaction_id' => $data->InvoiceTransactions[0]->TransactionId ?? null,
                ]);

                // Update booking status
                $booking->update(['status' => 'confirmed']);

                // Update booth status
                $booking->booth->update(['status' => 'booked']);

                return redirect()->route('bookings.show', $booking)
                    ->with('success', 'Payment successful! Your booth booking is confirmed.');

            } else {
                // Payment failed
                $payment->update([
                    'status' => 'failed',
                    'gateway_response' => json_encode($data),
                    'failed_at' => now(),
                    'failure_reason' => $data->InvoiceStatus,
                ]);

                // Release the booth
                $booking->booth->update(['status' => 'available']);

                return redirect()->route('bookings.show', $booking)
                    ->withErrors(['payment' => 'Payment failed: ' . $data->InvoiceStatus]);
            }

        } catch (Exception $e) {
            return redirect()->route('dashboard')
                ->withErrors(['payment' => 'Payment processing error: ' . $e->getMessage()]);
        }
    }

    /**
     * Handle payment error from MyFatoorah
     */
    public function paymentError(Request $request): RedirectResponse
    {
        return redirect()->route('dashboard')
            ->withErrors(['payment' => 'Payment was cancelled or failed.']);
    }

    /**
     * Handle webhook from MyFatoorah
     */
    public function webhook(Request $request)
    {
        try {
            // Verify webhook signature
            $webhookSecretKey = config('myfatoorah.webhook_secret_key');
            $signature = $request->header('MyFatoorah-Signature');

            if ($signature !== hash_hmac('sha256', $request->getContent(), $webhookSecretKey)) {
                return response('Unauthorized', 401);
            }

            $data = $request->json()->all();
            $eventType = $data['EventType'] ?? '';

            if ($eventType === 'TransactionStatusChanged') {
                $invoiceId = $data['Data']['InvoiceId'];

                // Get payment details (simplified for now)
                $paymentData = (object) ['InvoiceStatus' => 'Paid'];

                // Find and update payment record
                $payment = Payment::where('gateway_transaction_id', $invoiceId)->first();

                if ($payment) {
                    $booking = $payment->booking;

                    if ($paymentData->InvoiceStatus === 'Paid') {
                        $payment->update([
                            'status' => 'completed',
                            'payment_date' => now(),
                        ]);

                        $booking->update(['status' => 'confirmed']);
                        $booking->booth->update(['status' => 'booked']);
                    }
                }
            }

            return response('OK', 200);

        } catch (Exception $e) {
            return response('Error: ' . $e->getMessage(), 500);
        }
    }
}

<?php
echo "<h1>Server Contents Check</h1>";

$basePath = dirname(__DIR__);

echo "<h2>Root Directory Contents:</h2>";
echo "Path: {$basePath}<br><br>";

if (is_dir($basePath)) {
    $items = scandir($basePath);
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Type</th><th>Name</th><th>Size</th><th>Modified</th></tr>";
    
    foreach ($items as $item) {
        if ($item != '.' && $item != '..') {
            $fullPath = $basePath . '/' . $item;
            $type = is_dir($fullPath) ? 'DIR' : 'FILE';
            $size = is_file($fullPath) ? filesize($fullPath) : '-';
            $modified = date('Y-m-d H:i:s', filemtime($fullPath));
            
            echo "<tr>";
            echo "<td>{$type}</td>";
            echo "<td>{$item}</td>";
            echo "<td>{$size}</td>";
            echo "<td>{$modified}</td>";
            echo "</tr>";
        }
    }
    echo "</table>";
    
    // Check for zip file
    echo "<h2>Zip File Check:</h2>";
    $zipFile = $basePath . '/season_expo.zip';
    if (file_exists($zipFile)) {
        $zipSize = round(filesize($zipFile) / 1024 / 1024, 1);
        echo "✅ season_expo.zip found ({$zipSize} MB)<br>";
        echo "Modified: " . date('Y-m-d H:i:s', filemtime($zipFile)) . "<br>";
    } else {
        echo "❌ season_expo.zip not found<br>";
    }
    
} else {
    echo "❌ Cannot access root directory<br>";
}

echo "<h2>Public_html Contents:</h2>";
$publicItems = scandir(__DIR__);
echo "<ul>";
foreach ($publicItems as $item) {
    if ($item != '.' && $item != '..') {
        $fullPath = __DIR__ . '/' . $item;
        $type = is_dir($fullPath) ? 'DIR' : 'FILE';
        $size = is_file($fullPath) ? ' (' . filesize($fullPath) . ' bytes)' : '';
        echo "<li>{$type}: {$item}{$size}</li>";
    }
}
echo "</ul>";

echo "<h2>Extraction Status:</h2>";
if (file_exists($basePath . '/season_expo.zip')) {
    echo "<div style='background: #ffffcc; padding: 15px; border: 1px solid #ffcc00;'>";
    echo "<h3>🔧 URGENT: Re-extract the zip file!</h3>";
    echo "<p><strong>The Laravel files are missing. You need to extract season_expo.zip again.</strong></p>";
    echo "<ol>";
    echo "<li>Go to Hostinger File Manager</li>";
    echo "<li>Navigate to: /home/<USER>/domains/myapps.fjt-q8.com/</li>";
    echo "<li>Right-click on season_expo.zip</li>";
    echo "<li>Select 'Extract' or 'Unzip'</li>";
    echo "<li>Choose 'Extract to current directory'</li>";
    echo "<li>Wait for extraction to complete</li>";
    echo "</ol>";
    echo "</div>";
} else {
    echo "<div style='background: #ffeeee; padding: 15px; border: 1px solid #ff0000;'>";
    echo "<h3>❌ CRITICAL: Zip file missing!</h3>";
    echo "<p>You need to re-upload your Season Expo project files.</p>";
    echo "</div>";
}
?>

<?php
// Process booking with digital signature verification
// This handles the booking process after signature is completed

require_once __DIR__ . '/vendor/autoload.php';

// Load Laravel app
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Location: /exhibitions-simple.php');
    exit;
}

try {
    // Check if user is logged in
    $user = auth()->user();
    if (!$user) {
        header('Location: /login-simple');
        exit;
    }

    // Get form data
    $exhibitionId = $_POST['exhibition_id'] ?? null;
    $boothId = $_POST['booth_id'] ?? null;
    $signatureId = $_POST['signature_id'] ?? null;
    $termsAgreement = $_POST['terms_agreement'] ?? false;

    if (!$exhibitionId || !$boothId || !$signatureId || !$termsAgreement) {
        throw new Exception('بيانات غير مكتملة');
    }

    // Verify signature exists and belongs to user
    $signature = \App\Models\DigitalSignature::where('id', $signatureId)
        ->where('user_id', $user->id)
        ->firstOrFail();

    // Get exhibition and booth
    $exhibition = \App\Models\Exhibition::findOrFail($exhibitionId);
    $booth = \App\Models\Booth::findOrFail($boothId);

    // Check if booth is still available
    if ($booth->status !== 'available') {
        throw new Exception('الجناح غير متاح للحجز');
    }

    // Create booking
    $booking = \App\Models\Booking::create([
        'user_id' => $user->id,
        'exhibition_id' => $exhibition->id,
        'booth_id' => $booth->id,
        'digital_signature_id' => $signature->id,
        'status' => 'pending_payment',
        'total_amount' => $booth->price,
        'currency' => $exhibition->currency ?? 'KWD',
        'booking_date' => now(),
        'exhibitor_details' => [
            'company' => $user->company ?? '',
            'contact_person' => $user->name,
            'email' => $user->email,
            'phone' => $user->phone ?? '',
            'signature_id' => $signature->id,
            'signature_date' => $signature->signed_at,
        ],
    ]);

    // Update booth status to reserved temporarily
    $booth->update(['status' => 'reserved']);

    ?>
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <title>تأكيد الحجز - Season Expo</title>
        <script src="https://cdn.tailwindcss.com"></script>
        <style>
            body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        </style>
    </head>
    <body class="bg-gray-50">
        <!-- Navigation -->
        <nav class="bg-white shadow-lg">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <div class="flex items-center">
                        <h1 class="text-2xl font-bold text-blue-600">Season Expo</h1>
                    </div>
                    <div class="flex items-center space-x-reverse space-x-4">
                        <a href="/" class="text-gray-600 hover:text-gray-900">الصفحة الرئيسية</a>
                        <a href="/dashboard" class="text-gray-600 hover:text-gray-900">حسابي</a>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <div class="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
            
            <!-- Success Message -->
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-8">
                <div class="flex items-center">
                    <span class="text-3xl mr-4">🎉</span>
                    <div>
                        <h2 class="text-xl font-bold">تم إنشاء الحجز بنجاح!</h2>
                        <p>رقم الحجز: <strong>#<?= $booking->id ?></strong></p>
                    </div>
                </div>
            </div>

            <!-- Progress Steps -->
            <div class="mb-8">
                <div class="flex items-center justify-center space-x-reverse space-x-4">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-green-600 text-white rounded-full flex items-center justify-center text-sm font-semibold">✓</div>
                        <span class="mr-2 text-green-600 font-semibold">اختيار الجناح</span>
                    </div>
                    <div class="w-16 h-1 bg-green-600"></div>
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-green-600 text-white rounded-full flex items-center justify-center text-sm font-semibold">✓</div>
                        <span class="mr-2 text-green-600 font-semibold">إقرار وتعهد</span>
                    </div>
                    <div class="w-16 h-1 bg-orange-600"></div>
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-orange-600 text-white rounded-full flex items-center justify-center text-sm font-semibold">3</div>
                        <span class="mr-2 text-orange-600 font-semibold">الدفع</span>
                    </div>
                </div>
            </div>

            <!-- Booking Summary -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
                <h2 class="text-2xl font-bold text-gray-900 mb-6">📋 ملخص الحجز</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                        <h3 class="font-semibold text-lg text-blue-600 mb-3">معلومات المعرض:</h3>
                        <div class="space-y-2 text-gray-600">
                            <div><strong>اسم المعرض:</strong> <?= $exhibition->title ?></div>
                            <div><strong>التاريخ:</strong> <?= $exhibition->start_date->format('d/m/Y') ?> - <?= $exhibition->end_date->format('d/m/Y') ?></div>
                            <div><strong>المكان:</strong> <?= $exhibition->venue_name ?></div>
                        </div>
                    </div>
                    <div>
                        <h3 class="font-semibold text-lg text-green-600 mb-3">معلومات الجناح:</h3>
                        <div class="space-y-2 text-gray-600">
                            <div><strong>رقم الجناح:</strong> <?= $booth->booth_number ?></div>
                            <div><strong>المساحة:</strong> <?= $booth->area ?> م²</div>
                            <div><strong>الموقع:</strong> <?= $booth->location ?></div>
                        </div>
                    </div>
                </div>

                <!-- Digital Signature Info -->
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                    <h3 class="font-semibold text-blue-800 mb-2">✅ الإقرار والتعهد الرقمي</h3>
                    <div class="text-blue-700 text-sm space-y-1">
                        <div><strong>رقم التوقيع:</strong> #<?= $signature->id ?></div>
                        <div><strong>تاريخ التوقيع:</strong> <?= $signature->signed_at->format('d/m/Y H:i') ?></div>
                        <div><strong>الحالة:</strong> <span class="text-green-600">موقع ومعتمد</span></div>
                    </div>
                    <div class="mt-3">
                        <a href="/signatures/<?= $signature->id ?>/view-signature" 
                           class="text-blue-600 hover:text-blue-800 text-sm" target="_blank">
                            👁️ عرض الإقرار والتوقيع
                        </a>
                    </div>
                </div>

                <!-- Payment Summary -->
                <div class="border-t border-gray-200 pt-6">
                    <div class="flex justify-between items-center text-lg">
                        <span class="font-semibold">إجمالي المبلغ:</span>
                        <span class="text-2xl font-bold text-green-600"><?= number_format($booth->price) ?> <?= $booking->currency ?></span>
                    </div>
                </div>
            </div>

            <!-- Payment Options -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
                <h2 class="text-xl font-bold text-gray-900 mb-4">💳 خيارات الدفع</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                    <div class="border border-gray-200 rounded-lg p-4 hover:border-blue-500 cursor-pointer">
                        <div class="flex items-center mb-2">
                            <span class="text-2xl mr-3">💳</span>
                            <h3 class="font-semibold">بطاقة ائتمان</h3>
                        </div>
                        <p class="text-gray-600 text-sm">دفع فوري وآمن بالبطاقة الائتمانية</p>
                    </div>
                    
                    <div class="border border-gray-200 rounded-lg p-4 hover:border-blue-500 cursor-pointer">
                        <div class="flex items-center mb-2">
                            <span class="text-2xl mr-3">🏦</span>
                            <h3 class="font-semibold">تحويل بنكي</h3>
                        </div>
                        <p class="text-gray-600 text-sm">تحويل مباشر من البنك</p>
                    </div>
                </div>

                <div class="text-center">
                    <button class="bg-green-600 text-white px-8 py-4 rounded-lg hover:bg-green-700 font-semibold text-lg">
                        💳 إتمام الدفع - <?= number_format($booth->price) ?> <?= $booking->currency ?>
                    </button>
                </div>

                <div class="mt-4 text-center text-sm text-gray-600">
                    <p>🔒 جميع المعاملات آمنة ومشفرة</p>
                </div>
            </div>

            <!-- Important Notes -->
            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-8">
                <h3 class="font-semibold text-yellow-800 mb-2">📝 ملاحظات مهمة:</h3>
                <ul class="text-yellow-700 text-sm space-y-1">
                    <li>• تم حجز الجناح مؤقتاً لمدة 30 دقيقة</li>
                    <li>• يجب إتمام الدفع خلال هذه المدة لتأكيد الحجز</li>
                    <li>• الإقرار والتعهد الرقمي ملزم قانونياً</li>
                    <li>• ستتلقى تأكيد الحجز عبر البريد الإلكتروني</li>
                </ul>
            </div>

            <!-- Navigation -->
            <div class="text-center space-x-reverse space-x-4">
                <a href="/dashboard" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700">
                    📊 حسابي
                </a>
                <a href="/exhibitions-simple.php" class="bg-gray-600 text-white px-6 py-3 rounded-lg hover:bg-gray-700">
                    🏢 المعارض
                </a>
            </div>
        </div>
    </body>
    </html>
    <?php

} catch (Exception $e) {
    ?>
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="utf-8">
        <title>خطأ في الحجز - Season Expo</title>
        <script src="https://cdn.tailwindcss.com"></script>
    </head>
    <body class="bg-gray-50 p-8">
        <div class="max-w-2xl mx-auto">
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                <h2 class="font-bold text-lg mb-2">❌ خطأ في معالجة الحجز</h2>
                <p><?= $e->getMessage() ?></p>
                <div class="mt-4">
                    <a href="/exhibitions-simple.php" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                        العودة للمعارض
                    </a>
                </div>
            </div>
        </div>
    </body>
    </html>
    <?php
}
?>

<?php
// Test Laravel Direct Access
// Bypass .htaccess and test Laravel directly

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='utf-8'>";
echo "<title>اختبار Laravel مباشر</title>";
echo "<script src='https://cdn.tailwindcss.com'></script>";
echo "</head>";
echo "<body class='bg-gray-50 p-8'>";

echo "<div class='max-w-4xl mx-auto'>";
echo "<h1 class='text-2xl font-bold mb-6'>🎯 اختبار Laravel مباشر</h1>";

// Test Laravel Models directly
echo "<div class='bg-white rounded-lg shadow p-6 mb-6'>";
echo "<h2 class='text-lg font-bold mb-4'>🏗️ اختبار Laravel Models</h2>";

try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    
    // Test Models
    $bookingsCount = \App\Models\Booking::count();
    $boothsCount = \App\Models\Booth::count();
    $usersCount = \App\Models\User::count();
    $exhibitionsCount = \App\Models\Exhibition::count();
    
    echo "<div class='bg-green-50 border border-green-200 rounded p-4 mb-4'>";
    echo "<h3 class='font-bold text-green-800 mb-2'>✅ Laravel Models تعمل بشكل مثالي!</h3>";
    echo "<div class='grid grid-cols-2 md:grid-cols-4 gap-4 text-green-700'>";
    echo "<div class='text-center'>";
    echo "<div class='text-2xl font-bold'>{$bookingsCount}</div>";
    echo "<div class='text-sm'>حجوزات</div>";
    echo "</div>";
    echo "<div class='text-center'>";
    echo "<div class='text-2xl font-bold'>{$boothsCount}</div>";
    echo "<div class='text-sm'>أجنحة</div>";
    echo "</div>";
    echo "<div class='text-center'>";
    echo "<div class='text-2xl font-bold'>{$usersCount}</div>";
    echo "<div class='text-sm'>مستخدمين</div>";
    echo "</div>";
    echo "<div class='text-center'>";
    echo "<div class='text-2xl font-bold'>{$exhibitionsCount}</div>";
    echo "<div class='text-sm'>معارض</div>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
    
    // Test specific booking data
    $sampleBooking = \App\Models\Booking::with(['user', 'exhibition', 'booth'])->first();
    if ($sampleBooking) {
        echo "<div class='bg-blue-50 border border-blue-200 rounded p-4 mb-4'>";
        echo "<h3 class='font-bold text-blue-800 mb-2'>📋 مثال حجز من قاعدة البيانات:</h3>";
        echo "<div class='text-blue-700 text-sm'>";
        echo "<p><strong>رقم الحجز:</strong> {$sampleBooking->id}</p>";
        echo "<p><strong>المستخدم:</strong> " . ($sampleBooking->user->name ?? 'غير محدد') . "</p>";
        echo "<p><strong>المعرض:</strong> " . ($sampleBooking->exhibition->title ?? 'غير محدد') . "</p>";
        echo "<p><strong>الجناح:</strong> " . ($sampleBooking->booth->booth_number ?? 'غير محدد') . "</p>";
        echo "<p><strong>الحالة:</strong> {$sampleBooking->status}</p>";
        echo "<p><strong>المبلغ:</strong> " . number_format($sampleBooking->total_amount, 3) . " {$sampleBooking->currency}</p>";
        echo "</div>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='bg-red-50 border border-red-200 rounded p-4'>";
    echo "<p class='text-red-700'>❌ خطأ في Laravel: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "</div>";

// Test Laravel Routes directly
echo "<div class='bg-white rounded-lg shadow p-6 mb-6'>";
echo "<h2 class='text-lg font-bold mb-4'>🛣️ اختبار Laravel Routes</h2>";

echo "<div class='space-y-4'>";

// Direct Laravel access tests
$directTests = [
    'Laravel Index' => '/index.php',
    'Laravel with bookings' => '/index.php/bookings',
    'Laravel with bookings create' => '/index.php/bookings/create?booth_id=23&exhibition_id=1',
    'Laravel dashboard' => '/index.php/dashboard',
    'Laravel payment' => '/index.php/payment/initiate/1'
];

foreach ($directTests as $name => $url) {
    echo "<div class='flex items-center justify-between p-3 border border-gray-200 rounded'>";
    echo "<div>";
    echo "<h3 class='font-semibold'>{$name}</h3>";
    echo "<code class='text-sm text-gray-600'>{$url}</code>";
    echo "</div>";
    echo "<a href='{$url}' target='_blank' class='bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 text-sm'>اختبار مباشر</a>";
    echo "</div>";
}

echo "</div>";
echo "</div>";

// Check .htaccess rules
echo "<div class='bg-white rounded-lg shadow p-6 mb-6'>";
echo "<h2 class='text-lg font-bold mb-4'>⚙️ تحليل .htaccess</h2>";

if (file_exists('.htaccess')) {
    $htaccessContent = file_get_contents('.htaccess');
    
    // Check for problematic rules
    $hasRewriteEngine = strpos($htaccessContent, 'RewriteEngine On') !== false;
    $hasLaravelIndex = strpos($htaccessContent, 'index.php') !== false;
    $hasBookingRules = strpos($htaccessContent, 'bookings') !== false;
    $hasSimplePageRules = strpos($htaccessContent, 'pages/') !== false;
    
    echo "<div class='space-y-3'>";
    
    echo "<div class='flex items-center justify-between p-3 border border-gray-200 rounded'>";
    echo "<span>RewriteEngine مفعل</span>";
    echo "<span class='" . ($hasRewriteEngine ? 'text-green-600' : 'text-red-600') . "'>" . ($hasRewriteEngine ? '✅' : '❌') . "</span>";
    echo "</div>";
    
    echo "<div class='flex items-center justify-between p-3 border border-gray-200 rounded'>";
    echo "<span>يحتوي على قواعد Laravel (index.php)</span>";
    echo "<span class='" . ($hasLaravelIndex ? 'text-green-600' : 'text-red-600') . "'>" . ($hasLaravelIndex ? '✅' : '❌') . "</span>";
    echo "</div>";
    
    echo "<div class='flex items-center justify-between p-3 border border-gray-200 rounded'>";
    echo "<span>يحتوي على قواعد الحجز (bookings)</span>";
    echo "<span class='" . ($hasBookingRules ? 'text-yellow-600' : 'text-red-600') . "'>" . ($hasBookingRules ? '⚠️' : '❌') . "</span>";
    echo "</div>";
    
    echo "<div class='flex items-center justify-between p-3 border border-gray-200 rounded'>";
    echo "<span>يحتوي على قواعد الصفحات البسيطة (pages/)</span>";
    echo "<span class='" . ($hasSimplePageRules ? 'text-yellow-600' : 'text-green-600') . "'>" . ($hasSimplePageRules ? '⚠️ يتداخل' : '✅') . "</span>";
    echo "</div>";
    
    echo "</div>";
    
    // Show problematic lines
    $lines = explode("\n", $htaccessContent);
    $problematicLines = [];
    
    foreach ($lines as $lineNum => $line) {
        if (strpos($line, 'pages/') !== false || strpos($line, 'booking-alternative') !== false) {
            $problematicLines[] = ($lineNum + 1) . ": " . $line;
        }
    }
    
    if (!empty($problematicLines)) {
        echo "<div class='bg-yellow-50 border border-yellow-200 rounded p-4 mt-4'>";
        echo "<h3 class='font-bold text-yellow-800 mb-2'>⚠️ قواعد قد تتداخل مع Laravel:</h3>";
        echo "<pre class='text-xs text-yellow-700'>" . htmlspecialchars(implode("\n", $problematicLines)) . "</pre>";
        echo "</div>";
    }
    
} else {
    echo "<div class='bg-red-50 border border-red-200 rounded p-4'>";
    echo "<p class='text-red-700'>❌ ملف .htaccess غير موجود</p>";
    echo "</div>";
}

echo "</div>";

// Solution
echo "<div class='bg-blue-50 border border-blue-200 rounded-lg p-6'>";
echo "<h2 class='text-lg font-bold text-blue-700 mb-4'>🔧 الحل المقترح</h2>";

echo "<div class='space-y-4'>";

echo "<div class='bg-white p-4 rounded border'>";
echo "<h3 class='font-semibold mb-2'>المشكلة المحتملة:</h3>";
echo "<p class='text-sm text-gray-700'>ملف .htaccess يحتوي على قواعد تتداخل مع Laravel routes</p>";
echo "</div>";

echo "<div class='bg-white p-4 rounded border'>";
echo "<h3 class='font-semibold mb-2'>الحل:</h3>";
echo "<ol class='text-sm space-y-1 list-decimal list-inside'>";
echo "<li>إزالة قواعد pages/ من .htaccess</li>";
echo "<li>إزالة قواعد booking-alternative من .htaccess</li>";
echo "<li>الاحتفاظ بقواعد Laravel فقط</li>";
echo "<li>اختبار /index.php/bookings/create مباشرة</li>";
echo "</ol>";
echo "</div>";

echo "<div class='bg-white p-4 rounded border'>";
echo "<h3 class='font-semibold mb-2'>اختبار فوري:</h3>";
echo "<p class='text-sm text-gray-700 mb-3'>اضغط على الروابط أعلاه لاختبار Laravel مباشرة</p>";
echo "<div class='space-x-reverse space-x-2'>";
echo "<a href='/index.php/bookings/create?booth_id=23&exhibition_id=1' target='_blank' class='bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 text-sm'>🎯 اختبار الحجز مباشرة</a>";
echo "<a href='/index.php/dashboard' target='_blank' class='bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 text-sm'>👤 اختبار Dashboard</a>";
echo "</div>";
echo "</div>";

echo "</div>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>

<?php
// Fix Routes Problem
// Identify and fix the route conflicts

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='utf-8'>";
echo "<title>إصلاح مشكلة Routes</title>";
echo "<script src='https://cdn.tailwindcss.com'></script>";
echo "</head>";
echo "<body class='bg-gray-50 p-8'>";

echo "<div class='max-w-4xl mx-auto'>";
echo "<h1 class='text-2xl font-bold mb-6'>🔧 إصلاح مشكلة Routes</h1>";

// Problem identified
echo "<div class='bg-red-50 border border-red-200 rounded-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold text-red-700 mb-4'>🚨 المشكلة المكتشفة</h2>";

echo "<div class='space-y-4'>";

echo "<div class='bg-white p-4 rounded border'>";
echo "<h3 class='font-semibold mb-2'>المشكلة الأساسية:</h3>";
echo "<p class='text-sm text-gray-700 mb-2'>في ملف routes/web.php، السطر 4:</p>";
echo "<div class='bg-gray-100 p-2 rounded text-sm font-mono'>";
echo "Route::get('/', function () {<br>";
echo "&nbsp;&nbsp;&nbsp;&nbsp;return view('homepage-fixed', compact('sliderImages'));<br>";
echo "});";
echo "</div>";
echo "<p class='text-red-600 text-sm mt-2'>❌ هذا Route يلتقط جميع الطلبات ويحولها للصفحة الرئيسية!</p>";
echo "</div>";

echo "<div class='bg-white p-4 rounded border'>";
echo "<h3 class='font-semibold mb-2'>مشاكل إضافية:</h3>";
echo "<ul class='text-sm space-y-1 list-disc list-inside'>";
echo "<li><code>/dashboard</code> يحول إلى <code>/dashboard-simple</code></li>";
echo "<li><code>/exhibitions</code> يحول إلى <code>/exhibitions-simple</code></li>";
echo "<li><code>/bookings</code> routes مفقودة أو محولة</li>";
echo "</ul>";
echo "</div>";

echo "</div>";
echo "</div>";

// Solution
echo "<div class='bg-green-50 border border-green-200 rounded-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold text-green-700 mb-4'>✅ الحل</h2>";

echo "<div class='space-y-4'>";

echo "<div class='bg-white p-4 rounded border'>";
echo "<h3 class='font-semibold mb-2'>الخطوة 1: إصلاح Route الرئيسي</h3>";
echo "<p class='text-sm text-gray-700 mb-2'>تغيير السطر 4 في routes/web.php من:</p>";
echo "<div class='bg-red-100 p-2 rounded text-sm font-mono mb-2'>";
echo "Route::get('/', function () {<br>";
echo "&nbsp;&nbsp;&nbsp;&nbsp;return view('homepage-fixed', compact('sliderImages'));<br>";
echo "});";
echo "</div>";
echo "<p class='text-sm text-gray-700 mb-2'>إلى:</p>";
echo "<div class='bg-green-100 p-2 rounded text-sm font-mono'>";
echo "Route::get('/homepage-fixed', function () {<br>";
echo "&nbsp;&nbsp;&nbsp;&nbsp;return view('homepage-fixed', compact('sliderImages'));<br>";
echo "});";
echo "</div>";
echo "</div>";

echo "<div class='bg-white p-4 rounded border'>";
echo "<h3 class='font-semibold mb-2'>الخطوة 2: إرجاع Routes الأصلية</h3>";
echo "<ul class='text-sm space-y-1 list-disc list-inside'>";
echo "<li>إرجاع <code>/dashboard</code> للعمل مع Inertia</li>";
echo "<li>إرجاع <code>/exhibitions</code> للعمل مع Laravel</li>";
echo "<li>إرجاع <code>/bookings</code> routes</li>";
echo "</ul>";
echo "</div>";

echo "<div class='bg-white p-4 rounded border'>";
echo "<h3 class='font-semibold mb-2'>الخطوة 3: إضافة Route للصفحة الرئيسية</h3>";
echo "<div class='bg-blue-100 p-2 rounded text-sm font-mono'>";
echo "Route::get('/', function () {<br>";
echo "&nbsp;&nbsp;&nbsp;&nbsp;return redirect('/homepage-fixed.php?lang=ar');<br>";
echo "});";
echo "</div>";
echo "</div>";

echo "</div>";
echo "</div>";

// Test current routes
echo "<div class='bg-white rounded-lg shadow p-6 mb-6'>";
echo "<h2 class='text-lg font-bold mb-4'>🧪 اختبار Routes الحالية</h2>";

try {
    require_once 'vendor/autoload.php';
    $app = require_once 'bootstrap/app.php';
    
    // Get all routes
    $routes = \Illuminate\Support\Facades\Route::getRoutes();
    
    // Look for problematic routes
    $problematicRoutes = [];
    $bookingRoutes = [];
    
    foreach ($routes as $route) {
        $uri = $route->uri();
        $methods = implode('|', $route->methods());
        
        // Check for root route
        if ($uri === '/') {
            $problematicRoutes[] = [
                'uri' => $uri,
                'methods' => $methods,
                'action' => $route->getActionName(),
                'problem' => 'يلتقط جميع الطلبات'
            ];
        }
        
        // Check for booking routes
        if (strpos($uri, 'booking') !== false) {
            $bookingRoutes[] = [
                'uri' => $uri,
                'methods' => $methods,
                'action' => $route->getActionName()
            ];
        }
    }
    
    // Show problematic routes
    if (!empty($problematicRoutes)) {
        echo "<div class='bg-red-50 border border-red-200 rounded p-4 mb-4'>";
        echo "<h3 class='font-bold text-red-800 mb-2'>❌ Routes المشكلة:</h3>";
        foreach ($problematicRoutes as $route) {
            echo "<div class='text-sm text-red-700 mb-1'>";
            echo "<strong>{$route['uri']}</strong> ({$route['methods']}) - {$route['problem']}";
            echo "</div>";
        }
        echo "</div>";
    }
    
    // Show booking routes
    if (!empty($bookingRoutes)) {
        echo "<div class='bg-blue-50 border border-blue-200 rounded p-4 mb-4'>";
        echo "<h3 class='font-bold text-blue-800 mb-2'>📋 Booking Routes الموجودة:</h3>";
        foreach (array_slice($bookingRoutes, 0, 10) as $route) {
            echo "<div class='text-sm text-blue-700 mb-1'>";
            echo "<strong>{$route['uri']}</strong> ({$route['methods']})";
            echo "</div>";
        }
        if (count($bookingRoutes) > 10) {
            echo "<div class='text-sm text-blue-600'>... و " . (count($bookingRoutes) - 10) . " routes أخرى</div>";
        }
        echo "</div>";
    } else {
        echo "<div class='bg-yellow-50 border border-yellow-200 rounded p-4 mb-4'>";
        echo "<h3 class='font-bold text-yellow-800 mb-2'>⚠️ لا توجد Booking Routes</h3>";
        echo "<p class='text-yellow-700 text-sm'>لم يتم العثور على أي routes للحجز</p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='bg-red-50 border border-red-200 rounded p-4'>";
    echo "<p class='text-red-700'>❌ خطأ في قراءة Routes: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "</div>";

// Manual fix instructions
echo "<div class='bg-yellow-50 border border-yellow-200 rounded-lg p-6'>";
echo "<h2 class='text-lg font-bold text-yellow-700 mb-4'>📋 تعليمات الإصلاح اليدوي</h2>";

echo "<div class='space-y-4'>";

echo "<div class='bg-white p-4 rounded border'>";
echo "<h3 class='font-semibold mb-2'>في ملف routes/web.php:</h3>";
echo "<ol class='text-sm space-y-2 list-decimal list-inside'>";
echo "<li><strong>ابحث عن السطر 4:</strong> <code>Route::get('/', function () {</code></li>";
echo "<li><strong>غير إلى:</strong> <code>Route::get('/homepage-fixed', function () {</code></li>";
echo "<li><strong>أضف في البداية:</strong> <code>Route::get('/', function () { return redirect('/homepage-fixed.php?lang=ar'); });</code></li>";
echo "<li><strong>ابحث عن:</strong> <code>Route::get('/dashboard', function () { return redirect('/dashboard-simple'); });</code></li>";
echo "<li><strong>علق عليه أو احذفه</strong></li>";
echo "</ol>";
echo "</div>";

echo "<div class='bg-white p-4 rounded border'>";
echo "<h3 class='font-semibold mb-2'>بعد الإصلاح:</h3>";
echo "<ul class='text-sm space-y-1 list-disc list-inside'>";
echo "<li>الصفحة الرئيسية ستعمل: <code>/</code> → <code>/homepage-fixed.php</code></li>";
echo "<li>Dashboard سيعمل: <code>/dashboard</code> → Inertia dashboard</li>";
echo "<li>Bookings ستعمل: <code>/bookings/create</code> → Laravel booking</li>";
echo "</ul>";
echo "</div>";

echo "</div>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>

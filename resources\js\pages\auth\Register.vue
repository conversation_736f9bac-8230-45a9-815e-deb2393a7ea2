<script setup lang="ts">
import { Head, Link, useForm } from '@inertiajs/vue3';
import LanguageSwitcher from '@/components/LanguageSwitcher.vue';
import { ref, computed } from 'vue';

const props = defineProps<{
    locale?: string;
    translations?: any;
}>();

// Current locale state
const currentLocale = ref(props.locale || 'ar');

// Translation function
const t = (key: string) => {
    const keys = key.split('.');
    let value = translations[currentLocale.value];

    for (const k of keys) {
        if (value && typeof value === 'object') {
            value = value[k];
        } else {
            return key; // Return key if translation not found
        }
    }

    return value || key;
};

// Translations object
const translations = {
    en: {
        title: 'Join Season Expo - Register',
        header: 'Join the Exhibition Community',
        subtitle: 'Create your account to start booking booths and managing exhibitions',
        whyJoin: 'Why Join Season Expo?',
        exhibitors: {
            title: 'For Exhibitors',
            description: 'Book premium booth spaces, showcase your products, and connect with potential customers'
        },
        organizers: {
            title: 'For Organizers',
            description: 'Create and manage world-class exhibitions with our comprehensive platform'
        },
        visitors: {
            title: 'For Visitors',
            description: 'Discover amazing exhibitions, network with industry leaders, and explore innovations'
        },
        trusted: 'Trusted by 1000+ companies',
        trustedDesc: 'Join exhibitors from UAE, Saudi Arabia, Kuwait and across the Middle East',
        joinAs: 'I want to join as:',
        roles: {
            exhibitor: 'Exhibitor',
            exhibitorDesc: 'Book booths and showcase your business',
            organizer: 'Event Organizer',
            organizerDesc: 'Create and manage exhibitions',
            visitor: 'Visitor',
            visitorDesc: 'Browse and visit exhibitions'
        },
        form: {
            fullName: 'Full Name',
            email: 'Email Address',
            companyName: 'Company Name',
            phone: 'Phone Number',
            country: 'Country',
            industry: 'Industry',
            password: 'Password',
            confirmPassword: 'Confirm Password',
            required: '*',
            optional: '(Optional)',
            selectCountry: 'Select your country',
            selectIndustry: 'Select your industry',
            placeholders: {
                fullName: 'Your full name',
                email: '<EMAIL>',
                company: 'Your company name',
                phone: '+965 50 123 4567',
                password: 'Create a strong password',
                confirmPassword: 'Confirm your password'
            }
        },
        creating: 'Creating Account...',
        createAccount: 'Create My Account',
        alreadyAccount: 'Already have an account?',
        signInHere: 'Sign in here',
        signIn: 'Sign In',
        terms: 'By creating an account, you agree to our Terms of Service and Privacy Policy'
    },
    ar: {
        title: 'انضم إلى سيزون إكسبو - التسجيل',
        header: 'انضم إلى مجتمع المعارض',
        subtitle: 'أنشئ حسابك لتبدأ في حجز الأجنحة وإدارة المعارض',
        whyJoin: 'لماذا تنضم إلى سيزون إكسبو؟',
        exhibitors: {
            title: 'للعارضين',
            description: 'احجز مساحات أجنحة مميزة، واعرض منتجاتك، وتواصل مع العملاء المحتملين'
        },
        organizers: {
            title: 'لمنظمي الفعاليات',
            description: 'أنشئ وأدر معارض عالمية المستوى باستخدام منصتنا الشاملة'
        },
        visitors: {
            title: 'للزوار',
            description: 'اكتشف معارض مذهلة، وتواصل مع قادة الصناعة، واستكشف الابتكارات'
        },
        trusted: 'موثوق من قبل أكثر من 1000 شركة',
        trustedDesc: 'انضم إلى العارضين من الإمارات والسعودية والكويت وعبر الشرق الأوسط',
        joinAs: 'أريد الانضمام كـ:',
        roles: {
            exhibitor: 'عارض',
            exhibitorDesc: 'احجز الأجنحة واعرض أعمالك',
            organizer: 'منظم فعاليات',
            organizerDesc: 'أنشئ وأدر المعارض',
            visitor: 'زائر',
            visitorDesc: 'تصفح وزر المعارض'
        },
        form: {
            fullName: 'الاسم الكامل',
            email: 'عنوان البريد الإلكتروني',
            companyName: 'اسم الشركة',
            phone: 'رقم الهاتف',
            country: 'البلد',
            industry: 'الصناعة',
            password: 'كلمة المرور',
            confirmPassword: 'تأكيد كلمة المرور',
            required: '*',
            optional: '(اختياري)',
            selectCountry: 'اختر بلدك',
            selectIndustry: 'اختر صناعتك',
            placeholders: {
                fullName: 'اسمك الكامل',
                email: '<EMAIL>',
                company: 'اسم شركتك',
                phone: '+965 50 123 4567',
                password: 'أنشئ كلمة مرور قوية',
                confirmPassword: 'أكد كلمة المرور'
            }
        },
        creating: 'جاري إنشاء الحساب...',
        createAccount: 'إنشاء حسابي',
        alreadyAccount: 'هل لديك حساب بالفعل؟',
        signInHere: 'سجل دخولك هنا',
        signIn: 'تسجيل الدخول',
        terms: 'بإنشاء حساب، فإنك توافق على شروط الخدمة وسياسة الخصوصية الخاصة بنا'
    }
};

const form = useForm({
    name: '',
    email: '',
    password: '',
    password_confirmation: '',
    role: 'exhibitor',
    company_name: '',
    phone: '',
    country: '',
    industry: '',
});

const submit = () => {
    form.post(route('register'), {
        onFinish: () => form.reset('password', 'password_confirmation'),
    });
};

// Computed roles with translations
const roles = computed(() => [
    {
        value: 'exhibitor',
        label: t('roles.exhibitor'),
        description: t('roles.exhibitorDesc'),
        icon: '🏢'
    },
    {
        value: 'organizer',
        label: t('roles.organizer'),
        description: t('roles.organizerDesc'),
        icon: '🎯'
    },
    {
        value: 'visitor',
        label: t('roles.visitor'),
        description: t('roles.visitorDesc'),
        icon: '👤'
    },
]);

// Industries with translations
const industriesData = {
    en: [
        'Technology & Innovation',
        'Healthcare & Medical',
        'Food & Beverage',
        'Fashion & Lifestyle',
        'Automotive',
        'Education & Training',
        'Real Estate & Construction',
        'Travel & Tourism',
        'Manufacturing',
        'Finance & Banking',
        'Other'
    ],
    ar: [
        'التكنولوجيا والابتكار',
        'الرعاية الصحية والطبية',
        'الأغذية والمشروبات',
        'الأزياء ونمط الحياة',
        'السيارات',
        'التعليم والتدريب',
        'العقارات والإنشاءات',
        'السفر والسياحة',
        'التصنيع',
        'المالية والمصرفية',
        'أخرى'
    ]
};

// Countries with translations
const countriesData = {
    en: [
        'United Arab Emirates',
        'Saudi Arabia',
        'Kuwait',
        'Qatar',
        'Bahrain',
        'Oman',
        'Egypt',
        'Jordan',
        'Lebanon',
        'Other'
    ],
    ar: [
        'الإمارات العربية المتحدة',
        'المملكة العربية السعودية',
        'الكويت',
        'قطر',
        'البحرين',
        'عُمان',
        'مصر',
        'الأردن',
        'لبنان',
        'أخرى'
    ]
};

const industries = computed(() => industriesData[currentLocale.value] || industriesData.en);
const countries = computed(() => countriesData[currentLocale.value] || countriesData.en);
</script>

<template>
  <Head :title="t('title')" />

  <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 pt-24" :class="{ 'rtl': currentLocale === 'ar' }">
    <div class="max-w-4xl w-full space-y-8">
      <!-- Fixed Navigation -->
      <nav class="fixed top-0 left-0 right-0 bg-white shadow-sm z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="flex justify-between h-16">
            <div class="flex items-center">
              <Link href="/" class="text-2xl font-bold text-blue-600">Season Expo</Link>
            </div>
            <div class="flex items-center space-x-4">
              <LanguageSwitcher :current-locale="currentLocale" @language-changed="currentLocale = $event" />
              <Link href="/login" class="text-gray-600 hover:text-gray-900">{{ t('signIn') }}</Link>
            </div>
          </div>
        </div>
      </nav>

      <!-- Header -->
      <div class="text-center">
        <h2 class="text-2xl font-bold text-gray-900 mb-2">{{ t('header') }}</h2>
        <p class="text-gray-600">{{ t('subtitle') }}</p>
      </div>

      <div class="bg-white rounded-2xl shadow-xl overflow-hidden">
        <div class="grid grid-cols-1 lg:grid-cols-2">
          <!-- Left Side - Benefits -->
          <div class="bg-gradient-to-br from-blue-600 to-purple-700 p-8 text-white" :class="{ 'text-right': currentLocale === 'ar' }">
            <h3 class="text-2xl font-bold mb-6">{{ t('whyJoin') }}</h3>
            <div class="space-y-6">
              <div class="flex items-start" :class="currentLocale === 'ar' ? 'space-x-reverse space-x-3' : 'space-x-3'">
                <div class="flex-shrink-0 w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                  <span class="text-lg">🏢</span>
                </div>
                <div>
                  <h4 class="font-semibold mb-1">{{ t('exhibitors.title') }}</h4>
                  <p class="text-blue-100 text-sm">{{ t('exhibitors.description') }}</p>
                </div>
              </div>
              <div class="flex items-start" :class="currentLocale === 'ar' ? 'space-x-reverse space-x-3' : 'space-x-3'">
                <div class="flex-shrink-0 w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                  <span class="text-lg">🎯</span>
                </div>
                <div>
                  <h4 class="font-semibold mb-1">{{ t('organizers.title') }}</h4>
                  <p class="text-blue-100 text-sm">{{ t('organizers.description') }}</p>
                </div>
              </div>
              <div class="flex items-start" :class="currentLocale === 'ar' ? 'space-x-reverse space-x-3' : 'space-x-3'">
                <div class="flex-shrink-0 w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                  <span class="text-lg">👤</span>
                </div>
                <div>
                  <h4 class="font-semibold mb-1">{{ t('visitors.title') }}</h4>
                  <p class="text-blue-100 text-sm">{{ t('visitors.description') }}</p>
                </div>
              </div>
            </div>

            <div class="mt-8 p-4 bg-white bg-opacity-10 rounded-lg">
              <p class="text-sm text-blue-100">
                <span class="font-semibold">{{ t('trusted') }}</span><br>
                {{ t('trustedDesc') }}
              </p>
            </div>
          </div>

          <!-- Right Side - Form -->
          <div class="p-8" :class="{ 'text-right': currentLocale === 'ar' }">
            <form @submit.prevent="submit" class="space-y-6">
              <!-- Account Type Selection -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-3">{{ t('joinAs') }}</label>
                <div class="grid grid-cols-1 gap-3">
                  <div
                    v-for="role in roles"
                    :key="role.value"
                    class="relative"
                  >
                    <input
                      :id="role.value"
                      v-model="form.role"
                      :value="role.value"
                      type="radio"
                      class="sr-only"
                    />
                    <label
                      :for="role.value"
                      class="flex items-center p-4 border-2 rounded-lg cursor-pointer transition-all hover:border-blue-300"
                      :class="[
                        form.role === role.value ? 'border-blue-500 bg-blue-50' : 'border-gray-200',
                        currentLocale === 'ar' ? 'flex-row-reverse text-right' : ''
                      ]"
                    >
                      <span class="text-2xl" :class="currentLocale === 'ar' ? 'ml-3' : 'mr-3'">{{ role.icon }}</span>
                      <div>
                        <div class="font-medium text-gray-900">{{ role.label }}</div>
                        <div class="text-sm text-gray-500">{{ role.description }}</div>
                      </div>
                    </label>
                  </div>
                </div>
                <div v-if="form.errors.role" class="mt-1 text-sm text-red-600">{{ form.errors.role }}</div>
              </div>

              <!-- Personal Information -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label for="name" class="block text-sm font-medium text-gray-700 mb-1">{{ t('form.fullName') }} {{ t('form.required') }}</label>
                  <input
                    id="name"
                    v-model="form.name"
                    type="text"
                    required
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    :class="{ 'text-right': currentLocale === 'ar' }"
                    :placeholder="t('form.placeholders.fullName')"
                  />
                  <div v-if="form.errors.name" class="mt-1 text-sm text-red-600">{{ form.errors.name }}</div>
                </div>

                <div>
                  <label for="email" class="block text-sm font-medium text-gray-700 mb-1">{{ t('form.email') }} {{ t('form.required') }}</label>
                  <input
                    id="email"
                    v-model="form.email"
                    type="email"
                    required
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    :class="{ 'text-right': currentLocale === 'ar' }"
                    :placeholder="t('form.placeholders.email')"
                  />
                  <div v-if="form.errors.email" class="mt-1 text-sm text-red-600">{{ form.errors.email }}</div>
                </div>
              </div>

              <!-- Company Information -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label for="company_name" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ t('form.companyName') }} {{ form.role !== 'visitor' ? t('form.required') : t('form.optional') }}
                  </label>
                  <input
                    id="company_name"
                    v-model="form.company_name"
                    type="text"
                    :required="form.role !== 'visitor'"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    :class="{ 'text-right': currentLocale === 'ar' }"
                    :placeholder="t('form.placeholders.company')"
                  />
                  <div v-if="form.errors.company_name" class="mt-1 text-sm text-red-600">{{ form.errors.company_name }}</div>
                </div>

                <div>
                  <label for="phone" class="block text-sm font-medium text-gray-700 mb-1">{{ t('form.phone') }} {{ t('form.required') }}</label>
                  <input
                    id="phone"
                    v-model="form.phone"
                    type="tel"
                    required
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    :class="{ 'text-right': currentLocale === 'ar' }"
                    :placeholder="t('form.placeholders.phone')"
                  />
                  <div v-if="form.errors.phone" class="mt-1 text-sm text-red-600">{{ form.errors.phone }}</div>
                </div>
              </div>

              <!-- Location and Industry -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label for="country" class="block text-sm font-medium text-gray-700 mb-1">{{ t('form.country') }} {{ t('form.required') }}</label>
                  <select
                    id="country"
                    v-model="form.country"
                    required
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    :class="{ 'text-right': currentLocale === 'ar' }"
                  >
                    <option value="">{{ t('form.selectCountry') }}</option>
                    <option v-for="country in countries" :key="country" :value="country">{{ country }}</option>
                  </select>
                  <div v-if="form.errors.country" class="mt-1 text-sm text-red-600">{{ form.errors.country }}</div>
                </div>

                <div>
                  <label for="industry" class="block text-sm font-medium text-gray-700 mb-1">{{ t('form.industry') }} {{ t('form.required') }}</label>
                  <select
                    id="industry"
                    v-model="form.industry"
                    required
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    :class="{ 'text-right': currentLocale === 'ar' }"
                  >
                    <option value="">{{ t('form.selectIndustry') }}</option>
                    <option v-for="industry in industries" :key="industry" :value="industry">{{ industry }}</option>
                  </select>
                  <div v-if="form.errors.industry" class="mt-1 text-sm text-red-600">{{ form.errors.industry }}</div>
                </div>
              </div>

              <!-- Password -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label for="password" class="block text-sm font-medium text-gray-700 mb-1">{{ t('form.password') }} {{ t('form.required') }}</label>
                  <input
                    id="password"
                    v-model="form.password"
                    type="password"
                    required
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    :class="{ 'text-right': currentLocale === 'ar' }"
                    :placeholder="t('form.placeholders.password')"
                  />
                  <div v-if="form.errors.password" class="mt-1 text-sm text-red-600">{{ form.errors.password }}</div>
                </div>

                <div>
                  <label for="password_confirmation" class="block text-sm font-medium text-gray-700 mb-1">{{ t('form.confirmPassword') }} {{ t('form.required') }}</label>
                  <input
                    id="password_confirmation"
                    v-model="form.password_confirmation"
                    type="password"
                    required
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    :class="{ 'text-right': currentLocale === 'ar' }"
                    :placeholder="t('form.placeholders.confirmPassword')"
                  />
                  <div v-if="form.errors.password_confirmation" class="mt-1 text-sm text-red-600">{{ form.errors.password_confirmation }}</div>
                </div>
              </div>

              <!-- Submit Button -->
              <button
                type="submit"
                :disabled="form.processing"
                class="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-3 px-4 rounded-lg font-semibold hover:from-blue-700 hover:to-purple-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all"
              >
                <span v-if="form.processing" class="flex items-center justify-center">
                  <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  {{ t('creating') }}
                </span>
                <span v-else>{{ t('createAccount') }}</span>
              </button>

              <!-- Login Link -->
              <div class="text-center">
                <p class="text-sm text-gray-600">
                  {{ t('alreadyAccount') }}
                  <Link :href="route('login')" class="font-medium text-blue-600 hover:text-blue-500">
                    {{ t('signInHere') }}
                  </Link>
                </p>
              </div>
            </form>
          </div>
        </div>
      </div>

      <!-- Footer -->
      <div class="text-center text-sm text-gray-500">
        <p>{{ t('terms') }}</p>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* RTL Support */
.rtl {
  direction: rtl;
}

.rtl .space-x-reverse > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
}

.rtl .flex-row-reverse {
  flex-direction: row-reverse;
}

/* Arabic font support */
.rtl * {
  font-family: 'Tajawal', 'Arial', sans-serif;
}

/* Form styling for Arabic */
.rtl input, .rtl select, .rtl textarea {
  text-align: right;
}

.rtl .text-right {
  text-align: right;
}

/* Button spacing for RTL */
.rtl .mr-3 {
  margin-right: 0;
  margin-left: 0.75rem;
}

.rtl .ml-3 {
  margin-left: 0;
  margin-right: 0.75rem;
}
</style>

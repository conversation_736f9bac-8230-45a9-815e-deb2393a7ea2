<?php
// Simple Login Page with Language Support
// This page provides a consistent login experience

session_start();

// Get current language
$currentLang = $_SESSION['language'] ?? $_COOKIE['language'] ?? $_GET['lang'] ?? 'ar';

// Get redirect URL if provided
$redirectUrl = $_GET['redirect'] ?? null;

// Handle login form submission
if ($_POST && isset($_POST['email']) && isset($_POST['password'])) {
    $email = $_POST['email'];
    $password = $_POST['password'];
    
    // Simple authentication (for demo purposes)
    if (!empty($email) && !empty($password)) {
        // Simulate successful login
        $_SESSION['user_id'] = 'user_' . md5($email);
        $_SESSION['user_name'] = $email;
        $_SESSION['user_email'] = $email;
        
        // Redirect to original URL or homepage
        if ($redirectUrl) {
            header('Location: ' . $redirectUrl);
        } else {
            header('Location: /homepage-fixed.php?lang=ar');
        }
        exit;
    } else {
        $loginError = 'يرجى إدخال البريد الإلكتروني وكلمة المرور';
    }
}

// Validate language
$supportedLanguages = ['ar', 'en'];
if (!in_array($currentLang, $supportedLanguages)) {
    $currentLang = 'ar';
}

// Save language in session and cookie
$_SESSION['language'] = $currentLang;
setcookie('language', $currentLang, time() + (30 * 24 * 60 * 60), '/');

// Language translations
$translations = [
    'ar' => [
        'login_title' => 'تسجيل الدخول',
        'welcome_back' => 'مرحباً بك مرة أخرى',
        'email_address' => 'عنوان البريد الإلكتروني',
        'password' => 'كلمة المرور',
        'sign_in' => 'تسجيل الدخول',
        'back_home' => 'العودة للصفحة الرئيسية'
    ],
    'en' => [
        'login_title' => 'Login',
        'welcome_back' => 'Welcome Back',
        'email_address' => 'Email Address',
        'password' => 'Password',
        'sign_in' => 'Sign In',
        'back_home' => 'Back to Home'
    ]
];

$t = $translations[$currentLang];
?>

<!DOCTYPE html>
<html lang="<?= $currentLang ?>" dir="<?= $currentLang === 'ar' ? 'rtl' : 'ltr' ?>">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title><?= $t['login_title'] ?> - Season Expo Kuwait</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">

    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
    </style>
</head>
<body class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
    <!-- Navigation -->
    <nav class="fixed top-0 left-0 right-0 bg-white shadow-sm z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="/homepage-fixed.php?lang=<?= $currentLang ?>">
                        <img src="/images/logo.png" alt="Season Expo Kuwait" style="height: 40px; width: auto;" class="hover:opacity-80 transition-opacity">
                    </a>
                </div>
                <div class="flex items-center space-x-reverse space-x-4">
                    <a href="/homepage-fixed.php?lang=<?= $currentLang ?>" class="text-gray-600 hover:text-gray-900"><?= $t['back_home'] ?></a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="pt-16 min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8">
            <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                <!-- Header -->
                <div class="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6 text-center">
                    <h1 class="text-2xl font-bold"><?= $t['login_title'] ?></h1>
                    <p class="text-blue-100 mt-2"><?= $t['welcome_back'] ?></p>
                </div>
                
                <!-- Form -->
                <div class="p-6">
                    <?php if (isset($loginError)): ?>
                    <div class="mb-4 bg-red-50 border border-red-200 rounded-lg p-4">
                        <div class="flex items-center">
                            <div class="text-red-600 text-xl mr-3">❌</div>
                            <div>
                                <h3 class="font-semibold text-red-800">خطأ في تسجيل الدخول</h3>
                                <p class="text-red-700"><?= $loginError ?></p>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                    
                    <?php if ($redirectUrl): ?>
                    <div class="mb-4 bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <div class="flex items-center">
                            <div class="text-blue-600 text-xl mr-3">ℹ️</div>
                            <div>
                                <h3 class="font-semibold text-blue-800">إعادة التوجيه</h3>
                                <p class="text-blue-700">سيتم توجيهك لصفحة الحجز بعد تسجيل الدخول</p>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                    
                    <form class="space-y-6" action="<?= $_SERVER['PHP_SELF'] . ($redirectUrl ? '?redirect=' . urlencode($redirectUrl) : '') ?>" method="POST">
                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                                <?= $t['email_address'] ?>
                            </label>
                            <input id="email" name="email" type="email" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                   placeholder="<?= $t['email_address'] ?>">
                        </div>

                        <div>
                            <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                                <?= $t['password'] ?>
                            </label>
                            <input id="password" name="password" type="password" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                   placeholder="<?= $t['password'] ?>">
                        </div>

                        <div>
                            <button type="submit" class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                <?= $t['sign_in'] ?>
                            </button>
                        </div>
                    </form>
                    
                    <!-- Demo Info -->
                    <div class="mt-6 p-4 bg-gray-50 rounded-lg">
                        <h3 class="font-semibold text-gray-700 mb-2">للاختبار:</h3>
                        <p class="text-sm text-gray-600">يمكنك استخدام أي بريد إلكتروني وكلمة مرور للدخول</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>

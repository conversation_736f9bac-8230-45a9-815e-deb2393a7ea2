<?php
echo "<h1>🚨 Emergency Extraction Guide</h1>";

$basePath = dirname(__DIR__);
$zipFile = $basePath . '/season_expo.zip';

echo "<div style='background: #ffeeee; padding: 20px; border: 2px solid #ff0000; border-radius: 10px;'>";
echo "<h2>🚨 CRITICAL ISSUE DETECTED</h2>";
echo "<p><strong>All Laravel files are missing from your server!</strong></p>";
echo "<p>This is why you're getting the 500 error.</p>";
echo "</div>";

if (file_exists($zipFile)) {
    $zipSize = round(filesize($zipFile) / 1024 / 1024, 1);
    echo "<div style='background: #ffffcc; padding: 20px; border: 2px solid #ffcc00; border-radius: 10px; margin: 20px 0;'>";
    echo "<h2>✅ SOLUTION AVAILABLE</h2>";
    echo "<p>Your season_expo.zip file ({$zipSize} MB) is still on the server.</p>";
    echo "<p><strong>You need to extract it immediately!</strong></p>";
    echo "</div>";
    
    echo "<h2>📋 STEP-BY-STEP EXTRACTION:</h2>";
    echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 5px;'>";
    echo "<h3>Method 1: Hostinger File Manager (Recommended)</h3>";
    echo "<ol style='font-size: 16px; line-height: 1.6;'>";
    echo "<li><strong>Open Hostinger Control Panel</strong></li>";
    echo "<li><strong>Go to File Manager</strong></li>";
    echo "<li><strong>Navigate to:</strong> /home/<USER>/domains/myapps.fjt-q8.com/</li>";
    echo "<li><strong>Find season_expo.zip</strong> ({$zipSize} MB)</li>";
    echo "<li><strong>Right-click on season_expo.zip</strong></li>";
    echo "<li><strong>Select 'Extract' or 'Unzip'</strong></li>";
    echo "<li><strong>Choose 'Extract to current directory'</strong></li>";
    echo "<li><strong>Wait for extraction to complete</strong> (may take 2-5 minutes)</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<h2>✅ WHAT EXTRACTION WILL CREATE:</h2>";
    echo "<ul style='font-size: 16px;'>";
    echo "<li>✅ <strong>vendor/</strong> - Laravel framework files</li>";
    echo "<li>✅ <strong>bootstrap/</strong> - Laravel bootstrap</li>";
    echo "<li>✅ <strong>app/</strong> - Season Expo controllers and models</li>";
    echo "<li>✅ <strong>routes/</strong> - Season Expo routes</li>";
    echo "<li>✅ <strong>resources/</strong> - Vue.js components</li>";
    echo "<li>✅ <strong>config/</strong> - Configuration files</li>";
    echo "<li>✅ <strong>database/</strong> - Database files</li>";
    echo "<li>✅ <strong>composer.json</strong> - Project configuration</li>";
    echo "</ul>";
    
    echo "<h2>🔄 AFTER EXTRACTION:</h2>";
    echo "<ol style='font-size: 16px;'>";
    echo "<li><strong>Copy public folder contents</strong> to public_html/</li>";
    echo "<li><strong>Create .env file</strong> with database credentials</li>";
    echo "<li><strong>Test the application</strong></li>";
    echo "</ol>";
    
} else {
    echo "<div style='background: #ffeeee; padding: 20px; border: 2px solid #ff0000; border-radius: 10px;'>";
    echo "<h2>❌ ZIP FILE MISSING</h2>";
    echo "<p>The season_expo.zip file is not found on the server.</p>";
    echo "<p><strong>You need to re-upload your complete Season Expo project.</strong></p>";
    echo "</div>";
}

echo "<h2>🧪 VERIFICATION:</h2>";
echo "<p>After extraction, run this test:</p>";
echo "<p><a href='check-server-contents.php' style='background: #007cba; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>Check Server Contents Again</a></p>";

echo "<h2>⚠️ IMPORTANT NOTES:</h2>";
echo "<ul>";
echo "<li>The extraction may take several minutes due to the large vendor/ folder</li>";
echo "<li>Don't refresh or close the browser during extraction</li>";
echo "<li>After extraction, you'll need to set up the .env file again</li>";
echo "<li>The database is still intact and working</li>";
echo "</ul>";
?>

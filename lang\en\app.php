<?php

return [
    // Navigation
    'site_name' => 'Season Expo',
    'exhibitions' => 'Exhibitions',
    'booths' => 'Booths',
    'dashboard' => 'Dashboard',
    'sign_in' => 'Sign In',
    'get_started' => 'Get Started',
    'register' => 'Register',
    'login' => 'Login',
    'logout' => 'Logout',
    'search' => 'Search',
    'browse_exhibitions' => 'Browse Exhibitions',
    'search_booths' => 'Search Booths',

    // Homepage
    'hero_title' => 'Find & Book Exhibition Booths',
    'hero_subtitle' => 'Discover amazing exhibitions and book your perfect booth space. Connect with your audience and grow your business.',
    'welcome_title' => 'Welcome to Season Expo!',
    'welcome_subtitle' => 'Your exhibition and booth booking platform is ready. The database has been set up with sample data.',
    'featured_exhibitions' => 'Featured Exhibitions',
    'featured_exhibitions_subtitle' => 'Discover our handpicked selection of premium exhibitions offering the best booth opportunities.',
    'exhibition_categories' => 'Exhibition Categories',
    'exhibition_categories_subtitle' => 'Explore exhibitions by category to find the perfect match for your business.',
    'ready_to_showcase' => 'Ready to Showcase Your Business?',
    'ready_to_showcase_subtitle' => 'Join thousands of exhibitors who have found success through our platform. Book your booth today!',

    // Statistics
    'total_exhibitions' => 'Total Exhibitions',
    'upcoming_exhibitions' => 'Upcoming Events',
    'total_booths' => 'Total Booths',
    'available_booths' => 'Available Booths',

    // Buttons
    'view_details' => 'View Details',
    'view_all_exhibitions' => 'View All Exhibitions',
    'go_to_dashboard' => 'Go to Dashboard',
    'back_to_homepage' => 'Back to Homepage',

    // Footer
    'copyright' => '© 2024 Season Expo. All rights reserved.',
    'secure_login' => 'Secure login powered by Season Expo',

    // Categories
    'technology_innovation' => 'Technology & Innovation',
    'healthcare_medical' => 'Healthcare & Medical',
    'food_beverage' => 'Food & Beverage',
    'fashion_lifestyle' => 'Fashion & Lifestyle',
    'automotive' => 'Automotive',
    'education_training' => 'Education & Training',
    'real_estate_construction' => 'Real Estate & Construction',
    'travel_tourism' => 'Travel & Tourism',
    'manufacturing' => 'Manufacturing',
    'finance_banking' => 'Finance & Banking',
    'other' => 'Other',

    // Countries
    'united_arab_emirates' => 'United Arab Emirates',
    'saudi_arabia' => 'Saudi Arabia',
    'kuwait' => 'Kuwait',
    'qatar' => 'Qatar',
    'bahrain' => 'Bahrain',
    'oman' => 'Oman',
    'egypt' => 'Egypt',
    'jordan' => 'Jordan',
    'lebanon' => 'Lebanon',

    // Language
    'language' => 'Language',
    'arabic' => 'العربية',
    'english' => 'English',
    'switch_to_english' => 'English',
    'switch_to_arabic' => 'العربية',

    // Dashboard
    'dashboard_title' => 'Dashboard',
    'welcome_to_season_expo' => 'Welcome to Season Expo!',
    'dashboard_subtitle' => 'Your exhibition and booth booking platform is ready. Start exploring exhibitions and book your perfect booth space.',
    'view_homepage' => 'View Homepage',
    'quick_stats' => 'Quick Stats',
    'active_exhibitions' => 'Active Exhibitions',
    'my_bookings' => 'My Bookings',
    'quick_actions' => 'Quick Actions',
    'find_next_event' => 'Find your next event',
    'find_perfect_space' => 'Find the perfect space',
    'manage_reservations' => 'Manage your reservations',
    'get_support' => 'Get Support',
    'need_help' => 'Need help?',

    // Media Management
    'media_management' => 'Media Management',
    'manage_all_images' => 'Manage all images and media',
    'upload_new_media' => 'Upload New Media',
    'upload_media_description' => 'Upload images for exhibitions, sliders, and layout',
    'select_file' => 'Select File',
    'upload_file' => 'Upload a file',
    'or_drag_and_drop' => 'or drag and drop',
    'up_to' => 'up to',
    'enter_media_name' => 'Enter media name',
    'select_type' => 'Select type',
    'enter_category' => 'Enter category',
    'enter_sort_order' => 'Enter sort order',
    'additional_information' => 'Additional Information',
    'enter_title' => 'Enter title',
    'enter_description' => 'Enter description',
    'alt_text' => 'Alt Text',
    'enter_alt_text' => 'Enter alt text',
    'alt_text_description' => 'Image description for accessibility purposes',
    'upload_media' => 'Upload Media',
    'uploading' => 'Uploading',
    'all_types' => 'All Types',
    'filter_by_category' => 'Filter by category',
    'clear_filters' => 'Clear Filters',
    'no_media_found' => 'No media found',
    'upload_first_media' => 'Upload your first media to get started',
    'confirm_delete' => 'Are you sure you want to delete this media?',
    'active' => 'Active',
    'inactive' => 'Inactive',
    'edit' => 'Edit',
    'view' => 'View',
    'delete' => 'Delete',
    'cancel' => 'Cancel',
    'type' => 'Type',
    'category' => 'Category',
    'sort_order' => 'Sort Order',
    'title' => 'Title',
    'description' => 'Description',

    // Digital Signatures
    'digital_signatures' => 'Digital Signatures',
    'my_signatures' => 'My Signatures',
    'create_digital_signature' => 'Create Digital Signature',
    'create_signature_description' => 'Create a secure digital signature for documents and contracts',
    'document_type' => 'Document Type',
    'document_id' => 'Document ID',
    'enter_document_id' => 'Enter document ID',
    'signer_name' => 'Signer Name',
    'signer_email' => 'Signer Email',
    'enter_signer_name' => 'Enter signer name',
    'digital_signature' => 'Digital Signature',
    'draw_signature_instruction' => 'Draw your signature in the box below using your mouse or touch',
    'clear_signature' => 'Clear Signature',
    'create_signature' => 'Create Signature',
    'creating_signature' => 'Creating Signature',
    'please_draw_signature' => 'Please draw your signature first',
    'signature_required' => 'Signature is required',
    'legal_notice' => 'Legal Notice',
    'signature_legal_notice' => 'By creating this digital signature, you agree that it carries the same legal force as a handwritten signature.',
    'manage_digital_signatures' => 'Manage digital signatures',
    'create_new_signature' => 'Create New Signature',
    'verification_status' => 'Verification Status',
    'all_statuses' => 'All Statuses',
    'verified' => 'Verified',
    'pending_verification' => 'Pending Verification',
    'signed_date' => 'Signed Date',
    'actions' => 'Actions',
    'certificate' => 'Certificate',
    'confirm_delete_signature' => 'Are you sure you want to delete this signature?',
    'no_signatures_found' => 'No signatures found',
    'create_first_signature' => 'Create your first digital signature',
    'signature_verification' => 'Signature Verification',
    'signature_verified' => 'Signature Verified',
    'verification_failed' => 'Verification Failed',
    'signature_details' => 'Signature Details',
    'signature_hash' => 'Signature Hash',
    'verification_token' => 'Verification Token',
    'security_verified' => 'Security Verified',
    'signature_security_notice' => 'This digital signature has been verified for authenticity and integrity using advanced cryptographic techniques.',
    'verification_error' => 'Verification Error',
    'verification_error_notice' => 'This signature could not be verified. The token may be invalid or expired.',
    'back_to_home' => 'Back to Home',
    'print_verification' => 'Print Verification',
    'view_signature_information' => 'View signature information',
    'download_certificate' => 'Download Certificate',
    'verified_date' => 'Verified Date',
    'technical_details' => 'Technical Details',
    'copy' => 'Copy',
    'verification_url' => 'Verification URL',
    'verify' => 'Verify',
    'signer_ip' => 'Signer IP Address',
    'security_information' => 'Security Information',
    'signature_security_info' => 'This digital signature is protected by the following security features:',
    'cryptographic_hash_protection' => 'Cryptographic hash protection',
    'timestamp_verification' => 'Timestamp verification',
    'ip_address_logging' => 'IP address logging',
    'tamper_proof_storage' => 'Tamper-proof storage',
    'copied_to_clipboard' => 'Copied to clipboard',

    // Bookings
    'manage_bookings_description' => 'Manage your exhibition booth bookings and track their status',
    'filter_by_status' => 'Filter by Status',
    'all_status' => 'All Status',
    'pending' => 'Pending',
    'confirmed' => 'Confirmed',
    'cancelled' => 'Cancelled',
    'completed' => 'Completed',
    'booth_details' => 'Booth Details',
    'payment_status' => 'Payment Status',
    'booking_status' => 'Booking Status',
    'booked_on' => 'Booked on',
    'confirmed_on' => 'Confirmed on',
    'cancelled_on' => 'Cancelled on',
    'pending_confirmation' => 'Pending confirmation',
    'paid_on' => 'Paid on',
    'view_exhibition' => 'View Exhibition',
    'complete_payment' => 'Complete Payment',
    'cancel_booking' => 'Cancel Booking',
    'confirm_cancel_booking' => 'Are you sure you want to cancel this booking?',
    'no_bookings_yet' => 'No bookings yet',
    'no_bookings_description' => 'You haven\'t made any booth bookings yet. Start by browsing available exhibitions.',
    'processing' => 'Processing',
    'failed' => 'Failed',
    'refunded' => 'Refunded',
];

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Exhibition extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'slug',
        'description',
        'short_description',
        'category_id',
        'organizer_id',
        'venue_name',
        'venue_address',
        'city',
        'country',
        'latitude',
        'longitude',
        'start_date',
        'end_date',
        'registration_start',
        'registration_end',
        'featured_image',
        'gallery',
        'status',
        'is_featured',
        'booth_price_from',
        'currency',
        'contact_info',
        'social_links',
        'terms_and_conditions',
        'max_booths',
    ];

    protected $casts = [
        'start_date' => 'datetime',
        'end_date' => 'datetime',
        'registration_start' => 'datetime',
        'registration_end' => 'datetime',
        'is_featured' => 'boolean',
        'gallery' => 'array',
        'contact_info' => 'array',
        'social_links' => 'array',
        'booth_price_from' => 'decimal:2',
        'latitude' => 'decimal:8',
        'longitude' => 'decimal:8',
    ];

    /**
     * Get the route key for the model.
     * This makes the model use 'slug' instead of 'id' in routes
     */
    /**
     * Get formatted price with KWD currency
     */
    public function getFormattedPriceAttribute()
    {
        return number_format($this->booth_price_from, 3) . ' ' . $this->currency;
    }

    /**
    * Get price with KWD symbol
    */
    public function getPriceWithSymbolAttribute()
    {
        $symbol = $this->currency === 'KWD' ? 'د.ك' : '$';
        return $symbol . ' ' . number_format($this->booth_price_from, 3);
    }
    public function getRouteKeyName()
    {
        return 'slug';
    }

    /**
     * Get the category that owns the exhibition.
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    /**
     * Get the organizer that owns the exhibition.
     */
    public function organizer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'organizer_id');
    }

    /**
     * Get the booths for the exhibition.
     */
    public function booths(): HasMany
    {
        return $this->hasMany(Booth::class);
    }

    /**
     * Get the bookings for the exhibition.
     */
    public function bookings(): HasMany
    {
        return $this->hasMany(Booking::class);
    }

    /**
     * Scope a query to only include published exhibitions.
     */
    public function scopePublished($query)
    {
        return $query->where('status', 'published');
    }

    /**
     * Scope a query to only include featured exhibitions.
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope a query to only include upcoming exhibitions.
     */
    public function scopeUpcoming($query)
    {
        return $query->where('start_date', '>', now());
    }

    /**
     * Scope a query to only include ongoing exhibitions.
     */
    public function scopeOngoing($query)
    {
        return $query->where('start_date', '<=', now())
                    ->where('end_date', '>=', now());
    }

    /**
     * Get the exhibition's status badge color.
     */
    public function getStatusColorAttribute()
    {
        return match($this->status) {
            'published' => 'green',
            'draft' => 'yellow',
            'cancelled' => 'red',
            default => 'gray',
        };
    }

    /**
     * Get the exhibition's formatted date range.
     */
    public function getDateRangeAttribute()
    {
        if ($this->start_date->format('Y-m-d') === $this->end_date->format('Y-m-d')) {
            return $this->start_date->format('M j, Y');
        }

        if ($this->start_date->format('Y-m') === $this->end_date->format('Y-m')) {
            return $this->start_date->format('M j') . ' - ' . $this->end_date->format('j, Y');
        }

        return $this->start_date->format('M j, Y') . ' - ' . $this->end_date->format('M j, Y');
    }

    /**
     * Get available booths count.
     */
    public function getAvailableBoothsAttribute()
    {
        return $this->max_booths - $this->booths()->where('status', 'booked')->count();
    }

    /**
     * Check if registration is open.
     */
    public function getIsRegistrationOpenAttribute()
    {
        $now = now();
        return $now >= $this->registration_start && $now <= $this->registration_end;
    }

    /**
     * Check if registration is open (method version).
     */
    public function isRegistrationOpen()
    {
        $now = now();
        return $now >= $this->registration_start && $now <= $this->registration_end;
    }

    /**
     * Get the exhibition's main image URL.
     */
    public function getImageUrlAttribute()
    {
        if ($this->featured_image) {
            return asset('storage/' . $this->featured_image);
        }

        return asset('images/exhibition-placeholder.jpg');
    }
}

<?php
// Diagnose Homepage Issues
// This file helps identify why the homepage isn't working

session_start();

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='utf-8'>";
echo "<title>تشخيص مشاكل الصفحة الرئيسية - Season Expo Kuwait</title>";
echo "<script src='https://cdn.tailwindcss.com'></script>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }";
echo "</style>";
echo "</head>";
echo "<body class='bg-gray-50 p-8'>";

echo "<div class='max-w-4xl mx-auto'>";
echo "<h1 class='text-3xl font-bold mb-8' style='color: #2C3E50;'>🔍 تشخيص مشاكل الصفحة الرئيسية</h1>";

// Current status
$currentUrl = $_SERVER['REQUEST_URI'];
$serverName = $_SERVER['SERVER_NAME'];
$documentRoot = $_SERVER['DOCUMENT_ROOT'];

echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>📊 معلومات الخادم</h2>";

echo "<div class='space-y-3 text-sm'>";
echo "<div><strong>الخادم:</strong> <code class='bg-gray-100 px-2 py-1 rounded'>{$serverName}</code></div>";
echo "<div><strong>الرابط الحالي:</strong> <code class='bg-gray-100 px-2 py-1 rounded'>{$currentUrl}</code></div>";
echo "<div><strong>مجلد الموقع:</strong> <code class='bg-gray-100 px-2 py-1 rounded'>{$documentRoot}</code></div>";
echo "<div><strong>إصدار PHP:</strong> <code class='bg-gray-100 px-2 py-1 rounded'>" . phpversion() . "</code></div>";
echo "</div>";
echo "</div>";

// File check
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>📁 فحص الملفات الأساسية</h2>";

$files = [
    'index.php' => 'الصفحة الرئيسية',
    'homepage-fixed.php' => 'الصفحة المحدثة',
    'en.php' => 'الصفحة الإنجليزية',
    '.htaccess' => 'ملف إعادة التوجيه'
];

echo "<div class='space-y-3'>";

foreach ($files as $file => $description) {
    $fullPath = __DIR__ . '/' . $file;
    echo "<div class='flex items-center justify-between p-3 border border-gray-200 rounded-lg'>";
    echo "<div>";
    echo "<h3 class='font-semibold'>{$description}</h3>";
    echo "<p class='text-sm text-gray-600'>/{$file}</p>";
    echo "</div>";
    echo "<div>";
    
    if (file_exists($fullPath)) {
        $size = filesize($fullPath);
        echo "<div class='text-green-600 text-sm'>✅ موجود (" . round($size/1024, 2) . " KB)</div>";
    } else {
        echo "<div class='text-red-600 text-sm'>❌ مفقود</div>";
    }
    
    echo "</div>";
    echo "</div>";
}

echo "</div>";
echo "</div>";

// Test direct access
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>🧪 اختبار الوصول المباشر</h2>";

echo "<div class='space-y-3'>";

$testUrls = [
    '/homepage-fixed.php?lang=ar' => 'الصفحة الرئيسية العربية',
    '/homepage-fixed.php?lang=en' => 'الصفحة الرئيسية الإنجليزية',
    '/login-simple.php?lang=ar' => 'صفحة التسجيل العربية',
    '/admin-slider-management.php' => 'إدارة السلايد'
];

foreach ($testUrls as $url => $description) {
    echo "<div class='flex items-center justify-between p-3 border border-gray-200 rounded-lg'>";
    echo "<div>";
    echo "<h3 class='font-semibold'>{$description}</h3>";
    echo "<p class='text-sm text-gray-600'>{$url}</p>";
    echo "</div>";
    echo "<div>";
    echo "<a href='{$url}' target='_blank' class='text-blue-600 hover:underline text-sm'>🔗 اختبار</a>";
    echo "</div>";
    echo "</div>";
}

echo "</div>";
echo "</div>";

// Error checking
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>⚠️ فحص الأخطاء المحتملة</h2>";

echo "<div class='space-y-4'>";

// Check if index.php has errors
echo "<div class='bg-yellow-50 border border-yellow-200 rounded-lg p-4'>";
echo "<h3 class='font-semibold mb-2'>فحص ملف index.php:</h3>";

if (file_exists(__DIR__ . '/index.php')) {
    $indexContent = file_get_contents(__DIR__ . '/index.php');
    
    if (strpos($indexContent, 'header(') !== false) {
        echo "<div class='text-green-600 text-sm'>✅ يحتوي على إعادة توجيه</div>";
    } else {
        echo "<div class='text-red-600 text-sm'>❌ لا يحتوي على إعادة توجيه</div>";
    }
    
    if (strpos($indexContent, 'session_start') !== false) {
        echo "<div class='text-green-600 text-sm'>✅ يحتوي على session_start</div>";
    } else {
        echo "<div class='text-yellow-600 text-sm'>⚠️ لا يحتوي على session_start</div>";
    }
} else {
    echo "<div class='text-red-600 text-sm'>❌ ملف index.php غير موجود</div>";
}

echo "</div>";

// Check .htaccess
echo "<div class='bg-blue-50 border border-blue-200 rounded-lg p-4'>";
echo "<h3 class='font-semibold mb-2'>فحص ملف .htaccess:</h3>";

if (file_exists(__DIR__ . '/.htaccess')) {
    $htaccessContent = file_get_contents(__DIR__ . '/.htaccess');
    
    if (strpos($htaccessContent, 'RewriteEngine On') !== false) {
        echo "<div class='text-green-600 text-sm'>✅ RewriteEngine مفعل</div>";
    } else {
        echo "<div class='text-red-600 text-sm'>❌ RewriteEngine غير مفعل</div>";
    }
    
    echo "<div class='text-sm text-gray-600 mt-2'>حجم الملف: " . round(filesize(__DIR__ . '/.htaccess')/1024, 2) . " KB</div>";
} else {
    echo "<div class='text-red-600 text-sm'>❌ ملف .htaccess غير موجود</div>";
}

echo "</div>";

echo "</div>";
echo "</div>";

// Solutions
echo "<div class='bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>💡 الحلول المقترحة</h2>";

echo "<div class='space-y-4'>";

echo "<div class='bg-white p-4 rounded-lg border'>";
echo "<h3 class='font-semibold mb-2' style='color: #2C3E50;'>الحل الأول - استخدام الروابط المباشرة:</h3>";
echo "<ul class='text-sm space-y-1 list-disc list-inside ml-4'>";
echo "<li>استخدم <code>/homepage-fixed.php?lang=ar</code> للصفحة العربية</li>";
echo "<li>استخدم <code>/homepage-fixed.php?lang=en</code> للصفحة الإنجليزية</li>";
echo "<li>هذا يضمن عمل الموقع فوراً</li>";
echo "</ul>";
echo "</div>";

echo "<div class='bg-white p-4 rounded-lg border'>";
echo "<h3 class='font-semibold mb-2' style='color: #2C3E50;'>الحل الثاني - إصلاح index.php:</h3>";
echo "<ul class='text-sm space-y-1 list-disc list-inside ml-4'>";
echo "<li>تأكد من رفع ملف index.php المحدث</li>";
echo "<li>تحقق من صلاحيات الملف (644)</li>";
echo "<li>تأكد من عدم وجود أخطاء PHP</li>";
echo "</ul>";
echo "</div>";

echo "<div class='bg-white p-4 rounded-lg border'>";
echo "<h3 class='font-semibold mb-2' style='color: #2C3E50;'>الحل الثالث - تعطيل .htaccess مؤقت<|im_start|>:</h3>";
echo "<ul class='text-sm space-y-1 list-disc list-inside ml-4'>";
echo "<li>أعد تسمية .htaccess إلى .htaccess-backup</li>";
echo "<li>اختبر الموقع بدون إعادة التوجيه</li>";
echo "<li>إذا عمل، فالمشكلة في .htaccess</li>";
echo "</ul>";
echo "</div>";

echo "</div>";
echo "</div>";

// Quick test
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>⚡ اختبار سريع</h2>";

echo "<div class='text-center space-x-reverse space-x-4'>";
echo "<a href='/homepage-fixed.php?lang=ar' class='text-white px-6 py-3 rounded-lg hover:opacity-80' style='background: #2C3E50;'>🏠 الصفحة الرئيسية (مباشر)</a>";
echo "<a href='/' class='bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700'>🌐 الصفحة الرئيسية (نظيف)</a>";
echo "<a href='/en' class='bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700'>🇺🇸 English</a>";
echo "</div>";

echo "<div class='text-center mt-4 text-sm text-gray-600'>";
echo "إذا عمل الرابط الأول ولم يعمل الثاني، فالمشكلة في .htaccess أو index.php";
echo "</div>";

echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>

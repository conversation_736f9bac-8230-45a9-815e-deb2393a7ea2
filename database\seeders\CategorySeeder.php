<?php

namespace Database\Seeders;

use App\Models\Category;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name' => 'Technology & Innovation',
                'slug' => 'technology-innovation',
                'description' => 'Latest technology trends, software, hardware, and innovation showcases',
                'icon' => 'laptop',
                'color' => '#3B82F6',
                'sort_order' => 1,
            ],
            [
                'name' => 'Healthcare & Medical',
                'slug' => 'healthcare-medical',
                'description' => 'Medical equipment, healthcare services, pharmaceutical products',
                'icon' => 'heart',
                'color' => '#EF4444',
                'sort_order' => 2,
            ],
            [
                'name' => 'Food & Beverage',
                'slug' => 'food-beverage',
                'description' => 'Food products, beverages, culinary equipment, and restaurant services',
                'icon' => 'utensils',
                'color' => '#F59E0B',
                'sort_order' => 3,
            ],
            [
                'name' => 'Fashion & Lifestyle',
                'slug' => 'fashion-lifestyle',
                'description' => 'Fashion brands, lifestyle products, beauty, and wellness',
                'icon' => 'shirt',
                'color' => '#EC4899',
                'sort_order' => 4,
            ],
            [
                'name' => 'Automotive',
                'slug' => 'automotive',
                'description' => 'Cars, motorcycles, automotive parts, and transportation solutions',
                'icon' => 'car',
                'color' => '#6B7280',
                'sort_order' => 5,
            ],
            [
                'name' => 'Education & Training',
                'slug' => 'education-training',
                'description' => 'Educational institutions, training programs, and learning solutions',
                'icon' => 'graduation-cap',
                'color' => '#10B981',
                'sort_order' => 6,
            ],
            [
                'name' => 'Real Estate & Construction',
                'slug' => 'real-estate-construction',
                'description' => 'Property development, construction materials, and architectural services',
                'icon' => 'building',
                'color' => '#8B5CF6',
                'sort_order' => 7,
            ],
            [
                'name' => 'Travel & Tourism',
                'slug' => 'travel-tourism',
                'description' => 'Travel agencies, hotels, tourism services, and destination marketing',
                'icon' => 'plane',
                'color' => '#06B6D4',
                'sort_order' => 8,
            ],
        ];

        foreach ($categories as $category) {
            Category::create($category);
        }
    }
}

<?php
// Test Redirect - Simple Test
// This file tests if redirects work

session_start();

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='utf-8'>";
echo "<title>اختبار إعادة التوجيه - Season Expo Kuwait</title>";
echo "<script src='https://cdn.tailwindcss.com'></script>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }";
echo "</style>";
echo "</head>";
echo "<body class='bg-gray-50 p-8'>";

echo "<div class='max-w-4xl mx-auto'>";
echo "<h1 class='text-3xl font-bold mb-8' style='color: #2C3E50;'>🔄 اختبار إعادة التوجيه</h1>";

// Current info
$currentUrl = $_SERVER['REQUEST_URI'];
$currentLang = $_SESSION['language'] ?? 'غير محدد';

echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>📊 الحالة الحالية</h2>";
echo "<div class='space-y-2'>";
echo "<div><strong>الرابط الحالي:</strong> <code class='bg-gray-100 px-2 py-1 rounded'>{$currentUrl}</code></div>";
echo "<div><strong>اللغة المحفوظة:</strong> <code class='bg-gray-100 px-2 py-1 rounded'>{$currentLang}</code></div>";
echo "</div>";
echo "</div>";

// Test buttons
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>🧪 اختبار إعادة التوجيه</h2>";

echo "<div class='grid grid-cols-1 md:grid-cols-2 gap-4'>";

// Test index.php redirect
echo "<div class='border border-gray-200 rounded-lg p-4'>";
echo "<h3 class='font-semibold mb-3'>اختبار index.php:</h3>";
echo "<div class='space-y-2'>";
echo "<a href='/' class='block w-full text-center px-4 py-2 border border-blue-500 text-blue-600 rounded hover:bg-blue-50'>🏠 اختبار الصفحة الرئيسية</a>";
echo "<p class='text-xs text-gray-500 mt-2'>يجب أن يوجه إلى homepage-fixed.php</p>";
echo "</div>";
echo "</div>";

// Test language switching
echo "<div class='border border-gray-200 rounded-lg p-4'>";
echo "<h3 class='font-semibold mb-3'>اختبار تغيير اللغة:</h3>";
echo "<div class='space-y-2'>";
echo "<a href='/switch-language.php?lang=ar' class='block w-full text-center px-4 py-2 border border-green-500 text-green-600 rounded hover:bg-green-50'>🇰🇼 العربية</a>";
echo "<a href='/switch-language.php?lang=en' class='block w-full text-center px-4 py-2 border border-green-500 text-green-600 rounded hover:bg-green-50'>🇺🇸 English</a>";
echo "</div>";
echo "</div>";

echo "</div>";
echo "</div>";

// Direct links test
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>🔗 اختبار الروابط المباشرة</h2>";

$directLinks = [
    '/homepage-fixed.php?lang=ar' => 'الصفحة الرئيسية العربية',
    '/homepage-fixed.php?lang=en' => 'الصفحة الرئيسية الإنجليزية',
    '/login-simple.php?lang=ar' => 'صفحة التسجيل العربية',
    '/admin-slider-management.php' => 'إدارة السلايد'
];

echo "<div class='grid grid-cols-1 md:grid-cols-2 gap-3'>";

foreach ($directLinks as $url => $description) {
    echo "<a href='{$url}' target='_blank' class='block text-center px-4 py-2 border border-gray-300 rounded hover:bg-gray-50'>";
    echo "<div class='font-semibold'>{$description}</div>";
    echo "<div class='text-xs text-gray-500'>{$url}</div>";
    echo "</a>";
}

echo "</div>";
echo "</div>";

// Status check
echo "<div class='bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>📋 تحليل المشكلة</h2>";

echo "<div class='space-y-4'>";

echo "<div class='bg-white p-4 rounded-lg border'>";
echo "<h3 class='font-semibold mb-2' style='color: #2C3E50;'>إذا وصلت لهذه الصفحة:</h3>";
echo "<ul class='text-sm space-y-1 list-disc list-inside ml-4'>";
echo "<li>✅ ملف test-redirect.php يعمل</li>";
echo "<li>✅ PHP يعمل بشكل صحيح</li>";
echo "<li>✅ الخادم يستجيب للطلبات</li>";
echo "</ul>";
echo "</div>";

echo "<div class='bg-white p-4 rounded-lg border'>";
echo "<h3 class='font-semibold mb-2' style='color: #2C3E50;'>إذا لم تعمل الصفحة الرئيسية (/):</h3>";
echo "<ul class='text-sm space-y-1 list-disc list-inside ml-4'>";
echo "<li>❌ مشكلة في ملف index.php</li>";
echo "<li>❌ مشكلة في إعادة التوجيه</li>";
echo "<li>❌ مشكلة في .htaccess</li>";
echo "</ul>";
echo "</div>";

echo "<div class='bg-white p-4 rounded-lg border'>";
echo "<h3 class='font-semibold mb-2' style='color: #2C3E50;'>الحل السريع:</h3>";
echo "<ul class='text-sm space-y-1 list-disc list-inside ml-4'>";
echo "<li>استخدم الروابط المباشرة أعلاه</li>";
echo "<li>أضف الروابط للقائمة الرئيسية</li>";
echo "<li>اختبر كل رابط للتأكد من عمله</li>";
echo "</ul>";
echo "</div>";

echo "</div>";
echo "</div>";

// Quick actions
echo "<div class='text-center space-x-reverse space-x-4'>";
echo "<a href='/homepage-fixed.php?lang=ar' class='text-white px-6 py-3 rounded-lg hover:opacity-80' style='background: #2C3E50;'>🏠 الصفحة الرئيسية (مضمون)</a>";
echo "<a href='/diagnose-homepage.php' class='bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700'>🔍 التشخيص</a>";
echo "<a href='/admin-slider-management.php' class='bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700'>🖼️ إدارة السلايد</a>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>

<?php
echo "<h1>Real Directory Check</h1>";

$basePath = dirname(__DIR__);
echo "Base path: {$basePath}<br><br>";

echo "<h2>Root Directory Contents:</h2>";
if (is_dir($basePath)) {
    $items = scandir($basePath);
    if ($items === false) {
        echo "❌ Cannot read directory<br>";
    } else {
        foreach ($items as $item) {
            if ($item != '.' && $item != '..') {
                $fullPath = $basePath . '/' . $item;
                if (is_dir($fullPath)) {
                    echo "📁 DIR: {$item}<br>";
                    
                    // If it's vendor, check inside
                    if ($item === 'vendor') {
                        echo "&nbsp;&nbsp;&nbsp;📁 Checking vendor contents...<br>";
                        $vendorItems = scandir($fullPath);
                        $count = count($vendorItems) - 2; // exclude . and ..
                        echo "&nbsp;&nbsp;&nbsp;📊 Vendor has {$count} items<br>";
                        
                        // Check for autoload.php specifically
                        $autoloadPath = $fullPath . '/autoload.php';
                        if (file_exists($autoloadPath)) {
                            echo "&nbsp;&nbsp;&nbsp;✅ autoload.php EXISTS (size: " . filesize($autoloadPath) . " bytes)<br>";
                        } else {
                            echo "&nbsp;&nbsp;&nbsp;❌ autoload.php MISSING<br>";
                        }
                    }
                    
                    // If it's bootstrap, check inside
                    if ($item === 'bootstrap') {
                        echo "&nbsp;&nbsp;&nbsp;📁 Checking bootstrap contents...<br>";
                        $bootstrapItems = scandir($fullPath);
                        foreach ($bootstrapItems as $bItem) {
                            if ($bItem != '.' && $bItem != '..') {
                                echo "&nbsp;&nbsp;&nbsp;📄 {$bItem}<br>";
                            }
                        }
                        
                        // Check for app.php specifically
                        $appPath = $fullPath . '/app.php';
                        if (file_exists($appPath)) {
                            echo "&nbsp;&nbsp;&nbsp;✅ app.php EXISTS (size: " . filesize($appPath) . " bytes)<br>";
                        } else {
                            echo "&nbsp;&nbsp;&nbsp;❌ app.php MISSING<br>";
                        }
                    }
                    
                } else {
                    $size = filesize($fullPath);
                    echo "📄 FILE: {$item} ({$size} bytes)<br>";
                }
            }
        }
    }
} else {
    echo "❌ Base directory doesn't exist or not accessible<br>";
}

echo "<h2>Direct File Tests:</h2>";

// Test specific paths
$testPaths = [
    'vendor/autoload.php',
    'bootstrap/app.php',
    'app/Http/Kernel.php',
    'composer.json',
    '.env'
];

foreach ($testPaths as $testPath) {
    $fullPath = $basePath . '/' . $testPath;
    echo "Testing: {$fullPath}<br>";
    
    if (file_exists($fullPath)) {
        echo "✅ EXISTS<br>";
    } else {
        echo "❌ MISSING<br>";
    }
    echo "<br>";
}

echo "<h2>Public_html Contents:</h2>";
$publicItems = scandir(__DIR__);
foreach ($publicItems as $item) {
    if ($item != '.' && $item != '..') {
        $fullPath = __DIR__ . '/' . $item;
        if (is_dir($fullPath)) {
            echo "📁 DIR: {$item}<br>";
        } else {
            $size = filesize($fullPath);
            echo "📄 FILE: {$item} ({$size} bytes)<br>";
        }
    }
}
?>

<script setup>
import { Head, <PERSON> } from '@inertiajs/vue3';
import LanguageSwitcher from '@/components/LanguageSwitcher.vue';

const props = defineProps({
    success: Boolean,
    signature: Object,
    message: String,
    locale: String,
    translations: Object,
});

const t = (key) => {
    return props.translations?.app?.[key] || key;
};

const isRTL = props.locale === 'ar';

const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString(props.locale === 'ar' ? 'ar-SA' : 'en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
};
</script>

<template>
    <Head :title="t('signature_verification')" />

    <div class="min-h-screen bg-gray-50" :dir="isRTL ? 'rtl' : 'ltr'">
        <!-- Fixed Navigation -->
        <nav class="fixed top-0 left-0 right-0 fixed-nav z-50">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <div class="flex items-center">
                        <Link href="/" class="text-2xl font-bold text-blue-600 hover:text-blue-700 transition-colors">
                            {{ t('site_name') }}
                        </Link>
                    </div>
                    <div class="flex items-center" :class="isRTL ? 'space-x-reverse space-x-4' : 'space-x-4'">
                        <LanguageSwitcher :current-locale="locale" />
                        <Link href="/" class="nav-link text-gray-600">{{ t('view_homepage') }}</Link>
                    </div>
                </div>
            </div>
        </nav>

        <div class="py-12 pt-24">
            <div class="mx-auto max-w-4xl sm:px-6 lg:px-8">
                <!-- Verification Result -->
                <div class="bg-white rounded-lg shadow-sm p-8">
                    <!-- Success State -->
                    <div v-if="success" class="text-center">
                        <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-6">
                            <svg class="h-8 w-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                            </svg>
                        </div>
                        <h1 class="text-3xl font-bold text-gray-900 mb-4">{{ t('signature_verified') }}</h1>
                        <p class="text-lg text-gray-600 mb-8">{{ message }}</p>

                        <!-- Signature Details -->
                        <div v-if="signature" class="bg-gray-50 rounded-lg p-6 text-left">
                            <h2 class="text-xl font-semibold text-gray-900 mb-4">{{ t('signature_details') }}</h2>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">{{ t('document_type') }}</label>
                                    <p class="text-sm text-gray-900">{{ signature.document_type }}</p>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">{{ t('document_id') }}</label>
                                    <p class="text-sm text-gray-900">{{ signature.document_id }}</p>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">{{ t('signer_name') }}</label>
                                    <p class="text-sm text-gray-900">{{ signature.signer_name }}</p>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">{{ t('signer_email') }}</label>
                                    <p class="text-sm text-gray-900">{{ signature.signer_email }}</p>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">{{ t('signed_date') }}</label>
                                    <p class="text-sm text-gray-900">{{ formatDate(signature.signed_at) }}</p>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">{{ t('verification_status') }}</label>
                                    <span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">
                                        {{ t('verified') }}
                                    </span>
                                </div>
                            </div>

                            <!-- Signature Hash -->
                            <div class="mt-6">
                                <label class="block text-sm font-medium text-gray-700 mb-1">{{ t('signature_hash') }}</label>
                                <p class="text-xs text-gray-600 font-mono bg-gray-100 p-2 rounded break-all">
                                    {{ signature.signature_hash }}
                                </p>
                            </div>

                            <!-- Verification Token -->
                            <div class="mt-4">
                                <label class="block text-sm font-medium text-gray-700 mb-1">{{ t('verification_token') }}</label>
                                <p class="text-xs text-gray-600 font-mono bg-gray-100 p-2 rounded break-all">
                                    {{ signature.verification_token }}
                                </p>
                            </div>
                        </div>

                        <!-- Security Notice -->
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mt-6">
                            <div class="flex">
                                <svg class="w-5 h-5 text-blue-600 mt-0.5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                                </svg>
                                <div>
                                    <h3 class="text-sm font-medium text-blue-800">{{ t('security_verified') }}</h3>
                                    <p class="text-sm text-blue-700 mt-1">
                                        {{ t('signature_security_notice') }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Error State -->
                    <div v-else class="text-center">
                        <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 mb-6">
                            <svg class="h-8 w-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </div>
                        <h1 class="text-3xl font-bold text-gray-900 mb-4">{{ t('verification_failed') }}</h1>
                        <p class="text-lg text-gray-600 mb-8">{{ message }}</p>

                        <!-- Error Notice -->
                        <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                            <div class="flex">
                                <svg class="w-5 h-5 text-red-600 mt-0.5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                                </svg>
                                <div>
                                    <h3 class="text-sm font-medium text-red-800">{{ t('verification_error') }}</h3>
                                    <p class="text-sm text-red-700 mt-1">
                                        {{ t('verification_error_notice') }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Actions -->
                    <div class="flex justify-center mt-8 space-x-4">
                        <Link 
                            href="/"
                            class="bg-gray-100 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-200 transition-colors"
                        >
                            {{ t('back_to_home') }}
                        </Link>
                        
                        <button 
                            @click="window.print()"
                            class="btn-primary bg-blue-600 text-white px-6 py-2 rounded-lg font-semibold hover:bg-blue-700"
                        >
                            {{ t('print_verification') }}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<style>
@media print {
    .fixed-nav,
    .space-x-4,
    button {
        display: none !important;
    }
    
    .bg-gray-50 {
        background: white !important;
    }
    
    .shadow-sm {
        box-shadow: none !important;
    }
}
</style>

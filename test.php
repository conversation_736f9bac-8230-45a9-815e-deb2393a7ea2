<?php
// Basic PHP test file
echo "PHP is working!<br>";
echo "Current time: " . date('Y-m-d H:i:s') . "<br>";
echo "PHP Version: " . phpversion() . "<br>";

// Test if Laravel files exist
if (file_exists('app/Http/Kernel.php')) {
    echo "Laravel files found<br>";
} else {
    echo "Laravel files NOT found<br>";
}

// Test if vendor directory exists
if (file_exists('vendor/autoload.php')) {
    echo "Vendor directory found<br>";
} else {
    echo "Vendor directory NOT found - This is likely the problem!<br>";
}

// Test if .env file exists
if (file_exists('.env')) {
    echo ".env file found<br>";
} else {
    echo ".env file NOT found<br>";
}

// Test basic Laravel bootstrap
try {
    if (file_exists('vendor/autoload.php')) {
        require_once 'vendor/autoload.php';
        echo "Autoload successful<br>";
        
        if (file_exists('bootstrap/app.php')) {
            echo "Bootstrap file found<br>";
        } else {
            echo "Bootstrap file NOT found<br>";
        }
    }
} catch (Exception $e) {
    echo "Error loading Laravel: " . $e->getMessage() . "<br>";
}

phpinfo();
?>

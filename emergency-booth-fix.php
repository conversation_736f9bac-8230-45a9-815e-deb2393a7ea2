<?php
// Emergency Booth Fix - Immediate Solution
// This creates a fixed version of the exhibitions page

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='utf-8'>";
echo "<title>إصلاح طارئ للأجنحة - Season Expo Kuwait</title>";
echo "<script src='https://cdn.tailwindcss.com'></script>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }";
echo "</style>";
echo "</head>";
echo "<body class='bg-gray-50 p-8'>";

echo "<div class='max-w-4xl mx-auto'>";
echo "<h1 class='text-3xl font-bold mb-8' style='color: #2C3E50;'>🚨 إصلاح طارئ للأجنحة</h1>";

// Option 1: Direct JavaScript injection
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>🔧 الحل الأول: إضافة JavaScript مباشرة</h2>";
echo "<p class='mb-4'>انسخ هذا الكود وألصقه في Console المتصفح (F12 → Console):</p>";

echo "<div class='bg-gray-100 p-4 rounded-lg mb-4'>";
echo "<pre class='text-xs overflow-x-auto'>";
echo htmlspecialchars("// إصلاح فوري للأجنحة
document.querySelectorAll('div[style*=\"background: #2C3E50\"]').forEach(function(booth) {
    if (booth.textContent.includes('KWD') || booth.textContent.includes('متاح')) {
        booth.style.setProperty('background', 'linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)', 'important');
        booth.style.setProperty('border', '2px solid #2C3E50', 'important');
        booth.querySelectorAll('h3, p, div').forEach(function(text) {
            if (!text.closest('a')) {
                text.style.setProperty('color', '#2C3E50', 'important');
                if (text.textContent.includes('KWD')) {
                    text.style.setProperty('color', '#E74C3C', 'important');
                    text.style.setProperty('font-weight', 'bold', 'important');
                }
            }
        });
        booth.querySelectorAll('span').forEach(function(badge) {
            if (badge.textContent.includes('متاح')) {
                badge.style.setProperty('background', '#27AE60', 'important');
                badge.style.setProperty('color', 'white', 'important');
            }
        });
        console.log('✅ تم إصلاح جناح');
    }
});
console.log('🎉 تم إصلاح جميع الأجنحة!');");
echo "</pre>";
echo "</div>";

echo "<button onclick='runFix()' class='text-white px-6 py-3 rounded-lg hover:opacity-80' style='background: #2C3E50;'>🚀 تشغيل الإصلاح الآن</button>";
echo "</div>";

// Option 2: Bookmarklet
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>📌 الحل الثاني: Bookmarklet</h2>";
echo "<p class='mb-4'>اسحب هذا الرابط إلى شريط المفضلة، ثم اضغط عليه في صفحة المعرض:</p>";

$bookmarkletCode = "javascript:(function(){document.querySelectorAll('div[style*=\"background: #2C3E50\"]').forEach(function(booth){if(booth.textContent.includes('KWD')||booth.textContent.includes('متاح')){booth.style.setProperty('background','linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)','important');booth.style.setProperty('border','2px solid #2C3E50','important');booth.querySelectorAll('h3, p, div').forEach(function(text){if(!text.closest('a')){text.style.setProperty('color','#2C3E50','important');if(text.textContent.includes('KWD')){text.style.setProperty('color','#E74C3C','important');text.style.setProperty('font-weight','bold','important');}}});booth.querySelectorAll('span').forEach(function(badge){if(badge.textContent.includes('متاح')){badge.style.setProperty('background','#27AE60','important');badge.style.setProperty('color','white','important');}});console.log('✅ تم إصلاح جناح');}});alert('🎉 تم إصلاح جميع الأجنحة!');})();";

echo "<div class='bg-blue-50 border border-blue-200 rounded-lg p-4'>";
echo "<a href='{$bookmarkletCode}' class='text-blue-600 font-bold text-lg hover:underline'>🔧 إصلاح الأجنحة فوراً</a>";
echo "<p class='text-sm text-gray-600 mt-2'>اسحب هذا الرابط إلى شريط المفضلة</p>";
echo "</div>";
echo "</div>";

// Option 3: CSS Override
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>🎨 الحل الثالث: CSS Override</h2>";
echo "<p class='mb-4'>أضف هذا CSS في أي مكان في صفحة المعرض:</p>";

echo "<div class='bg-gray-100 p-4 rounded-lg'>";
echo "<pre class='text-xs'>";
echo htmlspecialchars('<style>
/* إصلاح طارئ للأجنحة */
div[style*="background: #2C3E50"] {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%) !important;
    border: 2px solid #2C3E50 !important;
    border-radius: 8px !important;
}

div[style*="background: #2C3E50"] h3,
div[style*="background: #2C3E50"] p,
div[style*="background: #2C3E50"] div {
    color: #2C3E50 !important;
}

div[style*="background: #2C3E50"] div:contains("KWD") {
    color: #E74C3C !important;
    font-weight: bold !important;
}

div[style*="background: #2C3E50"] span:contains("متاح") {
    background: #27AE60 !important;
    color: white !important;
}

div[style*="background: #2C3E50"] span:contains("محجوز") {
    background: #E74C3C !important;
    color: white !important;
}
</style>');
echo "</pre>";
echo "</div>";
echo "</div>";

// Option 4: Direct file modification
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>📝 الحل الرابع: تعديل الملف مباشرة</h2>";
echo "<p class='mb-4'>ابحث عن هذا السطر في ملف exhibitions-1-simple.php:</p>";

echo "<div class='bg-red-50 border border-red-200 rounded-lg p-3 mb-4'>";
echo "<code class='text-sm'>style=\"background: #2C3E50;\"</code>";
echo "</div>";

echo "<p class='mb-4'>واستبدله بهذا:</p>";

echo "<div class='bg-green-50 border border-green-200 rounded-lg p-3'>";
echo "<code class='text-sm'>style=\"background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%); border: 2px solid #2C3E50; color: #2C3E50;\"</code>";
echo "</div>";
echo "</div>";

// Test section
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>🧪 اختبار الإصلاح</h2>";

echo "<div class='grid grid-cols-1 md:grid-cols-2 gap-4 mb-4'>";

// Before fix example
echo "<div>";
echo "<h3 class='font-semibold mb-2'>قبل الإصلاح (مشكلة):</h3>";
echo "<div class='border rounded-lg p-4' style='background: #2C3E50;'>";
echo "<div class='flex justify-between items-start mb-3'>";
echo "<h3 class='font-bold text-lg text-white'>A001</h3>";
echo "<span class='bg-green-500 text-white px-3 py-1 rounded-full text-sm'>متاح</span>";
echo "</div>";
echo "<p class='mb-3 text-gray-300'>جناح ممتاز في موقع استراتيجي</p>";
echo "<div class='space-y-2 text-sm text-gray-300'>";
echo "<div>📏 المساحة: 25 م²</div>";
echo "<div>📍 الموقع: Hall A, Section 1</div>";
echo "<div class='font-bold text-lg text-red-400'>💰 2,500 KWD</div>";
echo "</div>";
echo "<div class='mt-2 text-center text-red-400 font-bold'>❌ النصوص غير واضحة</div>";
echo "</div>";
echo "</div>";

// After fix example
echo "<div>";
echo "<h3 class='font-semibold mb-2'>بعد الإصلاح (صحيح):</h3>";
echo "<div class='border-2 rounded-lg p-4' style='border-color: #2C3E50; background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);'>";
echo "<div class='flex justify-between items-start mb-3'>";
echo "<h3 class='font-bold text-lg' style='color: #2C3E50;'>A001</h3>";
echo "<span class='text-white px-3 py-1 rounded-full text-sm font-semibold' style='background: #27AE60;'>متاح</span>";
echo "</div>";
echo "<p class='mb-3' style='color: #34495E;'>جناح ممتاز في موقع استراتيجي</p>";
echo "<div class='space-y-2 text-sm'>";
echo "<div style='color: #2C3E50;'>📏 المساحة: 25 م²</div>";
echo "<div style='color: #2C3E50;'>📍 الموقع: Hall A, Section 1</div>";
echo "<div class='font-bold text-lg' style='color: #E74C3C;'>💰 2,500 KWD</div>";
echo "</div>";
echo "<div class='mt-2 text-center text-green-600 font-bold'>✅ جميع النصوص واضحة</div>";
echo "</div>";
echo "</div>";

echo "</div>";
echo "</div>";

// Navigation
echo "<div class='text-center space-x-reverse space-x-4'>";
echo "<a href='/exhibitions-1-simple.php' class='text-white px-6 py-3 rounded-lg hover:opacity-80' style='background: #2C3E50;'>🧪 اختبار الصفحة</a>";
echo "<a href='/fix-booth-colors-final.php' class='bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700'>📋 الحل الشامل</a>";
echo "</div>";

echo "</div>";

// JavaScript for the fix button
echo "<script>";
echo "function runFix() {";
echo "    console.log('🚀 بدء الإصلاح الطارئ...');";
echo "    let fixedCount = 0;";
echo "    document.querySelectorAll('div[style*=\"background: #2C3E50\"]').forEach(function(booth) {";
echo "        if (booth.textContent.includes('KWD') || booth.textContent.includes('متاح')) {";
echo "            booth.style.setProperty('background', 'linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)', 'important');";
echo "            booth.style.setProperty('border', '2px solid #2C3E50', 'important');";
echo "            booth.querySelectorAll('h3, p, div').forEach(function(text) {";
echo "                if (!text.closest('a')) {";
echo "                    text.style.setProperty('color', '#2C3E50', 'important');";
echo "                    if (text.textContent.includes('KWD')) {";
echo "                        text.style.setProperty('color', '#E74C3C', 'important');";
echo "                        text.style.setProperty('font-weight', 'bold', 'important');";
echo "                    }";
echo "                }";
echo "            });";
echo "            booth.querySelectorAll('span').forEach(function(badge) {";
echo "                if (badge.textContent.includes('متاح')) {";
echo "                    badge.style.setProperty('background', '#27AE60', 'important');";
echo "                    badge.style.setProperty('color', 'white', 'important');";
echo "                }";
echo "            });";
echo "            fixedCount++;";
echo "        }";
echo "    });";
echo "    alert('🎉 تم إصلاح ' + fixedCount + ' جناح بنجاح!');";
echo "}";
echo "</script>";

echo "</body>";
echo "</html>";
?>

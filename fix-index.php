<?php
echo "<h1>Fix index.php File</h1>";

$indexPath = __DIR__ . '/index.php';

// Correct Laravel 10 index.php content
$correctIndexContent = '<?php

use Illuminate\Contracts\Http\Kernel;
use Illuminate\Http\Request;

define(\'LARAVEL_START\', microtime(true));

/*
|--------------------------------------------------------------------------
| Check If The Application Is Under Maintenance
|--------------------------------------------------------------------------
|
| If the application is in maintenance / demo mode via the "down" command
| we will load this file so that any pre-rendered content can be shown
| instead of starting the framework, which could cause an exception.
|
*/

if (file_exists($maintenance = __DIR__.\'/../storage/framework/maintenance.php\')) {
    require $maintenance;
}

/*
|--------------------------------------------------------------------------
| Register The Auto Loader
|--------------------------------------------------------------------------
|
| Composer provides a convenient, automatically generated class loader for
| this application. We just need to utilize it! We\'ll simply require it
| into the script here so we don\'t need to manually load our classes.
|
*/

require __DIR__.\'/../vendor/autoload.php\';

/*
|--------------------------------------------------------------------------
| Run The Application
|--------------------------------------------------------------------------
|
| Once we have the application, we can handle the incoming request using
| the application\'s HTTP kernel. Then, we will send the response back
| to this client\'s browser, allowing them to enjoy our application.
|
*/

$app = require_once __DIR__.\'/../bootstrap/app.php\';

$kernel = $app->make(Kernel::class);

$response = $kernel->handle(
    $request = Request::capture()
)->send();

$kernel->terminate($request, $response);';

echo "<h2>Backing up current index.php:</h2>";

if (file_exists($indexPath)) {
    $backupPath = $indexPath . '.backup.' . date('Y-m-d-H-i-s');
    if (copy($indexPath, $backupPath)) {
        echo "✅ Backup created: " . basename($backupPath) . "<br>";
    } else {
        echo "❌ Failed to create backup<br>";
    }
} else {
    echo "⚠️ No existing index.php to backup<br>";
}

echo "<h2>Writing correct index.php:</h2>";

if (file_put_contents($indexPath, $correctIndexContent) !== false) {
    echo "✅ index.php updated successfully!<br>";
    echo "File size: " . filesize($indexPath) . " bytes<br>";
    
    echo "<h2>Verification:</h2>";
    
    // Verify the file was written correctly
    $newContent = file_get_contents($indexPath);
    
    if (strpos($newContent, 'LARAVEL_START') !== false) {
        echo "✅ LARAVEL_START present<br>";
    }
    
    if (strpos($newContent, '__DIR__.\'/../vendor/autoload.php\'') !== false) {
        echo "✅ Correct autoloader path<br>";
    }
    
    if (strpos($newContent, '__DIR__.\'/../bootstrap/app.php\'') !== false) {
        echo "✅ Correct bootstrap path<br>";
    }
    
    if (strpos($newContent, 'handleRequest') === false) {
        echo "✅ Uses Laravel 10 compatible syntax<br>";
    }
    
    echo "<br><div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px;'>";
    echo "<h3 style='color: #155724;'>🎉 index.php Fixed!</h3>";
    echo "<p>The index.php file has been updated with the correct Laravel 10 syntax.</p>";
    echo "<p><strong><a href='/' style='font-size: 18px;'>🚀 TEST YOUR APPLICATION NOW</a></strong></p>";
    echo "</div>";
    
} else {
    echo "❌ Failed to write index.php<br>";
    
    echo "<h2>Manual Fix Required:</h2>";
    echo "<div style='background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; border-radius: 5px;'>";
    echo "<p>Please manually replace the content of index.php with the correct version shown above.</p>";
    echo "</div>";
}

echo "<h2>Test Sequence:</h2>";
echo "<ol>";
echo "<li><a href='/'>Test Homepage</a></li>";
echo "<li><a href='/exhibitions'>Test Exhibitions</a></li>";
echo "<li><a href='/login'>Test Login</a></li>";
echo "</ol>";

echo "<h2>If Still Getting 500 Error:</h2>";
echo "<ul>";
echo "<li><a href='web-debug.php'>Run web debug again</a></li>";
echo "<li>Check Hostinger error logs in control panel</li>";
echo "<li>Verify .env file has correct database credentials</li>";
echo "</ul>";
?>

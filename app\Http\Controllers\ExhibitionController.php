<?php

namespace App\Http\Controllers;

use App\Models\Exhibition;
use App\Models\Category;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Str;

class ExhibitionController extends Controller
{
    /**
     * Display a listing of exhibitions.
     */
    public function index(Request $request): Response
    {
        $query = Exhibition::with(['category', 'organizer'])
            ->where('status', 'published'); // Temporary: use direct where instead of scope

        // Filter by category
        if ($request->filled('category')) {
            $query->whereHas('category', function ($q) use ($request) {
                $q->where('slug', $request->get('category'));
            });
        }

        // Search
        if ($request->filled('search')) {
            $searchTerm = $request->get('search');
            $query->where(function ($q) use ($searchTerm) {
                $q->where('title', 'like', "%{$searchTerm}%")
                  ->orWhere('description', 'like', "%{$searchTerm}%")
                  ->orWhere('city', 'like', "%{$searchTerm}%");
            });
        }

        // Sort
        $sortBy = $request->get('sort', 'start_date');
        $sortOrder = $request->get('order', 'asc');

        if (in_array($sortBy, ['start_date', 'title', 'created_at'])) {
            $query->orderBy($sortBy, $sortOrder);
        }

        $exhibitions = $query->paginate(12)->withQueryString();
        $categories = Category::active()->ordered()->get();

        return Inertia::render('Exhibitions/Index', [
            'exhibitions' => $exhibitions,
            'categories' => $categories,
            'filters' => $request->only(['category', 'search', 'sort', 'order']),
            'locale' => app()->getLocale(),
            'translations' => [
                'app' => __('app'),
            ],
        ]);
    }

    /**
     * Display the specified exhibition.
     */
    public function show(Exhibition $exhibition): Response
    {
        $exhibition->load([
            'category',
            'organizer',
            'booths' => function ($query) {
                $query->orderBy('booth_number');
            }
        ]);

        // Get available booths grouped by size
        $availableBooths = $exhibition->booths()
            ->available()
            ->get()
            ->groupBy('size');

        // Get booth statistics
        $boothStats = [
            'total' => $exhibition->booths()->count(),
            'available' => $exhibition->booths()->available()->count(),
            'booked' => $exhibition->booths()->booked()->count(),
            'by_size' => $exhibition->booths()
                ->selectRaw('size, count(*) as count, sum(case when status = "available" then 1 else 0 end) as available_count')
                ->groupBy('size')
                ->get()
                ->keyBy('size'),
        ];

        // Related exhibitions
        $relatedExhibitions = Exhibition::with(['category', 'organizer'])
            ->published()
            ->where('category_id', $exhibition->category_id)
            ->where('id', '!=', $exhibition->id)
            ->take(4)
            ->get();

        return Inertia::render('Exhibitions/Show', [
            'exhibition' => $exhibition,
            'availableBooths' => $availableBooths,
            'boothStats' => $boothStats,
            'relatedExhibitions' => $relatedExhibitions,
            'locale' => app()->getLocale(),
            'translations' => [
                'app' => __('app'),
            ],
        ]);
    }

    /**
     * Show the form for creating a new exhibition.
     */
    public function create(): Response
    {
        $this->authorize('create', Exhibition::class);

        $categories = Category::active()->ordered()->get();

        return Inertia::render('Exhibitions/Create', [
            'categories' => $categories,
        ]);
    }

    /**
     * Store a newly created exhibition.
     */
    public function store(Request $request): RedirectResponse
    {
        $this->authorize('create', Exhibition::class);

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'short_description' => 'nullable|string|max:500',
            'category_id' => 'required|exists:categories,id',
            'venue_name' => 'required|string|max:255',
            'venue_address' => 'required|string',
            'city' => 'required|string|max:255',
            'country' => 'required|string|max:255',
            'start_date' => 'required|date|after:now',
            'end_date' => 'required|date|after:start_date',
            'registration_start' => 'required|date|before:start_date',
            'registration_end' => 'required|date|after:registration_start|before:start_date',
            'booth_price_from' => 'nullable|numeric|min:0',
            'currency' => 'required|string|size:3',
            'max_booths' => 'required|integer|min:1',
            'contact_info' => 'nullable|array',
            'social_links' => 'nullable|array',
            'terms_and_conditions' => 'nullable|string',
        ]);

        $validated['slug'] = Str::slug($validated['title']);
        $validated['organizer_id'] = auth()->id();
        $validated['status'] = 'draft';

        $exhibition = Exhibition::create($validated);

        return redirect()->route('exhibitions.show', $exhibition)
            ->with('success', 'Exhibition created successfully!');
    }

    /**
     * Show the form for editing the specified exhibition.
     */
    public function edit(Exhibition $exhibition): Response
    {
        $this->authorize('update', $exhibition);

        $categories = Category::active()->ordered()->get();

        return Inertia::render('Exhibitions/Edit', [
            'exhibition' => $exhibition,
            'categories' => $categories,
        ]);
    }

    /**
     * Update the specified exhibition.
     */
    public function update(Request $request, Exhibition $exhibition): RedirectResponse
    {
        $this->authorize('update', $exhibition);

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'short_description' => 'nullable|string|max:500',
            'category_id' => 'required|exists:categories,id',
            'venue_name' => 'required|string|max:255',
            'venue_address' => 'required|string',
            'city' => 'required|string|max:255',
            'country' => 'required|string|max:255',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'registration_start' => 'required|date|before:start_date',
            'registration_end' => 'required|date|after:registration_start|before:start_date',
            'booth_price_from' => 'nullable|numeric|min:0',
            'currency' => 'required|string|size:3',
            'max_booths' => 'required|integer|min:1',
            'contact_info' => 'nullable|array',
            'social_links' => 'nullable|array',
            'terms_and_conditions' => 'nullable|string',
            'status' => 'required|in:draft,published,cancelled,completed',
        ]);

        if ($validated['title'] !== $exhibition->title) {
            $validated['slug'] = Str::slug($validated['title']);
        }

        $exhibition->update($validated);

        return redirect()->route('exhibitions.show', $exhibition)
            ->with('success', 'Exhibition updated successfully!');
    }

    /**
     * Remove the specified exhibition.
     */
    public function destroy(Exhibition $exhibition): RedirectResponse
    {
        $this->authorize('delete', $exhibition);

        $exhibition->delete();

        return redirect()->route('exhibitions.index')
            ->with('success', 'Exhibition deleted successfully!');
    }
}

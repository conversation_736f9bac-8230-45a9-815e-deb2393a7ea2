<?php
// Simple Laravel Test
// Test Laravel step by step

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='utf-8'>";
echo "<title>اختبار Laravel بسيط</title>";
echo "<script src='https://cdn.tailwindcss.com'></script>";
echo "</head>";
echo "<body class='bg-gray-50 p-8'>";

echo "<div class='max-w-4xl mx-auto'>";
echo "<h1 class='text-2xl font-bold mb-6'>🔍 اختبار Laravel بسيط</h1>";

// Test 1: Basic Laravel loading
echo "<div class='bg-white rounded-lg shadow p-6 mb-6'>";
echo "<h2 class='text-lg font-bold mb-4'>1️⃣ اختبار تحميل Laravel</h2>";

try {
    require_once 'vendor/autoload.php';
    echo "<div class='bg-green-50 border border-green-200 rounded p-3 mb-3'>";
    echo "<p class='text-green-700'>✅ تم تحميل vendor/autoload.php</p>";
    echo "</div>";
    
    $app = require_once 'bootstrap/app.php';
    echo "<div class='bg-green-50 border border-green-200 rounded p-3 mb-3'>";
    echo "<p class='text-green-700'>✅ تم تحميل bootstrap/app.php</p>";
    echo "</div>";
    
    // Test if we can access config
    $appName = config('app.name', 'Laravel');
    echo "<div class='bg-blue-50 border border-blue-200 rounded p-3 mb-3'>";
    echo "<p class='text-blue-700'>📋 اسم التطبيق: {$appName}</p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='bg-red-50 border border-red-200 rounded p-3'>";
    echo "<p class='text-red-700'>❌ خطأ: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "</div>";

// Test 2: Check if we can access routes
echo "<div class='bg-white rounded-lg shadow p-6 mb-6'>";
echo "<h2 class='text-lg font-bold mb-4'>2️⃣ اختبار الوصول لـ Routes</h2>";

try {
    // Try to get all routes
    $routes = \Illuminate\Support\Facades\Route::getRoutes();
    $routeCount = count($routes);
    
    echo "<div class='bg-green-50 border border-green-200 rounded p-3 mb-3'>";
    echo "<p class='text-green-700'>✅ تم العثور على {$routeCount} route</p>";
    echo "</div>";
    
    // Look for booking routes
    $bookingRoutes = [];
    foreach ($routes as $route) {
        $uri = $route->uri();
        if (strpos($uri, 'booking') !== false) {
            $bookingRoutes[] = $uri . ' (' . implode('|', $route->methods()) . ')';
        }
    }
    
    if (!empty($bookingRoutes)) {
        echo "<div class='bg-blue-50 border border-blue-200 rounded p-3 mb-3'>";
        echo "<p class='text-blue-700 font-semibold'>📋 Routes الحجز الموجودة:</p>";
        echo "<ul class='text-blue-600 text-sm mt-2'>";
        foreach (array_slice($bookingRoutes, 0, 10) as $route) {
            echo "<li>• {$route}</li>";
        }
        echo "</ul>";
        echo "</div>";
    } else {
        echo "<div class='bg-yellow-50 border border-yellow-200 rounded p-3 mb-3'>";
        echo "<p class='text-yellow-700'>⚠️ لم يتم العثور على routes للحجز</p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='bg-red-50 border border-red-200 rounded p-3'>";
    echo "<p class='text-red-700'>❌ خطأ في Routes: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "</div>";

// Test 3: Check Controllers
echo "<div class='bg-white rounded-lg shadow p-6 mb-6'>";
echo "<h2 class='text-lg font-bold mb-4'>3️⃣ اختبار Controllers</h2>";

$controllers = [
    'BookingController' => 'app/Http/Controllers/BookingController.php',
    'PaymentController' => 'app/Http/Controllers/PaymentController.php'
];

foreach ($controllers as $name => $path) {
    if (file_exists($path)) {
        echo "<div class='bg-green-50 border border-green-200 rounded p-3 mb-3'>";
        echo "<p class='text-green-700'>✅ {$name} موجود</p>";
        
        // Try to check if class exists
        try {
            $className = "App\\Http\\Controllers\\{$name}";
            if (class_exists($className)) {
                echo "<p class='text-green-600 text-sm'>• Class يمكن تحميله</p>";
                
                // Check methods
                $methods = get_class_methods($className);
                $importantMethods = array_intersect($methods, ['create', 'store', 'index', 'show']);
                if (!empty($importantMethods)) {
                    echo "<p class='text-green-600 text-sm'>• Methods متاحة: " . implode(', ', $importantMethods) . "</p>";
                }
            } else {
                echo "<p class='text-yellow-600 text-sm'>• Class لا يمكن تحميله</p>";
            }
        } catch (Exception $e) {
            echo "<p class='text-red-600 text-sm'>• خطأ في تحميل Class: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
        
        echo "</div>";
    } else {
        echo "<div class='bg-red-50 border border-red-200 rounded p-3 mb-3'>";
        echo "<p class='text-red-700'>❌ {$name} غير موجود في {$path}</p>";
        echo "</div>";
    }
}

echo "</div>";

// Test 4: Manual URL test
echo "<div class='bg-white rounded-lg shadow p-6 mb-6'>";
echo "<h2 class='text-lg font-bold mb-4'>4️⃣ اختبار الروابط يدوياً</h2>";

$testUrls = [
    'Laravel Index' => '/index.php',
    'Bookings Create' => '/index.php/bookings/create?booth_id=23&exhibition_id=1',
    'Dashboard' => '/index.php/dashboard'
];

echo "<div class='space-y-3'>";

foreach ($testUrls as $name => $url) {
    echo "<div class='border border-gray-200 rounded p-3'>";
    echo "<h3 class='font-semibold mb-2'>{$name}</h3>";
    echo "<p class='text-sm text-gray-600 mb-2'>انسخ هذا الرابط والصقه في نافذة جديدة:</p>";
    echo "<div class='bg-gray-100 p-2 rounded'>";
    echo "<code class='text-sm'>https://seasonexpo.com{$url}</code>";
    echo "</div>";
    echo "</div>";
}

echo "</div>";
echo "</div>";

// Test 5: Check .env
echo "<div class='bg-white rounded-lg shadow p-6 mb-6'>";
echo "<h2 class='text-lg font-bold mb-4'>5️⃣ اختبار إعدادات .env</h2>";

if (file_exists('.env')) {
    $envContent = file_get_contents('.env');
    
    // Check important settings
    $appDebug = env('APP_DEBUG', false);
    $appEnv = env('APP_ENV', 'production');
    $appUrl = env('APP_URL', 'http://localhost');
    
    echo "<div class='space-y-3'>";
    
    echo "<div class='bg-blue-50 border border-blue-200 rounded p-3'>";
    echo "<p class='text-blue-700'><strong>APP_ENV:</strong> {$appEnv}</p>";
    echo "</div>";
    
    echo "<div class='bg-blue-50 border border-blue-200 rounded p-3'>";
    echo "<p class='text-blue-700'><strong>APP_DEBUG:</strong> " . ($appDebug ? 'true' : 'false') . "</p>";
    echo "</div>";
    
    echo "<div class='bg-blue-50 border border-blue-200 rounded p-3'>";
    echo "<p class='text-blue-700'><strong>APP_URL:</strong> {$appUrl}</p>";
    echo "</div>";
    
    echo "</div>";
} else {
    echo "<div class='bg-red-50 border border-red-200 rounded p-3'>";
    echo "<p class='text-red-700'>❌ ملف .env غير موجود</p>";
    echo "</div>";
}

echo "</div>";

// Instructions
echo "<div class='bg-yellow-50 border border-yellow-200 rounded-lg p-6'>";
echo "<h2 class='text-lg font-bold text-yellow-700 mb-4'>📋 التعليمات</h2>";

echo "<div class='space-y-3'>";

echo "<div class='bg-white p-4 rounded border'>";
echo "<h3 class='font-semibold mb-2'>اختبر الروابط يدوياً:</h3>";
echo "<ol class='text-sm space-y-1 list-decimal list-inside'>";
echo "<li>انسخ كل رابط من القسم 4 أعلاه</li>";
echo "<li>الصقه في نافذة متصفح جديدة</li>";
echo "<li>أخبرني ماذا يحدث لكل رابط</li>";
echo "</ol>";
echo "</div>";

echo "<div class='bg-white p-4 rounded border'>";
echo "<h3 class='font-semibold mb-2'>أخبرني بالنتائج:</h3>";
echo "<ul class='text-sm space-y-1 list-disc list-inside'>";
echo "<li>هل يعمل /index.php؟</li>";
echo "<li>هل يعمل /index.php/bookings/create؟</li>";
echo "<li>هل يعمل /index.php/dashboard؟</li>";
echo "<li>ما هي رسائل الخطأ إن وجدت؟</li>";
echo "</ul>";
echo "</div>";

echo "</div>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>

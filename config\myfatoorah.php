<?php

return [
    /**
     * API Token Key (string)
     * Accepted value:
     * Live Token: https://myfatoorah.readme.io/docs/live-token
     * Test Token: https://myfatoorah.readme.io/docs/test-token
     */
    'api_key' => env('MYFATOORAH_API_KEY', 'rLtt6JWvbUHDDhsZnfpAhpYk4dxYDQkbcPTyGaKp2TYqQgG7FGZ5Th_WD53Oq8Ebz6A53njUoo1w3pjU1D4vs_ZMqFiz_j0urb_BH9Oq9VZoKFoJEDAbRZepGcQanImyYrry7Kt6MnMdgfG5jn4HngWoRdKduNNyP4kzcp3mRv7x00ahkm9LAK7ZRieg7k1PDAnBIOG3EyVSJ5kK4WLMvYr7sCwHbHcu4A5WwelxYK0GMJy37bNAarSJDFQsJ2ZvJjvMDmfWwDVFEVe_5tOomfVNt6bOg9mexbGjMrnHBnKnZR1vQbBtQieDlQepzTZMuQrSuKn-t5XZM7V6fCW7oP-uXGX-sMOajeX65JOf6XVpk29DP6ro8WTAflCDANC193yof8-f5_EYY-3hXhJj7RBXmizDpneEQDSaSz5sFk0sV5qPcARJ9zGG73vuGFyenjPPmtDtXtpx35A-BVcOSBYVIWe9kndG3nclfefjKEuZ3m4jL9Gg1h2JBvmXSMYiZtp9MNKynEAJQS6ZHe_J_l77652xwPNxMRTMASk1ZsJL'),

    /**
     * Test Mode (boolean)
     * Accepted value: true for the test mode or false for the live mode
     * BACK TO TEST MODE (compatible with test API key)
     */
    'test_mode' => env('MYFATOORAH_TEST_MODE', true),

    /**
     * Country ISO Code (string)
     * Accepted value: KWT, SAU, ARE, QAT, BHR, OMN, JOD, or EGY.
     */
    'country_iso' => env('MYFATOORAH_COUNTRY_ISO', 'KWT'),

    /**
     * Save card (boolean)
     * Accepted value: true if you want to enable save card options.
     * You should contact your account manager to enable this feature in your MyFatoorah account as well.
     */
    'save_card' => env('MYFATOORAH_SAVE_CARD', false),

    /**
     * Webhook secret key (string)
     * Enable webhook on your MyFatoorah account setting then paste the secret key here.
     * The webhook link is: https://{example.com}/myfatoorah/webhook
     */
    'webhook_secret_key' => env('MYFATOORAH_WEBHOOK_SECRET', ''),

    /**
     * Register Apple Pay (boolean)
     * Set it to true to show the Apple Pay on the checkout page.
     * First, verify your domain with Apple Pay before you set it to true.
     * You can either follow the steps here: https://docs.myfatoorah.com/docs/apple-pay#verify-your-domain-with-apple-pay or contact the MyFatoorah support team (<EMAIL>).
     */
    'register_apple_pay' => env('MYFATOORAH_APPLE_PAY', false),

    /**
     * Base URL (string)
     * Test API URL: https://apitest.myfatoorah.com
     * Live API URL: https://api.myfatoorah.com
     * BACK TO TEST (compatible with test API key)
     */
    'base_url' => env('MYFATOORAH_BASE_URL', env('MYFATOORAH_TEST_MODE', true)
        ? 'https://apitest.myfatoorah.com'
        : 'https://api.myfatoorah.com'),

    // Legacy support - keep for backward compatibility
    'token' => env('MYFATOORAH_TOKEN'),
    'api_url' => env('MYFATOORAH_BASE_URL', env('MYFATOORAH_TEST_MODE', false)
        ? 'https://apitest.myfatoorah.com'
        : 'https://api.myfatoorah.com'),
];

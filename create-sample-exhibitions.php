<?php
// Create Sample Exhibitions for Testing
// This file creates sample exhibitions in the database

require_once __DIR__ . '/vendor/autoload.php';

// Load Laravel app
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\Exhibition;
use App\Models\Booth;
use App\Models\Category;
use App\Models\User;

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='utf-8'>";
echo "<title>إنشاء بيانات تجريبية - Season Expo</title>";
echo "<script src='https://cdn.tailwindcss.com'></script>";
echo "</head>";
echo "<body class='bg-gray-50 p-8'>";

echo "<div class='max-w-4xl mx-auto'>";
echo "<h1 class='text-3xl font-bold text-blue-600 mb-8'>🏗️ إنشاء بيانات تجريبية للمعارض</h1>";

try {
    // Check if we already have exhibitions
    $existingExhibitions = Exhibition::count();
    
    if ($existingExhibitions > 0) {
        echo "<div class='bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-4'>";
        echo "⚠️ يوجد بالفعل {$existingExhibitions} معرض في قاعدة البيانات.";
        echo "</div>";
    }

    // Create a default organizer if none exists
    $organizer = User::first();
    if (!$organizer) {
        $organizer = User::create([
            'name' => 'منظم المعارض',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'organizer',
            'email_verified_at' => now(),
        ]);
        echo "<div class='bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4'>";
        echo "✅ تم إنشاء مستخدم منظم جديد: {$organizer->email}";
        echo "</div>";
    }

    // Create a default category if none exists
    $category = Category::first();
    if (!$category) {
        $category = Category::create([
            'name' => 'معارض عامة',
            'slug' => 'general-exhibitions',
            'description' => 'معارض متنوعة وعامة',
            'is_active' => true,
        ]);
        echo "<div class='bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4'>";
        echo "✅ تم إنشاء فئة جديدة: {$category->name}";
        echo "</div>";
    }

    // Sample exhibitions data
    $sampleExhibitions = [
        [
            'title' => 'معرض التكنولوجيا 2025',
            'slug' => 'tech-expo-2025',
            'description' => 'أحدث التقنيات والابتكارات التكنولوجية في معرض واحد. اكتشف المستقبل اليوم!',
            'short_description' => 'أحدث التقنيات والابتكارات التكنولوجية',
            'venue_name' => 'مركز المعارض الدولي',
            'venue_address' => 'شارع الخليج العربي، مدينة الكويت',
            'city' => 'الكويت',
            'country' => 'الكويت',
            'start_date' => now()->addDays(30),
            'end_date' => now()->addDays(35),
            'registration_start' => now(),
            'registration_end' => now()->addDays(25),
            'status' => 'published',
            'is_featured' => true,
            'booth_price_from' => 1500.000,
            'currency' => 'KWD',
            'max_booths' => 100,
        ],
        [
            'title' => 'معرض الصحة والجمال',
            'slug' => 'health-beauty-expo-2025',
            'description' => 'معرض متخصص في منتجات الصحة والجمال والعناية الشخصية',
            'short_description' => 'منتجات الصحة والجمال والعناية الشخصية',
            'venue_name' => 'مركز الكويت التجاري',
            'venue_address' => 'منطقة السالمية، الكويت',
            'city' => 'الكويت',
            'country' => 'الكويت',
            'start_date' => now()->addDays(60),
            'end_date' => now()->addDays(65),
            'registration_start' => now(),
            'registration_end' => now()->addDays(55),
            'status' => 'published',
            'is_featured' => false,
            'booth_price_from' => 1200.000,
            'currency' => 'KWD',
            'max_booths' => 80,
        ],
        [
            'title' => 'معرض الأغذية والمشروبات',
            'slug' => 'food-beverage-expo-2025',
            'description' => 'معرض شامل للأغذية والمشروبات والمنتجات الغذائية المتنوعة',
            'short_description' => 'الأغذية والمشروبات والمنتجات الغذائية',
            'venue_name' => 'قاعة الكويت الكبرى',
            'venue_address' => 'منطقة الشويخ، الكويت',
            'city' => 'الكويت',
            'country' => 'الكويت',
            'start_date' => now()->addDays(90),
            'end_date' => now()->addDays(95),
            'registration_start' => now(),
            'registration_end' => now()->addDays(85),
            'status' => 'published',
            'is_featured' => true,
            'booth_price_from' => 1800.000,
            'currency' => 'KWD',
            'max_booths' => 120,
        ]
    ];

    echo "<div class='bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded mb-4'>";
    echo "🚀 بدء إنشاء المعارض التجريبية...";
    echo "</div>";

    foreach ($sampleExhibitions as $index => $exhibitionData) {
        // Check if exhibition already exists
        $existingExhibition = Exhibition::where('slug', $exhibitionData['slug'])->first();
        
        if ($existingExhibition) {
            echo "<div class='bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-2'>";
            echo "⚠️ المعرض '{$exhibitionData['title']}' موجود بالفعل (ID: {$existingExhibition->id})";
            echo "</div>";
            continue;
        }

        // Add organizer and category IDs
        $exhibitionData['organizer_id'] = $organizer->id;
        $exhibitionData['category_id'] = $category->id;

        // Create exhibition
        $exhibition = Exhibition::create($exhibitionData);

        echo "<div class='bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-2'>";
        echo "✅ تم إنشاء المعرض: {$exhibition->title} (ID: {$exhibition->id})";
        echo "</div>";

        // Create sample booths for each exhibition
        $boothCount = rand(10, 20);
        for ($i = 1; $i <= $boothCount; $i++) {
            $booth = Booth::create([
                'exhibition_id' => $exhibition->id,
                'booth_number' => 'B' . str_pad($i, 3, '0', STR_PAD_LEFT),
                'name' => "جناح رقم {$i}",
                'description' => 'جناح متميز بموقع ممتاز ومساحة واسعة',
                'size' => ['small', 'medium', 'large'][rand(0, 2)],
                'width' => rand(3, 6),
                'height' => rand(3, 6),
                'area' => rand(9, 36),
                'price' => rand(1000, 3000),
                'location' => 'القاعة الرئيسية، القسم ' . chr(65 + rand(0, 3)),
                'features' => json_encode(['كهرباء', 'إنترنت', 'تخزين']),
                'status' => rand(0, 1) ? 'available' : 'booked',
                'is_featured' => rand(0, 1),
                'is_corner' => rand(0, 1),
            ]);
        }

        echo "<div class='bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded mb-4'>";
        echo "📦 تم إنشاء {$boothCount} جناح للمعرض";
        echo "</div>";
    }

    // Display summary
    $totalExhibitions = Exhibition::count();
    $totalBooths = Booth::count();

    echo "<div class='bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4'>";
    echo "<h2 class='text-xl font-bold mb-2'>📊 ملخص البيانات:</h2>";
    echo "<ul class='list-disc list-inside'>";
    echo "<li>إجمالي المعارض: {$totalExhibitions}</li>";
    echo "<li>إجمالي الأجنحة: {$totalBooths}</li>";
    echo "<li>المنظمين: " . User::count() . "</li>";
    echo "<li>الفئات: " . Category::count() . "</li>";
    echo "</ul>";
    echo "</div>";

    echo "<div class='bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded mb-4'>";
    echo "<h2 class='text-xl font-bold mb-2'>🔗 روابط الاختبار:</h2>";
    echo "<div class='space-y-2'>";
    echo "<a href='/exhibitions-simple' class='block bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700'>📋 قائمة المعارض</a>";
    echo "<a href='/exhibitions/1/simple' class='block bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700'>👁️ عرض المعرض الأول</a>";
    echo "<a href='/exhibitions/tech-expo-2025/simple' class='block bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700'>🔗 عرض المعرض بالـ Slug</a>";
    echo "<a href='/' class='block bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700'>🏠 الصفحة الرئيسية</a>";
    echo "</div>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div class='bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4'>";
    echo "❌ خطأ: " . $e->getMessage();
    echo "</div>";
}

echo "</div>";
echo "</body>";
echo "</html>";
?>

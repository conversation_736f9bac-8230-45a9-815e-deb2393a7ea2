<?php
// Enable all error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h1>Laravel Autoloader Test</h1>";

// Test 1: Basic PHP
echo "✅ Test 1: Basic PHP - OK<br>";

// Test 2: File path
$basePath = dirname(__DIR__);
echo "✅ Test 2: Base path: {$basePath}<br>";

// Test 3: Check autoload file
$autoloadPath = $basePath . '/vendor/autoload.php';
echo "✅ Test 3: Autoload path: {$autoloadPath}<br>";
echo "✅ Test 3: Autoload file size: " . filesize($autoloadPath) . " bytes<br>";

// Test 4: Check file permissions
$perms = substr(sprintf('%o', fileperms($autoloadPath)), -4);
echo "✅ Test 4: Autoload permissions: {$perms}<br>";

// Test 5: Try to read file content (first 100 chars)
$content = file_get_contents($autoloadPath, false, null, 0, 100);
echo "✅ Test 5: File starts with: " . htmlspecialchars($content) . "<br>";

// Test 6: Try to include autoload
echo "<br>🔄 Test 6: Attempting to load autoload...<br>";
try {
    require $autoloadPath;
    echo "✅ SUCCESS: Autoload loaded successfully!<br>";
    
    // Test 7: Try to use a Laravel class
    echo "<br>🔄 Test 7: Testing Laravel classes...<br>";
    
    if (class_exists('Illuminate\Foundation\Application')) {
        echo "✅ Laravel Application class found<br>";
    } else {
        echo "❌ Laravel Application class not found<br>";
    }
    
    if (class_exists('Illuminate\Http\Request')) {
        echo "✅ Laravel Request class found<br>";
    } else {
        echo "❌ Laravel Request class not found<br>";
    }
    
} catch (ParseError $e) {
    echo "❌ Parse Error: " . $e->getMessage() . "<br>";
    echo "❌ File: " . $e->getFile() . "<br>";
    echo "❌ Line: " . $e->getLine() . "<br>";
} catch (Error $e) {
    echo "❌ Fatal Error: " . $e->getMessage() . "<br>";
    echo "❌ File: " . $e->getFile() . "<br>";
    echo "❌ Line: " . $e->getLine() . "<br>";
} catch (Exception $e) {
    echo "❌ Exception: " . $e->getMessage() . "<br>";
    echo "❌ File: " . $e->getFile() . "<br>";
    echo "❌ Line: " . $e->getLine() . "<br>";
}

// Test 8: Check .env file
echo "<br>🔄 Test 8: Checking .env file...<br>";
$envPath = $basePath . '/.env';
if (file_exists($envPath)) {
    echo "✅ .env file exists<br>";
    echo "✅ .env file size: " . filesize($envPath) . " bytes<br>";
} else {
    echo "❌ .env file missing<br>";
}

// Test 9: Check bootstrap/app.php
echo "<br>🔄 Test 9: Checking bootstrap file...<br>";
$bootstrapPath = $basePath . '/bootstrap/app.php';
if (file_exists($bootstrapPath)) {
    echo "✅ bootstrap/app.php exists<br>";
    echo "✅ bootstrap/app.php size: " . filesize($bootstrapPath) . " bytes<br>";
    
    // Try to load bootstrap
    try {
        $app = require $bootstrapPath;
        echo "✅ Bootstrap loaded successfully<br>";
        echo "✅ App type: " . get_class($app) . "<br>";
    } catch (Exception $e) {
        echo "❌ Bootstrap error: " . $e->getMessage() . "<br>";
    }
} else {
    echo "❌ bootstrap/app.php missing<br>";
}

echo "<br>🎯 Test completed.<br>";
?>

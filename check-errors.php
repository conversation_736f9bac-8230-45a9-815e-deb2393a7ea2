<?php
// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h1>Error Diagnosis</h1>";

echo "<h2>PHP Basic Test:</h2>";
echo "✅ PHP is working<br>";
echo "PHP Version: " . phpversion() . "<br>";
echo "Current time: " . date('Y-m-d H:i:s') . "<br>";

echo "<h2>File Structure Check:</h2>";
$basePath = dirname(__DIR__);

$criticalFiles = [
    'vendor/autoload.php',
    'bootstrap/app.php',
    'routes/web.php',
    '.env'
];

foreach ($criticalFiles as $file) {
    $fullPath = $basePath . '/' . $file;
    if (file_exists($fullPath)) {
        echo "✅ {$file} exists<br>";
    } else {
        echo "❌ {$file} MISSING<br>";
    }
}

echo "<h2>Laravel Bootstrap Test:</h2>";

try {
    // Test autoloader
    require $basePath . '/vendor/autoload.php';
    echo "✅ Autoloader loaded<br>";
    
    // Test Laravel bootstrap
    $app = require $basePath . '/bootstrap/app.php';
    echo "✅ Laravel app created<br>";
    
    // Test configuration
    $config = $app->make('config');
    echo "✅ Configuration loaded<br>";
    echo "App Name: " . $config->get('app.name') . "<br>";
    echo "App Debug: " . ($config->get('app.debug') ? 'true' : 'false') . "<br>";
    
    // Test database
    try {
        $pdo = new PDO(
            'mysql:host=' . $config->get('database.connections.mysql.host') . ';dbname=' . $config->get('database.connections.mysql.database'),
            $config->get('database.connections.mysql.username'),
            $config->get('database.connections.mysql.password')
        );
        echo "✅ Database connection working<br>";
    } catch (Exception $e) {
        echo "❌ Database error: " . $e->getMessage() . "<br>";
    }
    
    // Test routes
    try {
        $routes = \Illuminate\Support\Facades\Route::getRoutes();
        echo "✅ Routes loaded: " . count($routes) . " routes<br>";
        
        // Check for home route
        $homeRoute = null;
        foreach ($routes as $route) {
            if ($route->uri() === '/') {
                $homeRoute = $route;
                break;
            }
        }
        
        if ($homeRoute) {
            echo "✅ Home route found: " . $homeRoute->getActionName() . "<br>";
        } else {
            echo "❌ Home route not found<br>";
        }
        
    } catch (Exception $e) {
        echo "❌ Routes error: " . $e->getMessage() . "<br>";
    }
    
} catch (ParseError $e) {
    echo "❌ Parse Error: " . $e->getMessage() . "<br>";
    echo "File: " . $e->getFile() . "<br>";
    echo "Line: " . $e->getLine() . "<br>";
} catch (Error $e) {
    echo "❌ Fatal Error: " . $e->getMessage() . "<br>";
    echo "File: " . $e->getFile() . "<br>";
    echo "Line: " . $e->getLine() . "<br>";
} catch (Exception $e) {
    echo "❌ Exception: " . $e->getMessage() . "<br>";
    echo "File: " . $e->getFile() . "<br>";
    echo "Line: " . $e->getLine() . "<br>";
}

echo "<h2>Laravel Error Logs:</h2>";
$logPath = $basePath . '/storage/logs/laravel.log';
if (file_exists($logPath)) {
    echo "✅ Laravel log file exists<br>";
    echo "Size: " . filesize($logPath) . " bytes<br>";
    
    // Show last 20 lines of log
    $lines = file($logPath);
    if ($lines) {
        $lastLines = array_slice($lines, -20);
        echo "<h3>Last 20 log entries:</h3>";
        echo "<pre style='background: #f5f5f5; padding: 10px; max-height: 300px; overflow-y: auto;'>";
        foreach ($lastLines as $line) {
            echo htmlspecialchars($line);
        }
        echo "</pre>";
    }
} else {
    echo "❌ No Laravel log file found<br>";
}

echo "<h2>Server Error Logs:</h2>";
echo "<p>Check Hostinger control panel for server error logs.</p>";

echo "<h2>Quick Fixes to Try:</h2>";
echo "<ol>";
echo "<li><a href='clear-all-caches.php'>Clear All Caches</a></li>";
echo "<li><a href='fix-permissions.php'>Fix File Permissions</a></li>";
echo "<li><a href='simple-test.php'>Simple Laravel Test</a></li>";
echo "</ol>";
?>

<?php
// Test email functionality for password reset

echo "<h1>اختبار وظيفة إرسال البريد الإلكتروني لاستعادة كلمة المرور</h1>";

echo "<h2>حالة النظام الحالية:</h2>";

// Check .env file
if (file_exists(__DIR__ . '/.env')) {
    $env = file_get_contents(__DIR__ . '/.env');
    
    // Extract mail settings
    preg_match('/MAIL_MAILER=(.*)/', $env, $mailer);
    preg_match('/MAIL_HOST=(.*)/', $env, $host);
    preg_match('/MAIL_PORT=(.*)/', $env, $port);
    preg_match('/MAIL_FROM_ADDRESS="?(.*?)"?$/', $env, $from);
    
    $mailer = isset($mailer[1]) ? trim($mailer[1]) : 'غير محدد';
    $host = isset($host[1]) ? trim($host[1]) : 'غير محدد';
    $port = isset($port[1]) ? trim($port[1]) : 'غير محدد';
    $from = isset($from[1]) ? trim($from[1]) : 'غير محدد';
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>إعدادات البريد الإلكتروني الحالية:</h3>";
    echo "<ul>";
    echo "<li><strong>MAIL_MAILER:</strong> $mailer</li>";
    echo "<li><strong>MAIL_HOST:</strong> $host</li>";
    echo "<li><strong>MAIL_PORT:</strong> $port</li>";
    echo "<li><strong>MAIL_FROM_ADDRESS:</strong> $from</li>";
    echo "</ul>";
    echo "</div>";
    
    // Analyze settings
    if ($mailer === 'log') {
        echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>⚠️ تحذير: البريد الإلكتروني في وضع LOG</h3>";
        echo "<p>الإعداد الحالي <code>MAIL_MAILER=log</code> يعني أن الرسائل لن يتم إرسالها فعلياً، بل ستُحفظ في ملف log.</p>";
        echo "<p><strong>مكان الرسائل:</strong> <code>storage/logs/laravel.log</code></p>";
        echo "</div>";
    } elseif ($mailer === 'smtp') {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>✅ البريد الإلكتروني مُعد للإرسال الفعلي</h3>";
        echo "<p>الإعداد الحالي يستخدم SMTP لإرسال الرسائل فعلياً.</p>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>❌ إعداد البريد الإلكتروني غير واضح</h3>";
        echo "<p>الإعداد الحالي قد لا يعمل بشكل صحيح.</p>";
        echo "</div>";
    }
} else {
    echo "<p style='color: red;'>❌ ملف .env غير موجود</p>";
}

// Check database table
echo "<h2>فحص قاعدة البيانات:</h2>";

try {
    $pdo = new PDO('sqlite:' . __DIR__ . '/database/database.sqlite');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Check if password_reset_tokens table exists
    $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='password_reset_tokens'");
    $table = $stmt->fetch();
    
    if ($table) {
        echo "<p style='color: green;'>✅ جدول password_reset_tokens موجود</p>";
        
        // Check recent entries
        $stmt = $pdo->query("SELECT email, created_at FROM password_reset_tokens ORDER BY created_at DESC LIMIT 5");
        $entries = $stmt->fetchAll();
        
        if ($entries) {
            echo "<h3>آخر طلبات إعادة التعيين:</h3>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>البريد الإلكتروني</th><th>تاريخ الطلب</th></tr>";
            foreach ($entries as $entry) {
                echo "<tr><td>{$entry['email']}</td><td>{$entry['created_at']}</td></tr>";
            }
            echo "</table>";
        } else {
            echo "<p>لا توجد طلبات إعادة تعيين سابقة</p>";
        }
        
    } else {
        echo "<p style='color: red;'>❌ جدول password_reset_tokens غير موجود</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
}

// Check log file
echo "<h2>فحص ملف السجلات:</h2>";

$logFile = __DIR__ . '/storage/logs/laravel.log';
if (file_exists($logFile)) {
    echo "<p style='color: green;'>✅ ملف السجلات موجود</p>";
    
    // Get last few lines
    $lines = file($logFile);
    $lastLines = array_slice($lines, -20); // Last 20 lines
    
    $emailLines = array_filter($lastLines, function($line) {
        return strpos($line, 'إعادة تعيين كلمة المرور') !== false || 
               strpos($line, 'password reset') !== false ||
               strpos($line, 'Mail') !== false;
    });
    
    if ($emailLines) {
        echo "<h3>رسائل البريد الإلكتروني الأخيرة في السجل:</h3>";
        echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px; font-size: 12px; overflow-x: auto;'>";
        foreach ($emailLines as $line) {
            echo htmlspecialchars($line);
        }
        echo "</pre>";
    } else {
        echo "<p>لا توجد رسائل بريد إلكتروني في السجل الأخير</p>";
    }
} else {
    echo "<p style='color: orange;'>⚠️ ملف السجلات غير موجود بعد</p>";
}

echo "<h2>اختبار الوظيفة:</h2>";
echo "<ol>";
echo "<li><a href='/reset-password-form' target='_blank'>اذهب إلى صفحة نسيت كلمة المرور</a></li>";
echo "<li>أدخل بريد إلكتروني صحيح</li>";
echo "<li>اضغط على 'إرسال رابط إعادة التعيين'</li>";
echo "<li>تحقق من:</li>";
echo "<ul>";
echo "<li>رسالة النجاح في الصفحة</li>";
echo "<li>إدخال جديد في جدول password_reset_tokens</li>";
echo "<li>رسالة في ملف السجلات (إذا كان MAIL_MAILER=log)</li>";
echo "</ul>";
echo "</ol>";

echo "<h2>كيفية تفعيل إرسال البريد الإلكتروني الفعلي:</h2>";
echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>لاستخدام Gmail SMTP:</h3>";
echo "<pre>";
echo "MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME=\"Season Expo\"";
echo "</pre>";
echo "<p><strong>ملاحظة:</strong> تحتاج إلى إنشاء App Password في Gmail بدلاً من كلمة المرور العادية.</p>";
echo "</div>";

?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h1, h2, h3 {
    color: #333;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
}

table {
    margin: 10px 0;
}

th, td {
    padding: 8px;
    text-align: right;
}

th {
    background-color: #f8f9fa;
}

code {
    background-color: #f8f9fa;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
}

pre {
    white-space: pre-wrap;
    word-wrap: break-word;
}
</style>

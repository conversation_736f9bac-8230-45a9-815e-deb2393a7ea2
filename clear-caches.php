<?php
echo "<h1>Clear All Laravel Caches</h1>";

try {
    require __DIR__ . '/vendor/autoload.php';
    $app = require_once __DIR__ . '/bootstrap/app.php';
    $kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
    
    echo "<h2>Clearing Laravel Caches:</h2>";
    
    // Clear various caches
    $commands = [
        'config:clear' => 'Configuration cache',
        'route:clear' => 'Route cache',
        'view:clear' => 'View cache',
        'cache:clear' => 'Application cache'
    ];
    
    foreach ($commands as $command => $description) {
        try {
            $kernel->call($command);
            echo "✅ {$description} cleared<br>";
        } catch (Exception $e) {
            echo "⚠️ {$description}: " . $e->getMessage() . "<br>";
        }
    }
    
    echo "<h2>Manual Cache Clearing:</h2>";
    
    // Clear bootstrap cache manually
    $bootstrapCache = __DIR__ . '/bootstrap/cache';
    if (is_dir($bootstrapCache)) {
        $files = glob($bootstrapCache . '/*.php');
        $deleted = 0;
        foreach ($files as $file) {
            if (unlink($file)) {
                $deleted++;
            }
        }
        echo "✅ Deleted {$deleted} bootstrap cache files<br>";
    }
    
    // Clear storage caches
    $storagePaths = [
        '/storage/framework/cache/data',
        '/storage/framework/views',
        '/storage/framework/sessions'
    ];
    
    foreach ($storagePaths as $path) {
        $fullPath = __DIR__ . $path;
        if (is_dir($fullPath)) {
            $files = glob($fullPath . '/*');
            $deleted = 0;
            foreach ($files as $file) {
                if (is_file($file) && basename($file) !== '.gitkeep') {
                    if (unlink($file)) {
                        $deleted++;
                    }
                }
            }
            echo "✅ Cleared {$deleted} files from {$path}<br>";
        }
    }
    
    echo "<br><div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px;'>";
    echo "<h3>🎉 All Caches Cleared!</h3>";
    echo "<p>Laravel caches have been cleared. Your routes should now load properly.</p>";
    echo "<p><strong><a href='/' style='font-size: 18px; background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>🚀 TEST SEASON EXPO APPLICATION</a></strong></p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px;'>";
    echo "<h3>❌ Error clearing caches:</h3>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "<h2>Test Routes:</h2>";
echo "<ul>";
echo "<li><a href='/'>Homepage</a></li>";
echo "<li><a href='/exhibitions'>Exhibitions</a></li>";
echo "<li><a href='/login'>Login</a></li>";
echo "<li><a href='/register'>Register</a></li>";
echo "</ul>";

echo "<h2>If Still Seeing Laravel Page:</h2>";
echo "<div style='background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; border-radius: 5px;'>";
echo "<p>If you still see the Laravel welcome page:</p>";
echo "<ol>";
echo "<li><a href='check-season-expo.php'>Check what files were uploaded</a></li>";
echo "<li>Verify routes/web.php contains Season Expo routes</li>";
echo "<li>Check if HomeController.php exists</li>";
echo "<li>Ensure .env has correct database credentials</li>";
echo "</ol>";
echo "</div>";
?>

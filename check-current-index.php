<?php
echo "<h1>Current Index.php Analysis</h1>";

$indexPath = __DIR__ . '/index.php';

if (file_exists($indexPath)) {
    echo "<h2>Current index.php content:</h2>";
    echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto;'>";
    echo htmlspecialchars(file_get_contents($indexPath));
    echo "</pre>";
    
    echo "<h2>File Information:</h2>";
    echo "File size: " . filesize($indexPath) . " bytes<br>";
    echo "Last modified: " . date('Y-m-d H:i:s', filemtime($indexPath)) . "<br>";
    
    // Check if it's the correct Laravel index.php
    $content = file_get_contents($indexPath);
    
    if (strpos($content, 'LARAVEL_START') !== false) {
        echo "✅ Contains LARAVEL_START<br>";
    } else {
        echo "❌ Missing LARAVEL_START<br>";
    }
    
    if (strpos($content, 'vendor/autoload.php') !== false) {
        echo "✅ Includes vendor/autoload.php<br>";
    } else {
        echo "❌ Missing vendor/autoload.php<br>";
    }
    
    if (strpos($content, 'bootstrap/app.php') !== false) {
        echo "✅ Includes bootstrap/app.php<br>";
    } else {
        echo "❌ Missing bootstrap/app.php<br>";
    }
    
    // Check for correct paths
    if (strpos($content, '__DIR__.\'/../') !== false) {
        echo "✅ Uses correct relative paths<br>";
    } else {
        echo "⚠️ Check if paths are correct<br>";
    }
    
} else {
    echo "❌ index.php not found<br>";
}

echo "<h2>Correct index.php should be:</h2>";
echo "<pre style='background: #e8f5e8; padding: 10px; border-radius: 5px; overflow-x: auto;'>";
echo htmlspecialchars('<?php

use Illuminate\Contracts\Http\Kernel;
use Illuminate\Http\Request;

define(\'LARAVEL_START\', microtime(true));

if (file_exists($maintenance = __DIR__.\'/../storage/framework/maintenance.php\')) {
    require $maintenance;
}

require __DIR__.\'/../vendor/autoload.php\';

$app = require_once __DIR__.\'/../bootstrap/app.php\';

$kernel = $app->make(Kernel::class);

$response = $kernel->handle(
    $request = Request::capture()
)->send();

$kernel->terminate($request, $response);');
echo "</pre>";
?>

<?php
// Test Fixed Database Connection
// Quick test to verify the corrected database settings

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='utf-8'>";
echo "<title>اختبار إعدادات قاعدة البيانات المصححة</title>";
echo "<script src='https://cdn.tailwindcss.com'></script>";
echo "</head>";
echo "<body class='bg-gray-50 p-8'>";

echo "<div class='max-w-2xl mx-auto'>";
echo "<h1 class='text-2xl font-bold mb-6'>🔧 اختبار إعدادات قاعدة البيانات المصححة</h1>";

// Show the corrected settings
echo "<div class='bg-white rounded-lg shadow p-6 mb-6'>";
echo "<h2 class='text-lg font-bold mb-4'>⚙️ الإعدادات المصححة</h2>";
echo "<div class='bg-gray-50 p-4 rounded'>";
echo "<h3 class='font-semibold mb-2'>من ملف .env (Laravel):</h3>";
echo "<div class='text-sm space-y-1 font-mono'>";
echo "<div>DB_HOST=127.0.0.1</div>";
echo "<div>DB_DATABASE=seasonexpodb</div>";
echo "<div>DB_USERNAME=root</div>";
echo "<div>DB_PASSWORD= (فارغة)</div>";
echo "</div>";
echo "</div>";
echo "</div>";

// Test the corrected includes/database.php
echo "<div class='bg-white rounded-lg shadow p-6 mb-6'>";
echo "<h2 class='text-lg font-bold mb-4'>🔍 اختبار includes/database.php المصحح</h2>";

try {
    require_once 'includes/database.php';
    $pdo = getDatabaseConnection();
    
    // Test basic connection
    $stmt = $pdo->query("SELECT 1 as test");
    $result = $stmt->fetch();
    
    if ($result['test'] == 1) {
        echo "<div class='bg-green-50 border border-green-200 rounded p-4 mb-4'>";
        echo "<h3 class='font-bold text-green-800 mb-2'>✅ الاتصال ناجح!</h3>";
        echo "<p class='text-green-700'>تم الاتصال بقاعدة البيانات بنجاح</p>";
        echo "</div>";
        
        // Test exhibitions table
        try {
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM exhibitions");
            $exhibitions = $stmt->fetch()['count'];
            
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM booths");
            $booths = $stmt->fetch()['count'];
            
            echo "<div class='bg-blue-50 border border-blue-200 rounded p-4'>";
            echo "<h3 class='font-bold text-blue-800 mb-2'>📊 إحصائيات قاعدة البيانات:</h3>";
            echo "<div class='text-blue-700'>";
            echo "<p>عدد المعارض: <strong>{$exhibitions}</strong></p>";
            echo "<p>عدد الأجنحة: <strong>{$booths}</strong></p>";
            echo "</div>";
            echo "</div>";
            
        } catch (Exception $e) {
            echo "<div class='bg-yellow-50 border border-yellow-200 rounded p-4'>";
            echo "<h3 class='font-bold text-yellow-800 mb-2'>⚠️ تحذير:</h3>";
            echo "<p class='text-yellow-700'>الاتصال يعمل لكن لا يمكن الوصول للجداول: " . htmlspecialchars($e->getMessage()) . "</p>";
            echo "</div>";
        }
    }
    
} catch (Exception $e) {
    echo "<div class='bg-red-50 border border-red-200 rounded p-4'>";
    echo "<h3 class='font-bold text-red-800 mb-2'>❌ فشل الاتصال</h3>";
    echo "<p class='text-red-700'>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "</div>";

// Test Laravel database connection
echo "<div class='bg-white rounded-lg shadow p-6 mb-6'>";
echo "<h2 class='text-lg font-bold mb-4'>🏗️ اختبار Laravel Database</h2>";

try {
    // Try to use Laravel's database configuration
    $host = '127.0.0.1';
    $database = 'seasonexpodb';
    $username = 'root';
    $password = '';
    
    $dsn = "mysql:host={$host};dbname={$database};charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    // Test Laravel tables
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<div class='bg-green-50 border border-green-200 rounded p-4'>";
    echo "<h3 class='font-bold text-green-800 mb-2'>✅ Laravel Database يعمل!</h3>";
    echo "<div class='text-green-700'>";
    echo "<p class='mb-2'>عدد الجداول: <strong>" . count($tables) . "</strong></p>";
    echo "<div class='text-sm'>";
    echo "<strong>الجداول الموجودة:</strong><br>";
    foreach (array_slice($tables, 0, 10) as $table) {
        echo "• {$table}<br>";
    }
    if (count($tables) > 10) {
        echo "... و " . (count($tables) - 10) . " جداول أخرى";
    }
    echo "</div>";
    echo "</div>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='bg-red-50 border border-red-200 rounded p-4'>";
    echo "<h3 class='font-bold text-red-800 mb-2'>❌ Laravel Database لا يعمل</h3>";
    echo "<p class='text-red-700'>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "</div>";

// What was fixed
echo "<div class='bg-green-50 border border-green-200 rounded-lg p-6 mb-6'>";
echo "<h2 class='text-lg font-bold text-green-700 mb-4'>✅ ما تم إصلاحه</h2>";

echo "<div class='space-y-3'>";

echo "<div class='bg-white p-4 rounded border'>";
echo "<h3 class='font-semibold mb-2'>1. إعدادات قاعدة البيانات:</h3>";
echo "<div class='text-sm'>";
echo "<div class='text-red-600'>❌ قبل: u404269408_seasonexpodb / u404269408_expo</div>";
echo "<div class='text-green-600'>✅ بعد: seasonexpodb / root</div>";
echo "</div>";
echo "</div>";

echo "<div class='bg-white p-4 rounded border'>";
echo "<h3 class='font-semibold mb-2'>2. مصدر الإعدادات:</h3>";
echo "<div class='text-sm'>";
echo "<div class='text-red-600'>❌ قبل: إعدادات خاطئة من الذاكرة</div>";
echo "<div class='text-green-600'>✅ بعد: إعدادات من ملف .env الصحيح</div>";
echo "</div>";
echo "</div>";

echo "<div class='bg-white p-4 rounded border'>";
echo "<h3 class='font-semibold mb-2'>3. التوافق مع Laravel:</h3>";
echo "<div class='text-sm'>";
echo "<div class='text-red-600'>❌ قبل: إعدادات مختلفة عن Laravel</div>";
echo "<div class='text-green-600'>✅ بعد: نفس إعدادات Laravel تماماً</div>";
echo "</div>";
echo "</div>";

echo "</div>";
echo "</div>";

// Next steps
echo "<div class='bg-blue-50 border border-blue-200 rounded-lg p-6'>";
echo "<h2 class='text-lg font-bold text-blue-700 mb-4'>🚀 الخطوات التالية</h2>";

echo "<div class='space-y-3'>";

echo "<div class='bg-white p-4 rounded border'>";
echo "<h3 class='font-semibold mb-2'>إذا نجح الاختبار:</h3>";
echo "<ul class='text-sm space-y-1 list-disc list-inside'>";
echo "<li>الآن يمكن اختبار صفحات الموقع</li>";
echo "<li>صفحة حسابي (/dashboard) ستعمل</li>";
echo "<li>قائمة المعارض (/exhibitions) ستعمل</li>";
echo "<li>نظام الحجز سيتصل بقاعدة البيانات</li>";
echo "</ul>";
echo "</div>";

echo "<div class='bg-white p-4 rounded border'>";
echo "<h3 class='font-semibold mb-2'>إذا فشل الاختبار:</h3>";
echo "<ul class='text-sm space-y-1 list-disc list-inside'>";
echo "<li>تحقق من أن XAMPP يعمل</li>";
echo "<li>تحقق من أن MySQL يعمل</li>";
echo "<li>تحقق من وجود قاعدة البيانات seasonexpodb</li>";
echo "<li>تحقق من إعدادات .env</li>";
echo "</ul>";
echo "</div>";

echo "</div>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>

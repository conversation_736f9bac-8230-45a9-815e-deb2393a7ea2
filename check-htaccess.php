<?php
echo "<h1>Check .htaccess Configuration</h1>";

$htaccessPath = __DIR__ . '/.htaccess';

echo "<h2>.htaccess File Status:</h2>";

if (file_exists($htaccessPath)) {
    echo "✅ .htaccess file exists<br>";
    echo "Size: " . filesize($htaccessPath) . " bytes<br>";
    echo "Modified: " . date('Y-m-d H:i:s', filemtime($htaccessPath)) . "<br><br>";
    
    $content = file_get_contents($htaccessPath);
    
    echo "<h3>Current .htaccess Content:</h3>";
    echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 5px;'>";
    echo htmlspecialchars($content);
    echo "</pre>";
    
    echo "<h3>Content Analysis:</h3>";
    
    if (strpos($content, 'RewriteEngine On') !== false) {
        echo "✅ RewriteEngine is enabled<br>";
    } else {
        echo "❌ RewriteEngine not found<br>";
    }
    
    if (strpos($content, 'index.php') !== false) {
        echo "✅ Contains index.php rules<br>";
    } else {
        echo "⚠️ No index.php rules found<br>";
    }
    
    // Check for problematic rules
    if (strpos($content, 'RewriteRule') !== false) {
        echo "✅ Contains rewrite rules<br>";
    } else {
        echo "⚠️ No rewrite rules found<br>";
    }
    
} else {
    echo "❌ .htaccess file not found!<br>";
    echo "<p>This could be the problem. Laravel needs an .htaccess file for URL rewriting.</p>";
}

echo "<h2>Correct Laravel .htaccess:</h2>";
echo "<pre style='background: #e8f5e8; padding: 10px; border-radius: 5px;'>";
echo htmlspecialchars('<IfModule mod_rewrite.c>
    <IfModule mod_negotiation.c>
        Options -MultiViews -Indexes
    </IfModule>

    RewriteEngine On

    # Handle Authorization Header
    RewriteCond %{HTTP:Authorization} .
    RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]

    # Redirect Trailing Slashes If Not A Folder...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_URI} (.+)/$
    RewriteRule ^ %1 [L,R=301]

    # Send Requests To Front Controller...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^ index.php [L]
</IfModule>');
echo "</pre>";

if (!file_exists($htaccessPath)) {
    echo "<h2>Create .htaccess File:</h2>";
    echo "<p><a href='create-htaccess.php' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>Create .htaccess File</a></p>";
} else {
    echo "<h2>Fix .htaccess File:</h2>";
    echo "<p><a href='fix-htaccess.php' style='background: #ffc107; color: black; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>Replace with Correct .htaccess</a></p>";
}

echo "<h2>Server Module Check:</h2>";
if (function_exists('apache_get_modules')) {
    $modules = apache_get_modules();
    if (in_array('mod_rewrite', $modules)) {
        echo "✅ mod_rewrite is enabled<br>";
    } else {
        echo "❌ mod_rewrite is not enabled<br>";
    }
} else {
    echo "⚠️ Cannot check Apache modules (might be Nginx or LiteSpeed)<br>";
}
?>

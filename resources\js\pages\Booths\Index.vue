<script setup>
import { Head, Link, router } from '@inertiajs/vue3';
import { ref, watch } from 'vue';
import LanguageSwitcher from '@/components/LanguageSwitcher.vue';

const props = defineProps({
  exhibition: Object,
  booths: Object,
  stats: Object,
  availableFeatures: Array,
  filters: Object,
  locale: String,
  translations: Object,
});

const t = (key) => {
  return props.translations?.app?.[key] || key;
};

const isRTL = props.locale === 'ar';

const selectedSize = ref(props.filters.size || '');
const selectedStatus = ref(props.filters.status || 'available');
const selectedFeatures = ref(props.filters.features || []);
const minPrice = ref(props.filters.min_price || '');
const maxPrice = ref(props.filters.max_price || '');
const cornerOnly = ref(props.filters.corner_only || false);
const sortBy = ref(props.filters.sort || 'booth_number');
const sortOrder = ref(props.filters.order || 'asc');

const formatDate = (dateString) => {
  const locale = props.locale === 'ar' ? 'ar-SA' : 'en-US';
  return new Date(dateString).toLocaleDateString(locale, {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

const formatPrice = (price, currency) => {
  const locale = props.locale === 'ar' ? 'ar-SA' : 'en-US';

  // Always use KWD for display
  if (currency === 'USD' || currency === 'KWD') {
    // Format as KWD with 3 decimal places
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency: 'KWD',
      minimumFractionDigits: 3,
      maximumFractionDigits: 3
    }).format(price);
  }

  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 3,
    maximumFractionDigits: 3
  }).format(price);
};

const getSizeColor = (size) => {
  const colors = {
    small: 'bg-green-100 text-green-800',
    medium: 'bg-blue-100 text-blue-800',
    large: 'bg-purple-100 text-purple-800',
    premium: 'bg-yellow-100 text-yellow-800'
  };
  return colors[size] || 'bg-gray-100 text-gray-800';
};

const getStatusColor = (status) => {
  const colors = {
    available: 'bg-green-100 text-green-800',
    booked: 'bg-red-100 text-red-800',
    reserved: 'bg-yellow-100 text-yellow-800',
    maintenance: 'bg-gray-100 text-gray-800'
  };
  return colors[status] || 'bg-gray-100 text-gray-800';
};

const search = () => {
  router.get(route('booths.index', props.exhibition.slug), {
    size: selectedSize.value,
    status: selectedStatus.value,
    features: selectedFeatures.value,
    min_price: minPrice.value,
    max_price: maxPrice.value,
    corner_only: cornerOnly.value,
    sort: sortBy.value,
    order: sortOrder.value,
  }, {
    preserveState: true,
    replace: true,
  });
};

const clearFilters = () => {
  selectedSize.value = '';
  selectedStatus.value = 'available';
  selectedFeatures.value = [];
  minPrice.value = '';
  maxPrice.value = '';
  cornerOnly.value = false;
  sortBy.value = 'booth_number';
  sortOrder.value = 'asc';
  search();
};

// Watch for changes and auto-search
watch([selectedSize, selectedStatus, selectedFeatures, minPrice, maxPrice, cornerOnly, sortBy, sortOrder], () => {
  search();
}, { debounce: 300 });
</script>

<template>
  <Head :title="`${t('booths')} - ${exhibition.title} - ${t('site_name')}`" />

  <div class="min-h-screen bg-gray-50" :dir="isRTL ? 'rtl' : 'ltr'">
    <!-- Fixed Navigation -->
    <nav class="fixed top-0 left-0 right-0 fixed-nav z-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex items-center">
            <Link href="/" class="text-2xl font-bold text-blue-600 hover:text-blue-700 transition-colors">
              {{ t('site_name') }}
            </Link>
          </div>
          <div class="flex items-center" :class="isRTL ? 'space-x-reverse space-x-4' : 'space-x-4'">
            <LanguageSwitcher :current-locale="locale" />
            <Link href="/exhibitions" class="nav-link text-blue-600 font-semibold">{{ t('exhibitions') }}</Link>
            <Link
              v-if="$page.props.auth?.user"
              :href="route('dashboard')"
              class="nav-link text-gray-600"
            >
              {{ t('dashboard') }}
            </Link>
            <template v-else>
              <Link
                :href="route('login')"
                class="nav-link text-gray-600"
              >
                {{ t('sign_in') }}
              </Link>
              <Link
                :href="route('register')"
                class="btn-primary bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
              >
                {{ t('get_started') }}
              </Link>
            </template>
          </div>
        </div>
      </div>
    </nav>

    <div class="py-12 pt-24">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Breadcrumb -->
        <nav class="mb-8">
          <ol class="flex items-center space-x-2 text-sm text-gray-500">
            <li><Link href="/" class="hover:text-blue-600">{{ t('home') }}</Link></li>
            <li>/</li>
            <li><Link :href="route('exhibitions.index')" class="hover:text-blue-600">{{ t('exhibitions') }}</Link></li>
            <li>/</li>
            <li><Link :href="route('exhibitions.show', exhibition.slug)" class="hover:text-blue-600">{{ exhibition.title }}</Link></li>
            <li>/</li>
            <li class="text-gray-900">{{ t('booths') }}</li>
          </ol>
        </nav>

      <!-- Header -->
      <div class="mb-8">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-3xl font-bold text-gray-900 mb-2">Available Booths</h1>
            <p class="text-gray-600">{{ exhibition.title }} • {{ exhibition.city }}, {{ exhibition.country }}</p>
            <p class="text-sm text-gray-500">{{ formatDate(exhibition.start_date) }} - {{ formatDate(exhibition.end_date) }}</p>
          </div>
          <Link
            :href="route('exhibitions.show', exhibition.slug)"
            class="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors"
          >
            ← Back to Exhibition
          </Link>
        </div>
      </div>

      <!-- Stats -->
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
        <div class="bg-white rounded-lg shadow-md p-4 text-center">
          <div class="text-2xl font-bold text-blue-600">{{ stats.total }}</div>
          <div class="text-sm text-gray-600">Total Booths</div>
        </div>
        <div class="bg-white rounded-lg shadow-md p-4 text-center">
          <div class="text-2xl font-bold text-green-600">{{ stats.available }}</div>
          <div class="text-sm text-gray-600">Available</div>
        </div>
        <div class="bg-white rounded-lg shadow-md p-4 text-center">
          <div class="text-2xl font-bold text-red-600">{{ stats.booked }}</div>
          <div class="text-sm text-gray-600">Booked</div>
        </div>
        <div class="bg-white rounded-lg shadow-md p-4 text-center">
          <div class="text-2xl font-bold text-purple-600">{{ Object.keys(stats.by_size).length }}</div>
          <div class="text-sm text-gray-600">Booth Sizes</div>
        </div>
      </div>

      <!-- Filters -->
      <div class="bg-white rounded-lg shadow-md p-6 mb-8">
        <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
          <!-- Size Filter -->
          <div>
            <label for="size" class="block text-sm font-medium text-gray-700 mb-2">Size</label>
            <select
              id="size"
              v-model="selectedSize"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Sizes</option>
              <option value="small">Small</option>
              <option value="medium">Medium</option>
              <option value="large">Large</option>
              <option value="premium">Premium</option>
            </select>
          </div>

          <!-- Status Filter -->
          <div>
            <label for="status" class="block text-sm font-medium text-gray-700 mb-2">Status</label>
            <select
              id="status"
              v-model="selectedStatus"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Status</option>
              <option value="available">Available</option>
              <option value="booked">Booked</option>
              <option value="reserved">Reserved</option>
            </select>
          </div>

          <!-- Min Price -->
          <div>
            <label for="min_price" class="block text-sm font-medium text-gray-700 mb-2">Min Price</label>
            <input
              id="min_price"
              v-model="minPrice"
              type="number"
              placeholder="0"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <!-- Max Price -->
          <div>
            <label for="max_price" class="block text-sm font-medium text-gray-700 mb-2">Max Price</label>
            <input
              id="max_price"
              v-model="maxPrice"
              type="number"
              placeholder="10000"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <!-- Sort By -->
          <div>
            <label for="sort" class="block text-sm font-medium text-gray-700 mb-2">Sort By</label>
            <select
              id="sort"
              v-model="sortBy"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="booth_number">Booth Number</option>
              <option value="price">Price</option>
              <option value="area">Area</option>
              <option value="size">Size</option>
            </select>
          </div>

          <!-- Sort Order -->
          <div>
            <label for="order" class="block text-sm font-medium text-gray-700 mb-2">Order</label>
            <select
              id="order"
              v-model="sortOrder"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="asc">Ascending</option>
              <option value="desc">Descending</option>
            </select>
          </div>
        </div>

        <!-- Additional Filters -->
        <div class="mt-4 flex flex-wrap items-center gap-4">
          <label class="flex items-center">
            <input
              v-model="cornerOnly"
              type="checkbox"
              class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span class="ml-2 text-sm text-gray-700">Corner booths only</span>
          </label>

          <div class="flex-1"></div>

          <div class="text-sm text-gray-600">
            Showing {{ booths.meta.from || 0 }} to {{ booths.meta.to || 0 }}
            of {{ booths.meta.total }} booths
          </div>

          <button
            @click="clearFilters"
            class="text-blue-600 hover:text-blue-800 text-sm font-medium"
          >
            Clear Filters
          </button>
        </div>
      </div>

      <!-- Booths Grid -->
      <div v-if="booths.data.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
        <div
          v-for="booth in booths.data"
          :key="booth.id"
          class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow"
        >
          <div class="p-6">
            <div class="flex items-center justify-between mb-3">
              <h3 class="text-lg font-semibold text-gray-900">{{ booth.booth_number }}</h3>
              <div class="flex space-x-2">
                <span :class="['px-2 py-1 rounded-full text-xs font-medium', getSizeColor(booth.size)]">
                  {{ booth.size }}
                </span>
                <span v-if="booth.is_corner" class="px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                  Corner
                </span>
                <span v-if="booth.is_featured" class="px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                  Featured
                </span>
              </div>
            </div>

            <div class="text-sm text-gray-600 mb-3">
              {{ booth.width }}m × {{ booth.height }}m ({{ booth.area }}m²)
            </div>

            <div class="text-sm text-gray-600 mb-3">
              📍 {{ booth.location }}
            </div>

            <div v-if="booth.features.length > 0" class="mb-4">
              <div class="flex flex-wrap gap-1">
                <span
                  v-for="feature in booth.features.slice(0, 3)"
                  :key="feature"
                  class="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded"
                >
                  {{ feature }}
                </span>
                <span v-if="booth.features.length > 3" class="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">
                  +{{ booth.features.length - 3 }} more
                </span>
              </div>
            </div>

            <div class="flex items-center justify-between">
              <div>
                <div class="text-xl font-bold text-blue-600">
                  {{ formatPrice(booth.price, exhibition.currency) }}
                </div>
                <span :class="['px-2 py-1 rounded-full text-xs font-medium', getStatusColor(booth.status)]">
                  {{ booth.status }}
                </span>
              </div>
              <Link
                :href="route('booths.show', [exhibition.slug, booth.id])"
                class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm"
              >
                View Details
              </Link>
            </div>
          </div>
        </div>
      </div>

      <!-- No Results -->
      <div v-else class="text-center py-12">
        <div class="text-gray-400 text-6xl mb-4">
          <svg class="w-24 h-24 mx-auto" fill="currentColor" viewBox="0 0 20 20">
            <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V4z" />
          </svg>
        </div>
        <h3 class="text-xl font-semibold text-gray-900 mb-2">No booths found</h3>
        <p class="text-gray-600 mb-4">
          Try adjusting your search criteria or browse all booths.
        </p>
        <button
          @click="clearFilters"
          class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
        >
          Clear Filters
        </button>
      </div>

      <!-- Pagination -->
      <div v-if="booths.links && booths.links.length > 3" class="flex justify-center">
        <nav class="flex space-x-2">
          <Link
            v-for="link in booths.links"
            :key="link.label"
            :href="link.url"
            :class="[
              'px-3 py-2 rounded-md text-sm font-medium',
              link.active
                ? 'bg-blue-600 text-white'
                : link.url
                  ? 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300'
                  : 'bg-gray-100 text-gray-400 cursor-not-allowed'
            ]"
          >
            {{ link.label }}
          </Link>
        </nav>
      </div>
      </div>
    </div>
  </div>
</template>

<?php
// Test Language Fix for Hostinger
// This file tests the new language switching solution

session_start();

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='utf-8'>";
echo "<title>اختبار إصلاح اللغة - Season Expo Kuwait</title>";
echo "<script src='https://cdn.tailwindcss.com'></script>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }";
echo "</style>";
echo "</head>";
echo "<body class='bg-gray-50 p-8'>";

echo "<div class='max-w-4xl mx-auto'>";
echo "<h1 class='text-3xl font-bold mb-8' style='color: #2C3E50;'>🔧 اختبار إصلاح اللغة</h1>";

// Current status
$currentLang = $_SESSION['language'] ?? $_COOKIE['language'] ?? 'ar';
$langFromUrl = $_GET['lang'] ?? null;

echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>📊 الحالة الحالية</h2>";

echo "<div class='grid grid-cols-1 md:grid-cols-2 gap-4'>";

echo "<div class='bg-blue-50 border border-blue-200 rounded-lg p-4'>";
echo "<h3 class='font-semibold mb-2'>اللغة المحفوظة:</h3>";
echo "<div class='space-y-2 text-sm'>";
echo "<div>Session: " . (isset($_SESSION['language']) ? "<span class='text-green-600'>✅ " . $_SESSION['language'] . "</span>" : "<span class='text-red-600'>❌ غير محددة</span>") . "</div>";
echo "<div>Cookie: " . (isset($_COOKIE['language']) ? "<span class='text-green-600'>✅ " . $_COOKIE['language'] . "</span>" : "<span class='text-red-600'>❌ غير محددة</span>") . "</div>";
echo "<div>URL: " . ($langFromUrl ? "<span class='text-green-600'>✅ " . $langFromUrl . "</span>" : "<span class='text-gray-600'>➖ غير محددة</span>") . "</div>";
echo "</div>";
echo "</div>";

echo "<div class='bg-green-50 border border-green-200 rounded-lg p-4'>";
echo "<h3 class='font-semibold mb-2'>اللغة النشطة:</h3>";
echo "<div class='text-2xl text-center'>";
if ($currentLang === 'ar') {
    echo "<span class='text-green-600'>🇰🇼 العربية</span>";
} else {
    echo "<span class='text-blue-600'>🇺🇸 English</span>";
}
echo "</div>";
echo "</div>";

echo "</div>";
echo "</div>";

// Test the new solution
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>🧪 اختبار الحل الجديد</h2>";

echo "<div class='text-center mb-6'>";
echo "<p class='mb-4'>اختبر تغيير اللغة باستخدام الملف الجديد:</p>";

echo "<div class='flex items-center justify-center space-x-reverse space-x-4 mb-6'>";

// Test buttons with new file
echo "<a href='/switch-language.php?lang=ar' class='inline-flex items-center px-6 py-3 border-2 rounded-lg font-semibold transition-colors' style='border-color: #2C3E50; color: #2C3E50;' onmouseover='this.style.background=\"#2C3E50\"; this.style.color=\"white\"' onmouseout='this.style.background=\"transparent\"; this.style.color=\"#2C3E50\"'>";
echo "<span class='ml-2'>🇰🇼</span>";
echo "العربية";
echo "</a>";

echo "<a href='/switch-language.php?lang=en' class='inline-flex items-center px-6 py-3 border-2 rounded-lg font-semibold transition-colors' style='border-color: #2C3E50; color: #2C3E50;' onmouseover='this.style.background=\"#2C3E50\"; this.style.color=\"white\"' onmouseout='this.style.background=\"transparent\"; this.style.color=\"#2C3E50\"'>";
echo "<span class='ml-2'>🇺🇸</span>";
echo "English";
echo "</a>";

echo "</div>";

// Debug buttons
echo "<div class='flex items-center justify-center space-x-reverse space-x-2 mb-4'>";
echo "<a href='/switch-language.php?lang=ar&debug=1' class='text-sm bg-gray-200 text-gray-700 px-3 py-1 rounded hover:bg-gray-300'>🔍 Debug العربية</a>";
echo "<a href='/switch-language.php?lang=en&debug=1' class='text-sm bg-gray-200 text-gray-700 px-3 py-1 rounded hover:bg-gray-300'>🔍 Debug English</a>";
echo "</div>";

echo "</div>";

// Results
if ($langFromUrl) {
    echo "<div class='bg-green-50 border border-green-200 rounded-lg p-4 mb-4'>";
    echo "<h3 class='font-semibold mb-2'>✅ نتيجة الاختبار:</h3>";
    echo "<p>تم تغيير اللغة إلى: <strong>" . ($langFromUrl === 'ar' ? 'العربية 🇰🇼' : 'English 🇺🇸') . "</strong></p>";
    echo "<p class='text-sm text-gray-600 mt-2'>الحل الجديد يعمل بشكل صحيح!</p>";
    echo "</div>";
}

echo "</div>";

// File status check
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>📁 فحص الملفات</h2>";

$files = [
    'switch-language.php' => 'معالج اللغة الجديد',
    'lang/.htaccess' => 'ملف إعدادات Apache',
    'lang/ar.php' => 'معالج اللغة العربية (قديم)',
    'lang/en.php' => 'معالج اللغة الإنجليزية (قديم)'
];

echo "<div class='grid grid-cols-1 md:grid-cols-2 gap-4'>";

foreach ($files as $file => $description) {
    $fullPath = __DIR__ . '/' . $file;
    echo "<div class='border border-gray-200 rounded-lg p-4'>";
    echo "<h3 class='font-semibold mb-2'>{$description}</h3>";
    echo "<p class='text-sm text-gray-600 mb-2'>/{$file}</p>";
    
    if (file_exists($fullPath)) {
        echo "<div class='text-green-600 mb-2'>✅ موجود</div>";
        
        if (strpos($file, '.php') !== false) {
            echo "<div class='mt-2'>";
            echo "<a href='/{$file}?lang=ar&debug=1' class='text-blue-600 hover:underline text-sm'>🧪 اختبار</a>";
            echo "</div>";
        }
        
    } else {
        echo "<div class='text-red-600'>❌ مفقود</div>";
    }
    echo "</div>";
}

echo "</div>";
echo "</div>";

// Solutions
echo "<div class='bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>💡 الحلول المطبقة</h2>";

echo "<div class='space-y-4'>";

echo "<div class='bg-white p-4 rounded-lg border'>";
echo "<h3 class='font-semibold mb-2' style='color: #2C3E50;'>1. ملف معالج جديد:</h3>";
echo "<ul class='text-sm space-y-1 list-disc list-inside ml-4'>";
echo "<li><code>switch-language.php</code> - ملف واحد لجميع اللغات</li>";
echo "<li>يعمل مع معاملات URL: <code>?lang=ar</code> أو <code>?lang=en</code></li>";
echo "<li>لا يحتاج صلاحيات خاصة</li>";
echo "</ul>";
echo "</div>";

echo "<div class='bg-white p-4 rounded-lg border'>";
echo "<h3 class='font-semibold mb-2' style='color: #2C3E50;'>2. ملف .htaccess:</h3>";
echo "<ul class='text-sm space-y-1 list-disc list-inside ml-4'>";
echo "<li>يحل مشكلة 403 Forbidden</li>";
echo "<li>يسمح بتشغيل ملفات PHP في مجلد lang</li>";
echo "<li>يضبط الصلاحيات تلقائياً</li>";
echo "</ul>";
echo "</div>";

echo "<div class='bg-white p-4 rounded-lg border'>";
echo "<h3 class='font-semibold mb-2' style='color: #2C3E50;'>3. تحديث الروابط:</h3>";
echo "<ul class='text-sm space-y-1 list-disc list-inside ml-4'>";
echo "<li>تم تحديث جميع الروابط لتستخدم الملف الجديد</li>";
echo "<li>يعمل مع جميع المتصفحات</li>";
echo "<li>متوافق مع Hostinger</li>";
echo "</ul>";
echo "</div>";

echo "</div>";
echo "</div>";

// Instructions
echo "<div class='bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>📋 خطوات التطبيق</h2>";

echo "<div class='space-y-4'>";

echo "<div>";
echo "<h3 class='font-semibold mb-2'>1. ارفع الملفات الجديدة:</h3>";
echo "<ul class='text-sm space-y-1 list-disc list-inside ml-4'>";
echo "<li><code>switch-language.php</code> - في المجلد الرئيسي</li>";
echo "<li><code>lang/.htaccess</code> - في مجلد lang</li>";
echo "<li><code>homepage-fixed.php</code> - محدث</li>";
echo "</ul>";
echo "</div>";

echo "<div>";
echo "<h3 class='font-semibold mb-2'>2. اختبر الوظيفة:</h3>";
echo "<ul class='text-sm space-y-1 list-disc list-inside ml-4'>";
echo "<li>استخدم الأزرار أعلاه للاختبار</li>";
echo "<li>تحقق من حفظ اللغة في Session و Cookie</li>";
echo "<li>اختبر في الصفحة الرئيسية</li>";
echo "</ul>";
echo "</div>";

echo "<div>";
echo "<h3 class='font-semibold mb-2'>3. في حالة استمرار المشاكل:</h3>";
echo "<ul class='text-sm space-y-1 list-disc list-inside ml-4'>";
echo "<li>تحقق من صلاحيات الملفات (644 للملفات، 755 للمجلدات)</li>";
echo "<li>تأكد من رفع ملف .htaccess</li>";
echo "<li>امسح كاش المتصفح</li>";
echo "<li>استخدم أزرار Debug للتشخيص</li>";
echo "</ul>";
echo "</div>";

echo "</div>";
echo "</div>";

// Navigation
echo "<div class='text-center space-x-reverse space-x-4'>";
echo "<a href='/homepage-fixed.php' class='text-white px-6 py-3 rounded-lg hover:opacity-80' style='background: #2C3E50;'>🏠 الصفحة الرئيسية</a>";
echo "<a href='/check-language-setup.php' class='bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700'>🔍 فحص الإعداد</a>";
echo "<a href='/admin-slider-management.php' class='bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700'>🖼️ إدارة السلايد</a>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>

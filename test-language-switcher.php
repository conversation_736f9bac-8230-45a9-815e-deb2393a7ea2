<?php
// Test Language Switcher
// This file tests the language switching functionality

session_start();

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='utf-8'>";
echo "<title>اختبار تغيير اللغة - Season Expo Kuwait</title>";
echo "<script src='https://cdn.tailwindcss.com'></script>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }";
echo "</style>";
echo "</head>";
echo "<body class='bg-gray-50 p-8'>";

echo "<div class='max-w-4xl mx-auto'>";
echo "<h1 class='text-3xl font-bold mb-8' style='color: #2C3E50;'>🌐 اختبار تغيير اللغة</h1>";

// Current language status
$currentLang = $_SESSION['language'] ?? $_COOKIE['language'] ?? 'ar';
$langFromUrl = $_GET['lang'] ?? null;

echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>📊 حالة اللغة الحالية</h2>";

echo "<div class='grid grid-cols-1 md:grid-cols-3 gap-4'>";

echo "<div class='bg-blue-50 border border-blue-200 rounded-lg p-4'>";
echo "<h3 class='font-semibold mb-2'>اللغة من الجلسة:</h3>";
echo "<div class='text-lg'>";
if (isset($_SESSION['language'])) {
    echo "<span class='text-green-600'>✅ " . $_SESSION['language'] . "</span>";
} else {
    echo "<span class='text-red-600'>❌ غير محددة</span>";
}
echo "</div>";
echo "</div>";

echo "<div class='bg-green-50 border border-green-200 rounded-lg p-4'>";
echo "<h3 class='font-semibold mb-2'>اللغة من الكوكيز:</h3>";
echo "<div class='text-lg'>";
if (isset($_COOKIE['language'])) {
    echo "<span class='text-green-600'>✅ " . $_COOKIE['language'] . "</span>";
} else {
    echo "<span class='text-red-600'>❌ غير محددة</span>";
}
echo "</div>";
echo "</div>";

echo "<div class='bg-yellow-50 border border-yellow-200 rounded-lg p-4'>";
echo "<h3 class='font-semibold mb-2'>اللغة من URL:</h3>";
echo "<div class='text-lg'>";
if ($langFromUrl) {
    echo "<span class='text-green-600'>✅ " . $langFromUrl . "</span>";
} else {
    echo "<span class='text-gray-600'>➖ غير محددة</span>";
}
echo "</div>";
echo "</div>";

echo "</div>";
echo "</div>";

// Language switcher test
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>🔧 اختبار تغيير اللغة</h2>";

echo "<div class='flex items-center justify-center space-x-reverse space-x-4 mb-6'>";

// Arabic button
echo "<a href='/language/ar.php' class='inline-flex items-center px-6 py-3 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors'>";
echo "<span class='ml-2'>🇰🇼</span>";
echo "العربية";
echo "</a>";

// English button
echo "<a href='/language/en.php' class='inline-flex items-center px-6 py-3 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors'>";
echo "<span class='ml-2'>🇺🇸</span>";
echo "English";
echo "</a>";

echo "</div>";

// Interactive language switcher (like in homepage)
echo "<div class='text-center mb-6'>";
echo "<h3 class='font-semibold mb-4'>زر تغيير اللغة التفاعلي:</h3>";

echo "<div class='relative inline-block text-left'>";
echo "<div class='group'>";
echo "<button type='button' class='inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500' onclick='toggleLanguageMenu()'>";
echo "<span class='ml-2'>🇰🇼</span>";
echo "العربية";
echo "<svg class='mr-2 h-4 w-4' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='currentColor'>";
echo "<path fill-rule='evenodd' d='M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z' clip-rule='evenodd' />";
echo "</svg>";
echo "</button>";
echo "<div id='languageMenu' class='absolute left-0 z-10 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 opacity-0 invisible transition-all duration-200'>";
echo "<div class='py-1' role='menu'>";
echo "<a href='/language/ar.php' class='flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900' role='menuitem'>";
echo "<span class='ml-3'>🇰🇼</span>";
echo "العربية";
echo "</a>";
echo "<a href='/language/en.php' class='flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900' role='menuitem'>";
echo "<span class='ml-3'>🇺🇸</span>";
echo "English";
echo "</a>";
echo "</div>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "</div>";
echo "</div>";

// Test results
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>📋 نتائج الاختبار</h2>";

echo "<div class='space-y-4'>";

// Check if language files exist
$languageFiles = [
    '/language/ar.php' => 'معالج اللغة العربية',
    '/language/en.php' => 'معالج اللغة الإنجليزية',
    '/language-switcher.php' => 'معالج تغيير اللغة العام'
];

foreach ($languageFiles as $file => $description) {
    $fullPath = __DIR__ . $file;
    echo "<div class='flex items-center justify-between p-3 border border-gray-200 rounded-lg'>";
    echo "<div>";
    echo "<h3 class='font-semibold'>{$description}</h3>";
    echo "<p class='text-sm text-gray-600'>{$file}</p>";
    echo "</div>";
    echo "<div>";
    if (file_exists($fullPath)) {
        echo "<span class='text-green-600'>✅ موجود</span>";
    } else {
        echo "<span class='text-red-600'>❌ مفقود</span>";
    }
    echo "</div>";
    echo "</div>";
}

echo "</div>";
echo "</div>";

// Instructions
echo "<div class='bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>📝 تعليمات الاستخدام</h2>";

echo "<div class='space-y-4'>";

echo "<div>";
echo "<h3 class='font-semibold mb-2'>1. للاختبار السريع:</h3>";
echo "<p class='text-sm'>اضغط على أزرار اللغة أعلاه وتحقق من تغيير اللغة في الجلسة والكوكيز.</p>";
echo "</div>";

echo "<div>";
echo "<h3 class='font-semibold mb-2'>2. للاختبار في الصفحة الرئيسية:</h3>";
echo "<p class='text-sm'>اذهب إلى الصفحة الرئيسية واستخدم زر تغيير اللغة في شريط التنقل.</p>";
echo "</div>";

echo "<div>";
echo "<h3 class='font-semibold mb-2'>3. للتحقق من الثبات:</h3>";
echo "<p class='text-sm'>غير اللغة ثم أعد تحميل الصفحة للتأكد من حفظ الإعداد.</p>";
echo "</div>";

echo "</div>";
echo "</div>";

// Navigation
echo "<div class='text-center space-x-reverse space-x-4'>";
echo "<a href='/homepage-fixed.php' class='text-white px-6 py-3 rounded-lg hover:opacity-80' style='background: #2C3E50;'>🏠 الصفحة الرئيسية</a>";
echo "<a href='/exhibitions-simple.php' class='bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700'>📋 المعارض</a>";
echo "<a href='/admin-slider-management.php' class='bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700'>🖼️ إدارة السلايد</a>";
echo "</div>";

echo "</div>";

// JavaScript for interactive menu
echo "<script>";
echo "function toggleLanguageMenu() {";
echo "    const menu = document.getElementById('languageMenu');";
echo "    const isVisible = menu.classList.contains('opacity-100');";
echo "    ";
echo "    if (isVisible) {";
echo "        menu.classList.remove('opacity-100', 'visible');";
echo "        menu.classList.add('opacity-0', 'invisible');";
echo "    } else {";
echo "        menu.classList.remove('opacity-0', 'invisible');";
echo "        menu.classList.add('opacity-100', 'visible');";
echo "    }";
echo "}";
echo "";
echo "// Close menu when clicking outside";
echo "document.addEventListener('click', function(event) {";
echo "    const menu = document.getElementById('languageMenu');";
echo "    const button = event.target.closest('button');";
echo "    ";
echo "    if (!button || button.getAttribute('onclick') !== 'toggleLanguageMenu()') {";
echo "        menu.classList.remove('opacity-100', 'visible');";
echo "        menu.classList.add('opacity-0', 'invisible');";
echo "    }";
echo "});";
echo "</script>";

echo "</body>";
echo "</html>";
?>

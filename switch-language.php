<?php
// Language Switcher for Hostinger Server
// This file handles language switching with proper permissions

session_start();

// Get the requested language
$lang = $_GET['lang'] ?? $_POST['lang'] ?? 'ar';

// Validate language
$supportedLanguages = ['ar', 'en'];
if (!in_array($lang, $supportedLanguages)) {
    $lang = 'ar'; // Default to Arabic
}

// Set language in session
$_SESSION['language'] = $lang;
$_SESSION['locale'] = $lang;

// Set persistent cookie (30 days)
setcookie('language', $lang, time() + (30 * 24 * 60 * 60), '/');
setcookie('locale', $lang, time() + (30 * 24 * 60 * 60), '/');

// Get referrer URL
$referrer = $_SERVER['HTTP_REFERER'] ?? '/';

// Clean referrer from language parameters and get base path
$referrer = preg_replace('/[?&]lang=[^&]*/', '', $referrer);
$referrer = preg_replace('/[?&]locale=[^&]*/', '', $referrer);

// Determine redirect URL based on language
if ($lang === 'ar') {
    $redirectUrl = '/';  // Arabic is default, so just go to root
} else {
    $redirectUrl = '/en'; // English gets /en
}

// If referrer contains specific pages, maintain the path
if (strpos($referrer, 'login') !== false) {
    $redirectUrl = $lang === 'ar' ? '/login-simple.php?lang=ar' : '/en/login';
} elseif (strpos($referrer, 'exhibitions') !== false) {
    $redirectUrl = $lang === 'ar' ? '/exhibitions-simple.php?lang=ar' : '/en/exhibitions';
}

// If referrer is this file or switch-language, go to homepage
if (strpos($referrer, 'switch-language.php') !== false) {
    $redirectUrl = $lang === 'ar' ? '/' : '/en';
}

// Success message for debugging
if (isset($_GET['debug'])) {
    echo "<!DOCTYPE html>";
    echo "<html dir='rtl' lang='ar'>";
    echo "<head><meta charset='utf-8'><title>Language Switch Debug</title></head>";
    echo "<body style='font-family: Arial; padding: 20px;'>";
    echo "<h2>🔧 Language Switch Debug</h2>";
    echo "<p><strong>Language set to:</strong> " . $lang . "</p>";
    echo "<p><strong>Session:</strong> " . ($_SESSION['language'] ?? 'Not set') . "</p>";
    echo "<p><strong>Cookie:</strong> " . ($_COOKIE['language'] ?? 'Not set') . "</p>";
    echo "<p><strong>Redirect URL:</strong> " . $redirectUrl . "</p>";
    echo "<p><a href='" . $redirectUrl . "'>Continue to page</a></p>";
    echo "</body></html>";
    exit;
}

// Redirect back
header('Location: ' . $redirectUrl);
exit;
?>

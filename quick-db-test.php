<?php
// Quick Database Test
// Simple test to check database connection

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='utf-8'>";
echo "<title>اختبار سريع لقاعدة البيانات</title>";
echo "<script src='https://cdn.tailwindcss.com'></script>";
echo "</head>";
echo "<body class='bg-gray-50 p-8'>";

echo "<div class='max-w-2xl mx-auto'>";
echo "<h1 class='text-2xl font-bold mb-6'>🔍 اختبار سريع لقاعدة البيانات</h1>";

// Test the includes/database.php file
echo "<div class='bg-white rounded-lg shadow p-6 mb-6'>";
echo "<h2 class='text-lg font-bold mb-4'>اختبار includes/database.php</h2>";

try {
    require_once 'includes/database.php';
    $pdo = getDatabaseConnection();
    
    // Test exhibitions table
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM exhibitions");
    $exhibitions = $stmt->fetch()['count'];
    
    // Test booths table
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM booths");
    $booths = $stmt->fetch()['count'];
    
    echo "<div class='bg-green-50 border border-green-200 rounded p-4'>";
    echo "<h3 class='font-bold text-green-800 mb-2'>✅ الاتصال ناجح!</h3>";
    echo "<div class='text-green-700'>";
    echo "<p>عدد المعارض: <strong>{$exhibitions}</strong></p>";
    echo "<p>عدد الأجنحة: <strong>{$booths}</strong></p>";
    echo "</div>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='bg-red-50 border border-red-200 rounded p-4'>";
    echo "<h3 class='font-bold text-red-800 mb-2'>❌ فشل الاتصال</h3>";
    echo "<p class='text-red-700'>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "</div>";

// Test the original config-database.php file
echo "<div class='bg-white rounded-lg shadow p-6 mb-6'>";
echo "<h2 class='text-lg font-bold mb-4'>اختبار config-database.php الأصلي</h2>";

try {
    require_once 'config-database.php';
    $pdo2 = getDatabaseConnection();
    
    // Test exhibitions table
    $stmt = $pdo2->query("SELECT COUNT(*) as count FROM exhibitions");
    $exhibitions2 = $stmt->fetch()['count'];
    
    // Test booths table
    $stmt = $pdo2->query("SELECT COUNT(*) as count FROM booths");
    $booths2 = $stmt->fetch()['count'];
    
    echo "<div class='bg-green-50 border border-green-200 rounded p-4'>";
    echo "<h3 class='font-bold text-green-800 mb-2'>✅ الاتصال ناجح!</h3>";
    echo "<div class='text-green-700'>";
    echo "<p>عدد المعارض: <strong>{$exhibitions2}</strong></p>";
    echo "<p>عدد الأجنحة: <strong>{$booths2}</strong></p>";
    echo "</div>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='bg-red-50 border border-red-200 rounded p-4'>";
    echo "<h3 class='font-bold text-red-800 mb-2'>❌ فشل الاتصال</h3>";
    echo "<p class='text-red-700'>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "</div>";

// Manual test with different configurations
$testConfigs = [
    'الإعدادات المحفوظة' => [
        'host' => 'localhost',
        'database' => 'u404269408_seasonexpodb',
        'username' => 'u404269408_expo',
        'password' => 'SeasonExpo2024!'
    ],
    'إعدادات بديلة 1' => [
        'host' => 'localhost',
        'database' => 'u404269408_season_expo',
        'username' => 'u404269408_season_user',
        'password' => 'YourDatabasePassword123'
    ],
    'إعدادات بديلة 2' => [
        'host' => 'localhost',
        'database' => 'u404269408_seasonexpo',
        'username' => 'u404269408_seasonexpo',
        'password' => 'SeasonExpo2024!'
    ]
];

foreach ($testConfigs as $name => $config) {
    echo "<div class='bg-white rounded-lg shadow p-6 mb-4'>";
    echo "<h2 class='text-lg font-bold mb-4'>{$name}</h2>";
    
    try {
        $dsn = "mysql:host={$config['host']};dbname={$config['database']};charset=utf8mb4";
        $pdo = new PDO($dsn, $config['username'], $config['password'], [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]);
        
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM exhibitions");
        $exhibitions = $stmt->fetch()['count'];
        
        echo "<div class='bg-green-50 border border-green-200 rounded p-4'>";
        echo "<h3 class='font-bold text-green-800 mb-2'>✅ يعمل!</h3>";
        echo "<p class='text-green-700'>عدد المعارض: <strong>{$exhibitions}</strong></p>";
        echo "<div class='mt-2 text-xs text-green-600'>";
        echo "Database: {$config['database']}<br>";
        echo "Username: {$config['username']}";
        echo "</div>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div class='bg-red-50 border border-red-200 rounded p-4'>";
        echo "<h3 class='font-bold text-red-800 mb-2'>❌ لا يعمل</h3>";
        echo "<p class='text-red-700 text-sm'>" . htmlspecialchars($e->getMessage()) . "</p>";
        echo "</div>";
    }
    
    echo "</div>";
}

echo "<div class='bg-blue-50 border border-blue-200 rounded p-6'>";
echo "<h2 class='text-lg font-bold text-blue-700 mb-4'>📋 الخطوات التالية</h2>";
echo "<ol class='list-decimal list-inside space-y-2 text-blue-700'>";
echo "<li>تحقق من النتائج أعلاه</li>";
echo "<li>استخدم الإعدادات التي تظهر ✅ يعمل</li>";
echo "<li>حدث ملف includes/database.php بالإعدادات الصحيحة</li>";
echo "<li>اختبر صفحات الموقع مرة أخرى</li>";
echo "</ol>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>

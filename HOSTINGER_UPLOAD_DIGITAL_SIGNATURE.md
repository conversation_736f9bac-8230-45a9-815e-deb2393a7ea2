# 📋 دليل رفع ملفات التوقيع الإلكتروني إلى Hostinger

## 🎯 **الهدف:**
رفع جميع ملفات نظام التوقيع الإلكتروني إلى سيرفر Hostinger بشكل منظم ومرتب.

---

## 📁 **قائمة الملفات المطلوب رفعها:**

### ✅ **1. ملفات العرض (Views) - الأولوية العالية:**

#### المسار على السيرفر: `resources/views/signatures/`

| الملف المحلي | المسار على السيرفر | الحالة |
|--------------|-------------------|--------|
| `resources/views/signatures/company-declaration.blade.php` | `resources/views/signatures/company-declaration.blade.php` | ⏳ جاهز للرفع |
| `resources/views/signatures/create-simple.blade.php` | `resources/views/signatures/create-simple.blade.php` | ⏳ جاهز للرفع |
| `resources/views/signatures/view-signature.blade.php` | `resources/views/signatures/view-signature.blade.php` | ⏳ جاهز للرفع |

### ✅ **2. ملف المسارات - الأولوية العالية:**

| الملف المحلي | المسار على السيرفر | الحالة |
|--------------|-------------------|--------|
| `routes/web.php` | `routes/web.php` | ⏳ جاهز للرفع |

**⚠️ تحذير مهم:** احتفظ بنسخة احتياطية من `routes/web.php` الحالي على السيرفر قبل الاستبدال!

### ✅ **3. ملفات الاختبار - اختيارية:**

| الملف المحلي | المسار على السيرفر | الحالة |
|--------------|-------------------|--------|
| `test-digital-signature.php` | `test-digital-signature.php` | ⏳ جاهز للرفع |

### ✅ **4. ملفات التوثيق - اختيارية:**

| الملف المحلي | المسار على السيرفر | الحالة |
|--------------|-------------------|--------|
| `digital-signature-guide.md` | `digital-signature-guide.md` | ⏳ جاهز للرفع |
| `DIGITAL_SIGNATURE_SUMMARY.md` | `DIGITAL_SIGNATURE_SUMMARY.md` | ⏳ جاهز للرفع |
| `DASHBOARD_TO_ACCOUNT_CHANGES.md` | `DASHBOARD_TO_ACCOUNT_CHANGES.md` | ⏳ جاهز للرفع |

---

## 🚀 **خطة الرفع المرحلية:**

### **المرحلة 1: التحضير (5 دقائق)**
- [ ] الدخول إلى لوحة تحكم Hostinger
- [ ] فتح File Manager
- [ ] التنقل إلى مجلد الموقع
- [ ] إنشاء نسخة احتياطية من `routes/web.php`

### **المرحلة 2: رفع ملفات العرض (10 دقائق)**
- [ ] رفع `company-declaration.blade.php`
- [ ] رفع `create-simple.blade.php`
- [ ] رفع `view-signature.blade.php`
- [ ] التحقق من رفع الملفات بنجاح

### **المرحلة 3: رفع ملف المسارات (5 دقائق)**
- [ ] عمل نسخة احتياطية من `routes/web.php` الحالي
- [ ] رفع `routes/web.php` الجديد
- [ ] التحقق من عدم وجود أخطاء

### **المرحلة 4: رفع ملفات الاختبار (5 دقائق)**
- [ ] رفع `test-digital-signature.php`
- [ ] رفع ملفات التوثيق

### **المرحلة 5: الاختبار والتحقق (10 دقائق)**
- [ ] مسح الكاش
- [ ] اختبار الصفحات
- [ ] التحقق من عمل النظام

---

## 📝 **تعليمات الرفع التفصيلية:**

### **الخطوة 1: الدخول إلى Hostinger**
1. اذهب إلى [hpanel.hostinger.com](https://hpanel.hostinger.com)
2. سجل دخولك
3. اختر موقعك
4. اضغط على "File Manager"

### **الخطوة 2: التنقل للمجلد الصحيح**
1. انتقل إلى مجلد `public_html` أو مجلد موقعك
2. تأكد من وجود مجلدات `resources` و `routes`

### **الخطوة 3: إنشاء النسخة الاحتياطية**
```
1. اذهب إلى routes/
2. انقر بالزر الأيمن على web.php
3. اختر "Copy"
4. أعد تسميته إلى web_backup_[التاريخ].php
```

### **الخطوة 4: رفع الملفات**
لكل ملف:
1. انتقل للمجلد المطلوب
2. اضغط "Upload Files"
3. اختر الملف من جهازك
4. انتظر اكتمال الرفع
5. تحقق من وجود الملف

---

## 🧪 **اختبار ما بعد الرفع:**

### **اختبارات أساسية:**
- [ ] `yourdomain.com/signatures` - يجب أن تعمل
- [ ] `yourdomain.com/signatures/company-declaration` - يجب أن تعمل
- [ ] `yourdomain.com/signatures/create-simple` - يجب أن تعمل
- [ ] `yourdomain.com/test-digital-signature.php` - يجب أن تعمل

### **اختبارات متقدمة:**
- [ ] إنشاء توقيع تجريبي
- [ ] عرض التوقيع
- [ ] التحقق من قاعدة البيانات

---

## ⚠️ **تحذيرات مهمة:**

### **قبل الرفع:**
- ✅ احتفظ بنسخة احتياطية من `routes/web.php`
- ✅ تأكد من أن قاعدة البيانات تحتوي على جدول `digital_signatures`
- ✅ تحقق من صلاحيات مجلد `storage`

### **أثناء الرفع:**
- ⚠️ لا تحذف الملفات القديمة قبل التأكد من عمل الجديدة
- ⚠️ ارفع ملف واحد في كل مرة
- ⚠️ تحقق من كل ملف بعد رفعه

### **بعد الرفع:**
- 🔄 امسح الكاش: `yourdomain.com/clear-all-caches.php`
- 🧪 اختبر كل صفحة
- 📊 تحقق من ملفات السجلات

---

## 🆘 **في حالة المشاكل:**

### **مشاكل شائعة وحلولها:**

| المشكلة | السبب المحتمل | الحل |
|---------|---------------|------|
| صفحة 404 | مسار خاطئ | تحقق من المسار |
| خطأ 500 | خطأ في الكود | راجع ملف السجلات |
| التوقيع لا يعمل | مشكلة في قاعدة البيانات | تحقق من الجدول |
| الصفحة فارغة | مشكلة في الكاش | امسح الكاش |

### **ملفات مهمة للفحص:**
- `storage/logs/laravel.log` - للأخطاء
- `.env` - للإعدادات
- `database/database.sqlite` - لقاعدة البيانات

---

## 📞 **الدعم:**

إذا واجهت مشاكل:
1. تحقق من ملفات السجلات أولاً
2. تأكد من صحة المسارات
3. امسح الكاش
4. تحقق من صلاحيات الملفات

---

**🎯 الهدف:** رفع نظام التوقيع الإلكتروني بنجاح دون التأثير على باقي الموقع

**⏱️ الوقت المتوقع:** 30-45 دقيقة

**📋 الحالة:** جاهز للبدء

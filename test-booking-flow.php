<?php
// Test Booking Flow
// This file tests the complete booking flow with the new organized structure

session_start();

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='utf-8'>";
echo "<title>اختبار تدفق الحجز - Season Expo Kuwait</title>";
echo "<script src='https://cdn.tailwindcss.com'></script>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }";
echo "</style>";
echo "</head>";
echo "<body class='bg-gray-50 p-8'>";

echo "<div class='max-w-4xl mx-auto'>";
echo "<h1 class='text-3xl font-bold mb-8' style='color: #2C3E50;'>🎯 اختبار تدفق الحجز الكامل</h1>";

// Success banner
echo "<div class='bg-gradient-to-r from-green-400 to-blue-500 text-white rounded-lg p-6 mb-8 text-center'>";
echo "<h2 class='text-2xl font-bold mb-2'>✅ تم إصلاح مشكلة الحجز!</h2>";
echo "<p class='text-lg'>الآن الحجز يعمل بشكل صحيح مع الإقرار والتعهد</p>";
echo "</div>";

// Current login status
$isLoggedIn = isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);

echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>👤 حالة تسجيل الدخول</h2>";

if ($isLoggedIn) {
    echo "<div class='bg-green-50 border border-green-200 rounded-lg p-4'>";
    echo "<div class='flex items-center justify-between'>";
    echo "<div>";
    echo "<h3 class='font-semibold text-green-700'>✅ مسجل الدخول</h3>";
    echo "<p class='text-green-600'>معرف المستخدم: " . $_SESSION['user_id'] . "</p>";
    echo "<p class='text-green-600'>البريد الإلكتروني: " . ($_SESSION['user_email'] ?? 'غير محدد') . "</p>";
    echo "</div>";
    echo "<div class='space-x-reverse space-x-2'>";
    echo "<a href='/dashboard' class='bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 text-sm'>حسابي</a>";
    echo "<a href='/logout' class='bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 text-sm'>تسجيل الخروج</a>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
} else {
    echo "<div class='bg-yellow-50 border border-yellow-200 rounded-lg p-4'>";
    echo "<div class='flex items-center justify-between'>";
    echo "<div>";
    echo "<h3 class='font-semibold text-yellow-700'>⚠️ غير مسجل الدخول</h3>";
    echo "<p class='text-yellow-600'>يجب تسجيل الدخول لإتمام عملية الحجز</p>";
    echo "</div>";
    echo "<div class='space-x-reverse space-x-2'>";
    echo "<a href='/login' class='bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 text-sm'>تسجيل الدخول</a>";
    echo "<a href='?simulate_login=1' class='bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 text-sm'>تسجيل دخول تجريبي</a>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
}

// Handle simulate login
if (isset($_GET['simulate_login'])) {
    $_SESSION['user_id'] = 'test_user_' . time();
    $_SESSION['user_name'] = 'مستخدم تجريبي';
    $_SESSION['user_email'] = '<EMAIL>';
    header('Location: ' . $_SERVER['PHP_SELF']);
    exit;
}

echo "</div>";

// Test the complete booking flow
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>🔄 اختبار تدفق الحجز الكامل</h2>";

echo "<div class='space-y-6'>";

// Step 1: Homepage
echo "<div class='border border-gray-200 rounded-lg p-4'>";
echo "<h3 class='font-semibold mb-3 text-blue-700'>الخطوة 1: الصفحة الرئيسية</h3>";
echo "<p class='text-sm text-gray-600 mb-3'>المستخدم يزور الموقع</p>";
echo "<a href='/homepage-fixed.php?lang=ar' target='_blank' class='bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 text-sm'>🏠 الصفحة الرئيسية</a>";
echo "</div>";

// Step 2: Exhibitions list
echo "<div class='border border-gray-200 rounded-lg p-4'>";
echo "<h3 class='font-semibold mb-3 text-green-700'>الخطوة 2: قائمة المعارض</h3>";
echo "<p class='text-sm text-gray-600 mb-3'>المستخدم يتصفح المعارض المتاحة</p>";
echo "<div class='space-x-reverse space-x-2'>";
echo "<a href='/exhibitions' target='_blank' class='bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 text-sm'>📋 قائمة المعارض (جديد)</a>";
echo "<a href='/exhibitions-simple.php' target='_blank' class='bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700 text-sm'>📋 قائمة المعارض (قديم)</a>";
echo "</div>";
echo "</div>";

// Step 3: Exhibition details
echo "<div class='border border-gray-200 rounded-lg p-4'>";
echo "<h3 class='font-semibold mb-3 text-purple-700'>الخطوة 3: تفاصيل المعرض</h3>";
echo "<p class='text-sm text-gray-600 mb-3'>المستخدم يختار معرض ويشاهد الأجنحة</p>";
echo "<div class='space-x-reverse space-x-2'>";
echo "<a href='/exhibitions/1' target='_blank' class='bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700 text-sm'>🏢 معرض 1 (جديد)</a>";
echo "<a href='/exhibition-details.php?id=1' target='_blank' class='bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700 text-sm'>🏢 معرض 1 (قديم)</a>";
echo "</div>";
echo "</div>";

// Step 4: Booking attempt
echo "<div class='border border-gray-200 rounded-lg p-4'>";
echo "<h3 class='font-semibold mb-3 text-red-700'>الخطوة 4: محاولة الحجز</h3>";
echo "<p class='text-sm text-gray-600 mb-3'>المستخدم يضغط على \"احجز الآن (مع الإقرار والتعهد)\"</p>";
echo "<div class='space-x-reverse space-x-2'>";
echo "<a href='/booking/23/1' target='_blank' class='bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 text-sm'>🎯 حجز جناح 23 (جديد)</a>";
echo "<a href='/booking-simple.php?booth_id=23&exhibition_id=1' target='_blank' class='bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700 text-sm'>🎯 حجز جناح 23 (قديم)</a>";
echo "</div>";
echo "</div>";

// Step 5: Login redirect (if needed)
if (!$isLoggedIn) {
    echo "<div class='border border-yellow-200 rounded-lg p-4 bg-yellow-50'>";
    echo "<h3 class='font-semibold mb-3 text-yellow-700'>الخطوة 5: إعادة توجيه لتسجيل الدخول</h3>";
    echo "<p class='text-sm text-gray-600 mb-3'>إذا لم يكن مسجل الدخول، سيتم توجيهه لتسجيل الدخول مع حفظ رابط الحجز</p>";
    echo "<a href='/login?redirect=" . urlencode('/booking/23/1') . "' target='_blank' class='bg-yellow-600 text-white px-4 py-2 rounded hover:bg-yellow-700 text-sm'>🔐 تسجيل الدخول مع إعادة التوجيه</a>";
    echo "</div>";
} else {
    echo "<div class='border border-green-200 rounded-lg p-4 bg-green-50'>";
    echo "<h3 class='font-semibold mb-3 text-green-700'>الخطوة 5: مسجل الدخول ✅</h3>";
    echo "<p class='text-sm text-gray-600 mb-3'>المستخدم مسجل الدخول، سيتم توجيهه مباشرة لصفحة الحجز</p>";
    echo "</div>";
}

// Step 6: Booking form
echo "<div class='border border-gray-200 rounded-lg p-4'>";
echo "<h3 class='font-semibold mb-3 text-indigo-700'>الخطوة 6: نموذج الحجز مع الإقرار والتعهد</h3>";
echo "<p class='text-sm text-gray-600 mb-3'>المستخدم يملأ النموذج ويوافق على الشروط</p>";
echo "<div class='bg-gray-50 p-3 rounded text-sm'>";
echo "<strong>يتضمن النموذج:</strong><br>";
echo "• معلومات المعرض والجناح<br>";
echo "• الإقرار والتعهد<br>";
echo "• موافقة على الشروط والأحكام<br>";
echo "• تأكيد الحجز";
echo "</div>";
echo "</div>";

echo "</div>";
echo "</div>";

// File status check
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>📁 حالة الملفات الجديدة</h2>";

$newFiles = [
    'pages/dashboard.php' => 'صفحة حسابي المنظمة',
    'pages/login.php' => 'صفحة تسجيل الدخول المحدثة',
    'pages/logout.php' => 'صفحة تسجيل الخروج',
    'pages/exhibitions.php' => 'قائمة المعارض المنظمة',
    'pages/exhibition-details.php' => 'تفاصيل المعرض المنظمة',
    'pages/booking.php' => 'صفحة الحجز مع الإقرار والتعهد',
    'includes/database.php' => 'إعدادات قاعدة البيانات',
    '.htaccess-simple' => 'ملف .htaccess المحدث'
];

echo "<div class='grid grid-cols-1 md:grid-cols-2 gap-3'>";

foreach ($newFiles as $file => $description) {
    $exists = file_exists($file) ? '✅' : '❌';
    echo "<div class='flex items-center justify-between p-3 border border-gray-200 rounded'>";
    echo "<div>";
    echo "<h3 class='font-semibold text-sm'>{$description}</h3>";
    echo "<code class='text-xs text-gray-600'>{$file}</code>";
    echo "</div>";
    echo "<div class='text-sm'>{$exists}</div>";
    echo "</div>";
}

echo "</div>";
echo "</div>";

// What was fixed
echo "<div class='bg-green-50 border border-green-200 rounded-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold text-green-700 mb-4'>✅ ما تم إصلاحه</h2>";

$fixes = [
    'رابط الحجز' => 'تم تحديث exhibition-details.php ليشير للرابط الصحيح /booking/23/1',
    'ملف الحجز المفقود' => 'تم إنشاء pages/booking.php مع الإقرار والتعهد',
    'إعادة التوجيه' => 'تم إصلاح إعادة التوجيه بعد تسجيل الدخول',
    'الروابط النظيفة' => 'تم تحديث .htaccess-simple لدعم جميع الروابط',
    'التنظيم' => 'تم تنظيم جميع الملفات في مجلدات مناسبة'
];

echo "<div class='space-y-3'>";

foreach ($fixes as $issue => $solution) {
    echo "<div class='bg-white p-4 rounded border'>";
    echo "<h3 class='font-semibold text-green-700 mb-1'>✅ {$issue}</h3>";
    echo "<p class='text-green-600 text-sm'>{$solution}</p>";
    echo "</div>";
}

echo "</div>";
echo "</div>";

// Next steps
echo "<div class='bg-blue-50 border border-blue-200 rounded-lg p-6'>";
echo "<h2 class='text-xl font-bold text-blue-700 mb-4'>🚀 الخطوات التالية</h2>";

echo "<div class='space-y-4'>";

echo "<div class='bg-white p-4 rounded border'>";
echo "<h3 class='font-semibold mb-2'>1. رفع الملفات الجديدة:</h3>";
echo "<ul class='text-sm space-y-1 list-disc list-inside ml-4'>";
echo "<li>إنشاء مجلدات pages/ و includes/</li>";
echo "<li>رفع جميع ملفات pages/</li>";
echo "<li>رفع includes/database.php</li>";
echo "<li>استبدال .htaccess بـ .htaccess-simple</li>";
echo "</ul>";
echo "</div>";

echo "<div class='bg-white p-4 rounded border'>";
echo "<h3 class='font-semibold mb-2'>2. اختبار النظام:</h3>";
echo "<ul class='text-sm space-y-1 list-disc list-inside ml-4'>";
echo "<li>اختبار تسجيل الدخول والخروج</li>";
echo "<li>اختبار تصفح المعارض</li>";
echo "<li>اختبار عملية الحجز الكاملة</li>";
echo "<li>التأكد من عمل الإقرار والتعهد</li>";
echo "</ul>";
echo "</div>";

echo "</div>";

echo "<div class='text-center mt-6'>";
echo "<p class='text-lg font-semibold text-blue-700'>🎉 النظام جاهز للعمل بشكل مثالي!</p>";
echo "</div>";

echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>

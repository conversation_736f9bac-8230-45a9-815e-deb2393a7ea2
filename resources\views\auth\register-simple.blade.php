<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>إنشاء حساب جديد - Season Expo</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .bg-pattern { background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="%23ffffff" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="%23ffffff" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>'); }
    </style>
</head>
<body class="min-h-screen bg-gradient-to-br from-blue-600 via-purple-600 to-blue-800 bg-pattern">
    <div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8">
            <!-- Header -->
            <div class="text-center">
                <div class="mx-auto h-16 w-16 bg-white rounded-full flex items-center justify-center mb-4">
                    <span class="text-2xl">🏢</span>
                </div>
                <h2 class="text-3xl font-bold text-white mb-2">Season Expo</h2>
                <p class="text-blue-100">إنشاء حساب جديد في منصة المعارض الرائدة</p>
            </div>

            <!-- Registration Form -->
            <div class="bg-white rounded-lg shadow-xl p-8">
                <form method="POST" action="/register" class="space-y-6">
                    @csrf

                    <!-- Success/Error Messages -->
                    @if($errors->any())
                        <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                            <div class="flex">
                                <span class="text-red-600 text-xl ml-3">❌</span>
                                <div>
                                    <p class="text-red-800 font-semibold mb-2">يرجى تصحيح الأخطاء التالية:</p>
                                    <ul class="text-red-700 list-disc list-inside">
                                        @foreach($errors->all() as $error)
                                            <li>{{ $error }}</li>
                                        @endforeach
                                    </ul>
                                </div>
                            </div>
                        </div>
                    @endif

                    <!-- Name -->
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 mb-2">الاسم الكامل</label>
                        <input type="text" id="name" name="name" value="{{ old('name') }}" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="أدخل اسمك الكامل">
                    </div>

                    <!-- Email -->
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني</label>
                        <input type="email" id="email" name="email" value="{{ old('email') }}" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="<EMAIL>">
                    </div>

                    <!-- Phone -->
                    <div>
                        <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">
                            رقم الهاتف <span class="text-red-500">*</span>
                        </label>
                        <input type="tel" id="phone" name="phone" value="{{ old('phone') }}" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="+965 1234 5678">
                        @error('phone')
                            <div class="mt-2 text-sm text-red-600">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Company -->
                    <div>
                        <label for="company" class="block text-sm font-medium text-gray-700 mb-2">اسم الشركة (اختياري)</label>
                        <input type="text" id="company" name="company" value="{{ old('company') }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="اسم شركتك أو مؤسستك">
                    </div>

                    <!-- Password -->
                    <div>
                        <label for="password" class="block text-sm font-medium text-gray-700 mb-2">كلمة المرور</label>
                        <input type="password" id="password" name="password" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="أدخل كلمة مرور قوية">
                        <p class="text-xs text-gray-500 mt-1">يجب أن تحتوي على 8 أحرف على الأقل</p>
                    </div>

                    <!-- Confirm Password -->
                    <div>
                        <label for="password_confirmation" class="block text-sm font-medium text-gray-700 mb-2">تأكيد كلمة المرور</label>
                        <input type="password" id="password_confirmation" name="password_confirmation" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="أعد إدخال كلمة المرور">
                    </div>

                    <!-- Account Type -->
                    <div>
                        <label for="role" class="block text-sm font-medium text-gray-700 mb-2">نوع الحساب</label>
                        <select id="role" name="role" required
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="">اختر نوع الحساب</option>
                            <option value="exhibitor" {{ old('role') == 'exhibitor' ? 'selected' : '' }}>عارض (Exhibitor)</option>
                            <option value="organizer" {{ old('role') == 'organizer' ? 'selected' : '' }}>منظم معارض (Organizer)</option>
                        </select>
                    </div>

                    <!-- Terms and Conditions -->
                    <div class="flex items-start">
                        <input type="checkbox" id="terms" name="terms" required
                               class="mt-1 h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                        <label for="terms" class="mr-2 text-sm text-gray-700">
                            أوافق على <a href="#" class="text-blue-600 hover:text-blue-700 underline">الشروط والأحكام</a>
                            و <a href="#" class="text-blue-600 hover:text-blue-700 underline">سياسة الخصوصية</a>
                        </label>
                    </div>

                    <!-- Submit Button -->
                    <button type="submit"
                            class="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-semibold hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors">
                        إنشاء الحساب
                    </button>
                </form>

                <!-- Login Link -->
                <div class="mt-6 text-center">
                    <p class="text-gray-600">
                        لديك حساب بالفعل؟
                        <a href="/login-simple" class="text-blue-600 hover:text-blue-700 font-semibold">تسجيل الدخول</a>
                    </p>
                </div>

                <!-- Forgot Password Link -->
                <div class="mt-4 text-center">
                    <p class="text-gray-600">
                        نسيت كلمة المرور؟
                        <a href="/reset-password-form" class="text-blue-600 hover:text-blue-700 font-semibold">🔑 استعادة كلمة المرور</a>
                    </p>
                </div>
            </div>

            <!-- Footer Links -->
            <div class="text-center">
                <div class="flex justify-center space-x-reverse space-x-6 text-blue-100">
                    <a href="/" class="hover:text-white">الصفحة الرئيسية</a>
                    <a href="/exhibitions" class="hover:text-white">المعارض</a>
                    <a href="/reset-password-form" class="hover:text-white">🔑 نسيت كلمة المرور؟</a>
                    <a href="#" class="hover:text-white">المساعدة</a>
                </div>
                <p class="mt-4 text-blue-200 text-sm">© 2024 Season Expo. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </div>
</body>
</html>

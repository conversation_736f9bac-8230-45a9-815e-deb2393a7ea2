<?php
// Logo Debug Tool
// This file helps diagnose logo display issues

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='utf-8'>";
echo "<title>تشخيص مشكلة الشعار - Season Expo Kuwait</title>";
echo "<script src='https://cdn.tailwindcss.com'></script>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }";
echo ".test-box { border: 2px dashed #ccc; padding: 20px; margin: 10px 0; }";
echo "</style>";
echo "</head>";
echo "<body class='bg-gray-50 p-8'>";

echo "<div class='max-w-4xl mx-auto'>";
echo "<h1 class='text-3xl font-bold mb-8' style='color: #2C3E50;'>🔍 تشخيص مشكلة الشعار</h1>";

// Check if images directory exists
$imagesDir = __DIR__ . '/images';
$logoFile = __DIR__ . '/images/logo.png';
$faviconFile = __DIR__ . '/favicon.ico';

echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>📁 فحص الملفات والمجلدات</h2>";

// Check images directory
echo "<div class='mb-4'>";
echo "<h3 class='font-semibold mb-2'>1. مجلد الصور:</h3>";
if (is_dir($imagesDir)) {
    echo "<div class='text-green-600'>✅ مجلد /images موجود</div>";
    
    // List files in images directory
    $files = scandir($imagesDir);
    $imageFiles = array_filter($files, function($file) {
        return !in_array($file, ['.', '..']) && preg_match('/\.(png|jpg|jpeg|gif|ico)$/i', $file);
    });
    
    if (count($imageFiles) > 0) {
        echo "<div class='mt-2'>";
        echo "<strong>الملفات الموجودة:</strong>";
        echo "<ul class='list-disc list-inside mt-1 text-sm'>";
        foreach ($imageFiles as $file) {
            $filePath = $imagesDir . '/' . $file;
            $fileSize = filesize($filePath);
            echo "<li>{$file} (" . round($fileSize/1024, 2) . " KB)</li>";
        }
        echo "</ul>";
        echo "</div>";
    } else {
        echo "<div class='text-red-600'>❌ لا توجد ملفات صور في المجلد</div>";
    }
} else {
    echo "<div class='text-red-600'>❌ مجلد /images غير موجود</div>";
    echo "<div class='text-sm text-gray-600 mt-1'>يجب إنشاء مجلد images في public_html</div>";
}
echo "</div>";

// Check logo file specifically
echo "<div class='mb-4'>";
echo "<h3 class='font-semibold mb-2'>2. ملف الشعار الرئيسي:</h3>";
if (file_exists($logoFile)) {
    $logoSize = filesize($logoFile);
    echo "<div class='text-green-600'>✅ ملف logo.png موجود (" . round($logoSize/1024, 2) . " KB)</div>";
} else {
    echo "<div class='text-red-600'>❌ ملف logo.png غير موجود</div>";
    echo "<div class='text-sm text-gray-600 mt-1'>المسار المتوقع: /images/logo.png</div>";
}
echo "</div>";

// Check favicon
echo "<div class='mb-4'>";
echo "<h3 class='font-semibold mb-2'>3. أيقونة المتصفح:</h3>";
if (file_exists($faviconFile)) {
    $faviconSize = filesize($faviconFile);
    echo "<div class='text-green-600'>✅ ملف favicon.ico موجود (" . round($faviconSize/1024, 2) . " KB)</div>";
} else {
    echo "<div class='text-red-600'>❌ ملف favicon.ico غير موجود</div>";
    echo "<div class='text-sm text-gray-600 mt-1'>المسار المتوقع: /favicon.ico</div>";
}
echo "</div>";

echo "</div>";

// Test logo display
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>🖼️ اختبار عرض الشعار</h2>";

echo "<div class='test-box'>";
echo "<h3 class='font-semibold mb-2'>اختبار 1: عرض مباشر للشعار</h3>";
echo "<img src='/images/logo.png' alt='Season Expo Kuwait' style='max-height: 60px; border: 1px solid #ddd;' onerror='this.style.display=\"none\"; this.nextElementSibling.style.display=\"block\";'>";
echo "<div style='display: none; color: red; font-weight: bold;'>❌ فشل في تحميل الشعار</div>";
echo "</div>";

echo "<div class='test-box'>";
echo "<h3 class='font-semibold mb-2'>اختبار 2: مسارات مختلفة</h3>";
$testPaths = [
    '/images/logo.png',
    './images/logo.png',
    'images/logo.png',
    '/logo.png'
];

foreach ($testPaths as $path) {
    echo "<div class='mb-2'>";
    echo "<strong>المسار: {$path}</strong><br>";
    echo "<img src='{$path}' alt='Test' style='max-height: 40px; border: 1px solid #ddd; margin: 5px 0;' onerror='this.style.display=\"none\"; this.nextElementSibling.style.display=\"inline\";'>";
    echo "<span style='display: none; color: red;'>❌ فشل</span>";
    echo "</div>";
}
echo "</div>";

echo "</div>";

// Browser console check
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>🔧 فحص المتصفح</h2>";
echo "<div class='bg-yellow-50 p-4 rounded-lg'>";
echo "<h3 class='font-semibold mb-2'>خطوات الفحص في المتصفح:</h3>";
echo "<ol class='list-decimal list-inside space-y-1 text-sm'>";
echo "<li>اضغط F12 لفتح أدوات المطور</li>";
echo "<li>اذهب إلى تبويب 'Console'</li>";
echo "<li>ابحث عن رسائل خطأ تتعلق بالصور</li>";
echo "<li>اذهب إلى تبويب 'Network' وأعد تحميل الصفحة</li>";
echo "<li>ابحث عن طلبات فاشلة (باللون الأحمر)</li>";
echo "</ol>";
echo "</div>";
echo "</div>";

// Quick fixes
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>🛠️ حلول سريعة</h2>";

echo "<div class='grid grid-cols-1 md:grid-cols-2 gap-4'>";

echo "<div class='border border-gray-200 rounded-lg p-4'>";
echo "<h3 class='font-semibold mb-2' style='color: #2C3E50;'>المشكلة: مجلد images غير موجود</h3>";
echo "<div class='text-sm space-y-1'>";
echo "<div><strong>الحل:</strong></div>";
echo "<div>1. في File Manager، اذهب إلى public_html</div>";
echo "<div>2. اضغط 'New Folder'</div>";
echo "<div>3. اكتب 'images'</div>";
echo "<div>4. ارفع ملفات الشعار إلى هذا المجلد</div>";
echo "</div>";
echo "</div>";

echo "<div class='border border-gray-200 rounded-lg p-4'>";
echo "<h3 class='font-semibold mb-2' style='color: #2C3E50;'>المشكلة: ملف الشعار غير موجود</h3>";
echo "<div class='text-sm space-y-1'>";
echo "<div><strong>الحل:</strong></div>";
echo "<div>1. تأكد من أن اسم الملف 'logo.png' بالضبط</div>";
echo "<div>2. تأكد من أن الملف في مجلد images/</div>";
echo "<div>3. تأكد من أن الملف ليس تالف</div>";
echo "<div>4. جرب رفع الملف مرة أخرى</div>";
echo "</div>";
echo "</div>";

echo "<div class='border border-gray-200 rounded-lg p-4'>";
echo "<h3 class='font-semibold mb-2' style='color: #2C3E50;'>المشكلة: صلاحيات الملفات</h3>";
echo "<div class='text-sm space-y-1'>";
echo "<div><strong>الحل:</strong></div>";
echo "<div>1. في File Manager، انقر بالزر الأيمن على الملف</div>";
echo "<div>2. اختر 'Permissions'</div>";
echo "<div>3. تأكد من أن الصلاحيات 644 أو 755</div>";
echo "</div>";
echo "</div>";

echo "<div class='border border-gray-200 rounded-lg p-4'>";
echo "<h3 class='font-semibold mb-2' style='color: #2C3E50;'>المشكلة: كاش المتصفح</h3>";
echo "<div class='text-sm space-y-1'>";
echo "<div><strong>الحل:</strong></div>";
echo "<div>1. اضغط Ctrl+F5 لإعادة تحميل قوية</div>";
echo "<div>2. أو امسح كاش المتصفح</div>";
echo "<div>3. أو جرب متصفح آخر</div>";
echo "</div>";
echo "</div>";

echo "</div>";
echo "</div>";

// Manual upload guide
echo "<div class='bg-white rounded-lg shadow-lg p-6'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>📤 دليل الرفع اليدوي</h2>";
echo "<div class='space-y-4'>";

echo "<div class='bg-blue-50 p-4 rounded-lg'>";
echo "<h3 class='font-semibold mb-2'>الخطوة 1: تحضير الملف</h3>";
echo "<ul class='text-sm space-y-1'>";
echo "<li>• احفظ الشعار باسم 'logo.png' بالضبط</li>";
echo "<li>• تأكد من أن الخلفية شفافة</li>";
echo "<li>• الحجم المناسب: 300-500 بكسل عرض</li>";
echo "<li>• حجم الملف: أقل من 100 KB</li>";
echo "</ul>";
echo "</div>";

echo "<div class='bg-green-50 p-4 rounded-lg'>";
echo "<h3 class='font-semibold mb-2'>الخطوة 2: الرفع</h3>";
echo "<ul class='text-sm space-y-1'>";
echo "<li>• اذهب إلى File Manager في Hostinger</li>";
echo "<li>• انتقل إلى public_html</li>";
echo "<li>• أنشئ مجلد 'images' إذا لم يكن موجود</li>";
echo "<li>• ادخل إلى مجلد images</li>";
echo "<li>• ارفع ملف logo.png</li>";
echo "</ul>";
echo "</div>";

echo "<div class='bg-yellow-50 p-4 rounded-lg'>";
echo "<h3 class='font-semibold mb-2'>الخطوة 3: التحقق</h3>";
echo "<ul class='text-sm space-y-1'>";
echo "<li>• اذهب إلى: <code>yourdomain.com/images/logo.png</code></li>";
echo "<li>• يجب أن يظهر الشعار مباشرة</li>";
echo "<li>• إذا لم يظهر، تحقق من المسار والاسم</li>";
echo "</ul>";
echo "</div>";

echo "</div>";
echo "</div>";

echo "</div>";

// JavaScript for additional checks
echo "<script>";
echo "
// Check if images load
document.addEventListener('DOMContentLoaded', function() {
    const images = document.querySelectorAll('img[src*=\"logo\"]');
    images.forEach(function(img) {
        img.addEventListener('load', function() {
            console.log('✅ تم تحميل الشعار بنجاح:', this.src);
        });
        img.addEventListener('error', function() {
            console.error('❌ فشل في تحميل الشعار:', this.src);
        });
    });
});
";
echo "</script>";

echo "</body>";
echo "</html>";
?>

<?php
echo "<h1>Install Production index.php</h1>";

$indexPath = __DIR__ . '/index.php';

// Production version without error reporting
$productionIndexContent = '<?php

use Illuminate\Contracts\Http\Kernel;
use Illuminate\Http\Request;

define("LARAVEL_START", microtime(true));

/*
|--------------------------------------------------------------------------
| Check If The Application Is Under Maintenance
|--------------------------------------------------------------------------
*/

if (file_exists($maintenance = __DIR__."/../storage/framework/maintenance.php")) {
    require $maintenance;
}

/*
|--------------------------------------------------------------------------
| Register The Auto Loader
|--------------------------------------------------------------------------
*/

require __DIR__."/../vendor/autoload.php";

/*
|--------------------------------------------------------------------------
| Run The Application
|--------------------------------------------------------------------------
*/

$app = require_once __DIR__."/../bootstrap/app.php";

$kernel = $app->make(Kernel::class);

$response = $kernel->handle(
    $request = Request::capture()
)->send();

$kernel->terminate($request, $response);';

echo "<h2>Production index.php Features:</h2>";
echo "<ul>";
echo "<li>✅ No error reporting (secure for production)</li>";
echo "<li>✅ Clean Laravel structure</li>";
echo "<li>✅ Proper error handling</li>";
echo "<li>✅ Optimized for performance</li>";
echo "</ul>";

echo "<h2>Install Production Version:</h2>";

if (isset($_GET['install']) && $_GET['install'] === 'yes') {
    // Backup current version
    $backupPath = $indexPath . '.debug-backup-' . date('Y-m-d-H-i-s');
    if (file_exists($indexPath)) {
        copy($indexPath, $backupPath);
        echo "✅ Debug version backed up as: " . basename($backupPath) . "<br>";
    }
    
    // Install production version
    if (file_put_contents($indexPath, $productionIndexContent) !== false) {
        echo "✅ Production index.php installed successfully!<br>";
        
        echo "<div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3 style='color: #155724;'>🎉 Production Version Active!</h3>";
        echo "<p>Your application is now running with the production-ready index.php</p>";
        echo "<p><a href='/'>Test Your Application</a></p>";
        echo "</div>";
    } else {
        echo "❌ Failed to install production version<br>";
    }
} else {
    echo "<div style='background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; border-radius: 5px;'>";
    echo "<h3>⚠️ Only install this AFTER your application is working</h3>";
    echo "<p>First test with the debug version, then switch to production.</p>";
    echo "<p><a href='?install=yes' style='background: #ffc107; color: black; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>Install Production Version</a></p>";
    echo "</div>";
}

echo "<h2>Current Status:</h2>";
if (file_exists($indexPath)) {
    $content = file_get_contents($indexPath);
    if (strpos($content, 'display_errors') !== false) {
        echo "🔧 Debug version is active (shows errors)<br>";
    } else {
        echo "🚀 Production version is active (no error display)<br>";
    }
}
?>

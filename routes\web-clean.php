<?php

use Illuminate\Support\Facades\Route;

// Redirect original dashboard to simple version
Route::get('/dashboard', function () {
    return redirect('/dashboard-simple');
})->middleware('auth');

// Simple dashboard using blade template
Route::get('/dashboard-simple', function () {
    $user = auth()->user();

    if (!$user) {
        return redirect('/login');
    }

    return view('dashboard-simple', compact('user'));
})->middleware('auth');

// Function to save signature image
function saveSignatureImage($base64Image, $userId, $signatureId) {
    try {
        // Remove data:image/png;base64, prefix
        $imageData = preg_replace('#^data:image/\w+;base64,#i', '', $base64Image);
        $imageData = base64_decode($imageData);

        if ($imageData === false) {
            return null;
        }

        // Create signatures directory if it doesn't exist
        $directory = storage_path('app/public/signatures');
        if (!file_exists($directory)) {
            mkdir($directory, 0755, true);
        }

        // Save image file
        $fileName = 'signature_' . $userId . '_' . $signatureId . '.png';
        $filePath = $directory . '/' . $fileName;

        if (file_put_contents($filePath, $imageData)) {
            return 'signatures/' . $fileName;
        }

        return null;
    } catch (Exception $e) {
        return null;
    }
}

// Digital Signatures Routes
Route::get('/signatures', function () {
    if (!auth()->check()) {
        return redirect('/login')->with('error', 'يجب تسجيل الدخول أولاً');
    }

    $user = auth()->user();
    $signatures = \App\Models\DigitalSignature::where('user_id', $user->id)
        ->orderBy('created_at', 'desc')
        ->get();

    return view('signatures.index', compact('user', 'signatures'));
})->middleware('auth');

Route::get('/signatures/verify/{token}', function ($token) {
    try {
        $signature = \App\Models\DigitalSignature::where('verification_token', $token)->first();

        if (!$signature) {
            return view('signatures.verify-result', [
                'success' => false,
                'message' => 'رمز التحقق غير صحيح أو منتهي الصلاحية'
            ]);
        }

        // Update verification status
        $signature->update([
            'is_verified' => true,
            'verified_at' => now()
        ]);

        return view('signatures.verify-result', [
            'success' => true,
            'signature' => $signature,
            'message' => 'تم التحقق من التوقيع بنجاح'
        ]);

    } catch (Exception $e) {
        return view('signatures.verify-result', [
            'success' => false,
            'message' => 'حدث خطأ أثناء التحقق من التوقيع'
        ]);
    }
})->name('signatures.verify');

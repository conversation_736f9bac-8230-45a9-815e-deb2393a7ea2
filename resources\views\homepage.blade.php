<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Season Expo Kuwait - منصة المعارض الرائدة في الكويت</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">

    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .hero-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
    </style>
</head>
<body class="min-h-screen bg-gray-50">
    <!-- Fixed Navigation -->
    <nav class="fixed top-0 left-0 right-0 bg-white shadow-sm z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="/">
                        <img src="/images/logo.png" alt="Season Expo Kuwait" style="height: 40px; width: auto;" class="hover:opacity-80 transition-opacity">
                    </a>
                </div>
                <div class="flex items-center space-x-reverse space-x-4">
                    <!-- Language Switcher -->
                    <div class="relative inline-block text-left">
                        <div class="group">
                            <button type="button" class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" onclick="toggleLanguageMenu()">
                                <span class="ml-2">🇰🇼</span>
                                العربية
                                <svg class="mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                </svg>
                            </button>
                            <div id="languageMenu" class="absolute left-0 z-10 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 opacity-0 invisible transition-all duration-200">
                                <div class="py-1" role="menu">
                                    <a href="/language/ar" class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900" role="menuitem">
                                        <span class="ml-3">🇰🇼</span>
                                        العربية
                                    </a>
                                    <a href="/language/en" class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900" role="menuitem">
                                        <span class="ml-3">🇺🇸</span>
                                        English
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    @auth
                        <a href="/dashboard" class="text-gray-600 hover:text-gray-900">لوحة التحكم</a>
                        <form method="POST" action="/logout" class="inline">
                            @csrf
                            <button type="submit" class="text-gray-600 hover:text-gray-900">تسجيل الخروج</button>
                        </form>
                    @else
                        <a href="/login-simple" class="text-gray-600 hover:text-gray-900">تسجيل الدخول</a>
                        <a href="/register" class="text-white px-4 py-2 rounded-lg hover:opacity-80 transition-opacity" style="background: #2C3E50;">إنشاء حساب</a>
                    @endauth
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-bg pt-16 pb-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
            <div class="text-center text-white">
                <h1 class="text-4xl md:text-6xl font-bold mb-6">
                    مرحباً بك في Season Expo
                </h1>
                <p class="text-xl md:text-2xl mb-8 text-blue-100">
                    منصة المعارض الرائدة في الكويت - احجز جناحك الآن
                </p>
                <div class="flex flex-col sm:flex-row justify-center gap-4">
                    <a href="/exhibitions" class="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                        🏢 تصفح المعارض
                    </a>
                    @auth
                        <a href="/dashboard" class="bg-blue-800 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-900 transition-colors">
                            📊 لوحة التحكم
                        </a>
                    @else
                        <a href="/login-simple" class="bg-blue-800 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-900 transition-colors">
                            🔑 تسجيل الدخول
                        </a>
                    @endauth
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl font-bold text-gray-900 mb-4">لماذا Season Expo؟</h2>
                <p class="text-xl text-gray-600">منصة شاملة لإدارة المعارض وحجز الأجنحة</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="text-center p-6">
                    <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-2xl">🏢</span>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">حجز الأجنحة</h3>
                    <p class="text-gray-600">احجز جناحك بسهولة واختر الموقع والحجم المناسب لعملك</p>
                </div>

                <div class="text-center p-6">
                    <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-2xl">💳</span>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">دفع آمن</h3>
                    <p class="text-gray-600">نظام دفع آمن ومتكامل مع بوابة MyFatoorah الموثوقة</p>
                </div>

                <div class="text-center p-6">
                    <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-2xl">📊</span>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">إدارة شاملة</h3>
                    <p class="text-gray-600">لوحة تحكم متقدمة لإدارة حجوزاتك ومتابعة معارضك</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Current Exhibitions -->
    <section class="py-20 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl font-bold text-gray-900 mb-4">المعارض الحالية</h2>
                <p class="text-xl text-gray-600">اكتشف أحدث المعارض المتاحة للحجز</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                @php
                    $exhibitions = \App\Models\Exhibition::where('status', 'published')
                        ->where('start_date', '>', now())
                        ->take(3)
                        ->get();
                @endphp

                @forelse($exhibitions as $exhibition)
                    <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
                        <div class="h-48 bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
                            <span class="text-white text-4xl">🏢</span>
                        </div>
                        <div class="p-6">
                            <h3 class="text-xl font-semibold text-gray-900 mb-2">{{ $exhibition->title }}</h3>
                            <p class="text-gray-600 mb-4">{{ Str::limit($exhibition->description, 100) }}</p>
                            <div class="flex justify-between items-center text-sm text-gray-500 mb-4">
                                <span>📅 {{ $exhibition->start_date->format('d/m/Y') }}</span>
                                <span>📍 {{ $exhibition->city }}</span>
                            </div>
                            <div class="flex space-x-reverse space-x-2">
                                <a href="/exhibitions/{{ $exhibition->slug }}"
                                   class="flex-1 bg-blue-600 text-white text-center py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors">
                                    عرض التفاصيل
                                </a>
                                <a href="/exhibitions/{{ $exhibition->slug }}/booths"
                                   class="flex-1 bg-green-600 text-white text-center py-2 px-4 rounded-lg hover:bg-green-700 transition-colors">
                                    حجز جناح
                                </a>
                            </div>
                        </div>
                    </div>
                @empty
                    <div class="col-span-full text-center py-12">
                        <div class="text-gray-400 text-6xl mb-4">🏢</div>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">لا توجد معارض متاحة حالياً</h3>
                        <p class="text-gray-500">سيتم إضافة معارض جديدة قريباً</p>
                    </div>
                @endforelse
            </div>

            <div class="text-center mt-12">
                <a href="/exhibitions" class="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                    عرض جميع المعارض
                </a>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="py-20 bg-blue-600">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 class="text-3xl font-bold text-white mb-4">ابدأ رحلتك مع Season Expo</h2>
            <p class="text-xl text-blue-100 mb-8">انضم إلى آلاف العارضين الذين يثقون بمنصتنا</p>
            @guest
                <a href="/login-simple" class="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                    ابدأ الآن - مجاناً
                </a>
            @else
                <a href="/dashboard" class="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                    انتقل إلى لوحة التحكم
                </a>
            @endguest
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <h3 class="text-xl font-bold mb-4">Season Expo</h3>
                    <p class="text-gray-400">منصة المعارض الرائدة في الكويت</p>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">روابط سريعة</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="/exhibitions" class="hover:text-white">المعارض</a></li>
                        <li><a href="/dashboard" class="hover:text-white">لوحة التحكم</a></li>
                        <li><a href="/login-simple" class="hover:text-white">تسجيل الدخول</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">الدعم</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li>📧 <EMAIL></li>
                        <li>📱 +965 1234 5678</li>
                        <li>🕒 الأحد - الخميس: 9ص - 6م</li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">تابعنا</h4>
                    <div class="flex space-x-reverse space-x-4">
                        <a href="#" class="text-gray-400 hover:text-white">📘</a>
                        <a href="#" class="text-gray-400 hover:text-white">📷</a>
                        <a href="#" class="text-gray-400 hover:text-white">🐦</a>
                    </div>
                </div>
            </div>
            <div class="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
                <p>© 2024 Season Expo. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <!-- Language Switcher JavaScript -->
    <script>
    function toggleLanguageMenu() {
        const menu = document.getElementById('languageMenu');
        const isVisible = menu.classList.contains('opacity-100');

        if (isVisible) {
            menu.classList.remove('opacity-100', 'visible');
            menu.classList.add('opacity-0', 'invisible');
        } else {
            menu.classList.remove('opacity-0', 'invisible');
            menu.classList.add('opacity-100', 'visible');
        }
    }

    // Close menu when clicking outside
    document.addEventListener('click', function(event) {
        const menu = document.getElementById('languageMenu');
        const button = event.target.closest('button');

        if (!button || button.getAttribute('onclick') !== 'toggleLanguageMenu()') {
            menu.classList.remove('opacity-100', 'visible');
            menu.classList.add('opacity-0', 'invisible');
        }
    });

    // Handle language switching
    function switchLanguage(locale) {
        // Store language preference
        localStorage.setItem('preferred_language', locale);

        // Redirect to language route
        window.location.href = '/language/' + locale;
    }

    // Load saved language preference
    document.addEventListener('DOMContentLoaded', function() {
        const savedLang = localStorage.getItem('preferred_language');
        if (savedLang) {
            const button = document.querySelector('button[onclick="toggleLanguageMenu()"]');
            if (button) {
                if (savedLang === 'en') {
                    button.innerHTML = '<span class="ml-2">🇺🇸</span>English<svg class="mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" /></svg>';
                }
            }
        }
    });
    </script>
</body>
</html>

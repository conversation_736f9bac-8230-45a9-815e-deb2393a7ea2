<?php
echo "<h1>Replace index.php with Working Version</h1>";

$indexPath = __DIR__ . '/index.php';

// Working index.php based on our successful test
$workingIndexContent = '<?php
ini_set("display_errors", 1);
ini_set("display_startup_errors", 1);
error_reporting(E_ALL);

use Illuminate\Contracts\Http\Kernel;
use Illuminate\Http\Request;

define("LARAVEL_START", microtime(true));

try {
    // Check maintenance mode
    if (file_exists($maintenance = __DIR__."/../storage/framework/maintenance.php")) {
        require $maintenance;
    }
    
    // Load autoloader
    require __DIR__."/../vendor/autoload.php";
    
    // Load Laravel app
    $app = require_once __DIR__."/../bootstrap/app.php";
    
    // Create HTTP kernel
    $kernel = $app->make(Kernel::class);
    
    // Handle request
    $response = $kernel->handle(
        $request = Request::capture()
    )->send();
    
    // Terminate
    $kernel->terminate($request, $response);
    
} catch (Throwable $e) {
    // Emergency error display
    echo "<h1>Application Error</h1>";
    echo "<div style=\"background: #f8d7da; padding: 20px; margin: 20px; border-radius: 5px;\">";
    echo "<h2>Error Details:</h2>";
    echo "<p><strong>Message:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>File:</strong> " . htmlspecialchars($e->getFile()) . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    echo "<details><summary>Stack Trace</summary>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
    echo "</details>";
    echo "</div>";
    http_response_code(500);
}
?>';

echo "<h2>Current index.php Status:</h2>";

if (file_exists($indexPath)) {
    echo "✅ index.php exists<br>";
    echo "Size: " . filesize($indexPath) . " bytes<br>";
    echo "Modified: " . date('Y-m-d H:i:s', filemtime($indexPath)) . "<br>";
    
    // Backup current version
    $backupPath = $indexPath . '.backup-' . date('Y-m-d-H-i-s');
    if (copy($indexPath, $backupPath)) {
        echo "✅ Backup created: " . basename($backupPath) . "<br>";
    }
} else {
    echo "❌ index.php not found<br>";
}

echo "<h2>Installing Working index.php:</h2>";

if (file_put_contents($indexPath, $workingIndexContent) !== false) {
    echo "✅ New index.php installed successfully!<br>";
    echo "Size: " . filesize($indexPath) . " bytes<br>";
    
    echo "<br><div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px;'>";
    echo "<h3 style='color: #155724;'>🎉 index.php Replaced!</h3>";
    echo "<p>The index.php has been replaced with a working version that includes error reporting.</p>";
    echo "<p><strong><a href='/' style='font-size: 18px; background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>🚀 TEST YOUR SEASON EXPO APPLICATION</a></strong></p>";
    echo "</div>";
    
} else {
    echo "❌ Failed to write new index.php<br>";
    
    echo "<h2>Manual Replacement:</h2>";
    echo "<div style='background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; border-radius: 5px;'>";
    echo "<p>Please manually replace your index.php content with:</p>";
    echo "<textarea style='width: 100%; height: 200px; font-family: monospace;'>";
    echo htmlspecialchars($workingIndexContent);
    echo "</textarea>";
    echo "</div>";
}

echo "<h2>What This New Version Does:</h2>";
echo "<ul>";
echo "<li>✅ Uses the same Laravel loading pattern that works in our test</li>";
echo "<li>✅ Includes error reporting for debugging</li>";
echo "<li>✅ Has proper error handling</li>";
echo "<li>✅ Shows detailed errors if something goes wrong</li>";
echo "</ul>";

echo "<h2>After Testing:</h2>";
echo "<p>Once your application works, you can disable error reporting by removing these lines:</p>";
echo "<code>ini_set(\"display_errors\", 1);<br>ini_set(\"display_startup_errors\", 1);<br>error_reporting(E_ALL);</code>";
?>

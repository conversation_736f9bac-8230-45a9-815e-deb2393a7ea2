<?php
// Test Slider Fixes

echo "<h1>🔧 اختبار إصلاحات السلايد</h1>";

echo "<h2>1. اختبار قاعدة البيانات:</h2>";

try {
    $pdo = new PDO('sqlite:' . __DIR__ . '/database/database.sqlite');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $stmt = $pdo->query("SELECT * FROM slider_images WHERE is_active = 1 ORDER BY sort_order ASC");
    $slides = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p style='color: green;'>✅ عدد السلايدات النشطة: <strong>" . count($slides) . "</strong></p>";
    
    if (count($slides) > 1) {
        echo "<p style='color: green;'>✅ يجب أن يعمل السلايد بشكل صحيح (أكثر من سلايد واحد)</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ سلايد واحد فقط - لن يظهر التنقل</p>";
    }
    
    // Show slides preview
    echo "<h3>معاينة السلايدات:</h3>";
    echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0;'>";
    
    foreach ($slides as $index => $slide) {
        echo "<div style='border: 1px solid #ddd; border-radius: 8px; padding: 15px; background: white; box-shadow: 0 2px 4px rgba(0,0,0,0.1);'>";
        echo "<h4 style='margin: 0 0 10px 0; color: #333;'>سلايد " . ($index + 1) . "</h4>";
        echo "<p><strong>العنوان:</strong> " . htmlspecialchars($slide['title']) . "</p>";
        echo "<p><strong>الترتيب:</strong> " . $slide['sort_order'] . "</p>";
        echo "<div style='width: 100%; height: 80px; background: " . $slide['background_color'] . "; border-radius: 4px; margin: 10px 0; display: flex; align-items: center; justify-content: center; color: white; font-size: 12px;'>";
        echo "معاينة الخلفية";
        echo "</div>";
        echo "<img src='{$slide['image_url']}' style='width: 100%; height: 80px; object-fit: cover; border-radius: 4px; margin: 5px 0;' onerror='this.style.display=\"none\";'>";
        echo "</div>";
    }
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
}

echo "<h2>2. اختبار الصفحات:</h2>";

$testPages = [
    '/' => 'الصفحة الرئيسية - اختبار السلايد المحدث',
    '/admin/slider' => 'لوحة تحكم الأدمن (Laravel)',
    '/admin-slider-management.php' => 'لوحة تحكم مباشرة (PHP)',
    '/homepage-fixed.php' => 'الصفحة المصححة',
    '/debug-slider.php' => 'تشخيص مشاكل السلايد'
];

echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; margin: 20px 0;'>";
foreach ($testPages as $url => $description) {
    $color = strpos($url, 'admin') !== false ? '#dc3545' : '#007bff';
    echo "<a href='$url' target='_blank' style='background: $color; color: white; padding: 15px; text-decoration: none; border-radius: 8px; text-align: center; display: block; transition: transform 0.2s;' onmouseover='this.style.transform=\"translateY(-2px)\"' onmouseout='this.style.transform=\"translateY(0)\"'>";
    echo "<strong>" . (basename($url) ?: 'الرئيسية') . "</strong><br>";
    echo "<small>$description</small>";
    echo "</a>";
}
echo "</div>";

echo "<h2>3. الإصلاحات المطبقة:</h2>";

echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
echo "<h3>✅ إصلاح مشكلة السلايد:</h3>";
echo "<ul>";
echo "<li>✅ تحديث CSS للسلايد مع كلاسات محددة</li>";
echo "<li>✅ إصلاح HTML structure للسلايدات</li>";
echo "<li>✅ تحسين JavaScript للتنقل</li>";
echo "<li>✅ إضافة flex-shrink: 0 لمنع انكماش السلايدات</li>";
echo "</ul>";
echo "</div>";

echo "<div style='background: #cce5ff; border: 1px solid #99ccff; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
echo "<h3>✅ إصلاح مشكلة التعديل:</h3>";
echo "<ul>";
echo "<li>✅ إنشاء admin-slider-handler.php لمعالجة العمليات</li>";
echo "<li>✅ استخدام AJAX بدلاً من إعادة تحميل الصفحة</li>";
echo "<li>✅ تحسين معالجة الأخطاء</li>";
echo "<li>✅ إضافة تأكيد للعمليات الحساسة</li>";
echo "</ul>";
echo "</div>";

echo "<h2>4. اختبار JavaScript للسلايد:</h2>";

echo "<div id='js-test' style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
echo "<p>جاري اختبار JavaScript...</p>";
echo "</div>";

echo "<script>";
echo "document.addEventListener('DOMContentLoaded', function() {";
echo "    const testDiv = document.getElementById('js-test');";
echo "    const totalSlides = " . count($slides ?? []) . ";";
echo "    ";
echo "    testDiv.innerHTML = '<h4>نتائج اختبار JavaScript:</h4>';";
echo "    testDiv.innerHTML += '<p>عدد السلايدات: ' + totalSlides + '</p>';";
echo "    ";
echo "    if (totalSlides > 1) {";
echo "        testDiv.innerHTML += '<p style=\"color: green;\">✅ السلايد يجب أن يعمل بشكل صحيح</p>';";
echo "        testDiv.innerHTML += '<p>🔄 التنقل التلقائي كل 5 ثوان</p>';";
echo "        testDiv.innerHTML += '<p>⬅️➡️ أزرار التنقل اليدوي</p>';";
echo "        testDiv.innerHTML += '<p>⚪ مؤشرات السلايد</p>';";
echo "    } else {";
echo "        testDiv.innerHTML += '<p style=\"color: orange;\">⚠️ سلايد واحد فقط - لن تظهر عناصر التحكم</p>';";
echo "    }";
echo "    ";
echo "    // Test slider functions simulation";
echo "    testDiv.innerHTML += '<div style=\"margin: 15px 0; padding: 15px; background: white; border-radius: 5px; border: 1px solid #ddd;\">';";
echo "    testDiv.innerHTML += '<h5>محاكاة وظائف السلايد:</h5>';";
echo "    testDiv.innerHTML += '<button onclick=\"simulateSlider()\" style=\"background: #007bff; color: white; padding: 8px 15px; border: none; border-radius: 4px; margin: 5px;\">▶️ تشغيل</button>';";
echo "    testDiv.innerHTML += '<button onclick=\"simulateNext()\" style=\"background: #28a745; color: white; padding: 8px 15px; border: none; border-radius: 4px; margin: 5px;\">⏭️ التالي</button>';";
echo "    testDiv.innerHTML += '<button onclick=\"simulatePrev()\" style=\"background: #ffc107; color: black; padding: 8px 15px; border: none; border-radius: 4px; margin: 5px;\">⏮️ السابق</button>';";
echo "    testDiv.innerHTML += '<div id=\"slider-status\" style=\"margin-top: 10px; padding: 10px; background: #f0f0f0; border-radius: 3px;\">جاهز للاختبار</div>';";
echo "    testDiv.innerHTML += '</div>';";
echo "});";
echo "";
echo "let currentSlide = 0;";
echo "const totalSlides = " . count($slides ?? []) . ";";
echo "";
echo "function simulateSlider() {";
echo "    document.getElementById('slider-status').innerHTML = '🔄 تشغيل السلايد... السلايد الحالي: ' + (currentSlide + 1) + ' من ' + totalSlides;";
echo "}";
echo "";
echo "function simulateNext() {";
echo "    currentSlide = (currentSlide + 1) % totalSlides;";
echo "    document.getElementById('slider-status').innerHTML = '⏭️ الانتقال للسلايد التالي: ' + (currentSlide + 1) + ' من ' + totalSlides;";
echo "}";
echo "";
echo "function simulatePrev() {";
echo "    currentSlide = (currentSlide - 1 + totalSlides) % totalSlides;";
echo "    document.getElementById('slider-status').innerHTML = '⏮️ الانتقال للسلايد السابق: ' + (currentSlide + 1) + ' من ' + totalSlides;";
echo "}";
echo "</script>";

echo "<h2>5. تعليمات الاختبار:</h2>";

echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
echo "<h3>خطوات اختبار السلايد:</h3>";
echo "<ol>";
echo "<li><strong>افتح الصفحة الرئيسية:</strong> تأكد من ظهور جميع السلايدات</li>";
echo "<li><strong>اختبر التنقل التلقائي:</strong> انتظر 5 ثوان لرؤية التغيير</li>";
echo "<li><strong>اختبر الأزرار:</strong> اضغط على أسهم اليسار واليمين</li>";
echo "<li><strong>اختبر المؤشرات:</strong> اضغط على النقاط في الأسفل</li>";
echo "<li><strong>اختبر التجاوب:</strong> غير حجم النافذة</li>";
echo "</ol>";
echo "</div>";

echo "<div style='background: #e7f3ff; border: 1px solid #99ccff; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
echo "<h3>خطوات اختبار التعديل:</h3>";
echo "<ol>";
echo "<li><strong>اذهب لوحة التحكم:</strong> /admin-slider-management.php</li>";
echo "<li><strong>جرب إضافة سلايد:</strong> اضغط 'إضافة سلايد جديد'</li>";
echo "<li><strong>جرب التعديل:</strong> اضغط 'تعديل' على سلايد موجود</li>";
echo "<li><strong>جرب الحذف:</strong> اضغط 'حذف' (مع الحذر)</li>";
echo "<li><strong>جرب الإخفاء:</strong> اضغط 'إظهار/إخفاء'</li>";
echo "</ol>";
echo "</div>";

echo "<h2>6. النتيجة المتوقعة:</h2>";

echo "<div style='background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 12px; text-align: center; margin: 20px 0;'>";
echo "<h3 style='margin: 0 0 20px 0; font-size: 2em;'>🎯 النتائج المتوقعة</h3>";
echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0;'>";
echo "<div style='background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px;'>";
echo "<h4>🖼️ السلايد</h4>";
echo "<p>عرض جميع السلايدات</p>";
echo "<p>تنقل سلس</p>";
echo "<p>تشغيل تلقائي</p>";
echo "</div>";
echo "<div style='background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px;'>";
echo "<h4>⚙️ التعديل</h4>";
echo "<p>إضافة سلايدات جديدة</p>";
echo "<p>تعديل المحتوى</p>";
echo "<p>حذف وإخفاء</p>";
echo "</div>";
echo "<div style='background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px;'>";
echo "<h4>📱 التجاوب</h4>";
echo "<p>يعمل على الهواتف</p>";
echo "<p>يعمل على الأجهزة اللوحية</p>";
echo "<p>يعمل على الكمبيوتر</p>";
echo "</div>";
echo "</div>";
echo "</div>";

?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
}

h1, h2, h3 {
    color: #333;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
}

h1 {
    text-align: center;
    font-size: 2.5em;
    margin-bottom: 30px;
}

ul, ol {
    margin: 10px 0;
    padding-left: 20px;
}

a {
    text-decoration: none;
    transition: all 0.3s ease;
}

button {
    cursor: pointer;
    transition: all 0.2s ease;
}

button:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}
</style>

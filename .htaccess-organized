# Season Expo Kuwait - Organized File Structure
RewriteEngine On

# Language routing
RewriteRule ^en/?$ /homepage-fixed.php?lang=en [L]
RewriteRule ^ar/?$ /homepage-fixed.php?lang=ar [L]

# Admin routes
RewriteRule ^admin/?$ /admin/dashboard.php [L]
RewriteRule ^dashboard/?$ /admin/dashboard.php [L]
RewriteRule ^حسابي/?$ /admin/dashboard.php [L]
RewriteRule ^admin/slider/?$ /admin/slider-management.php [L]

# Auth routes
RewriteRule ^login/?$ /auth/login.php [L]
RewriteRule ^login-simple/?$ /auth/login.php [L]
RewriteRule ^logout/?$ /auth/logout.php [L]
RewriteRule ^register/?$ /auth/register.php [L]

# Exhibition routes
RewriteRule ^exhibitions/?$ /exhibitions/index.php [L]
RewriteRule ^exhibitions/([0-9]+)/?$ /exhibitions/details.php?id=$1 [L]
RewriteRule ^exhibitions/([0-9]+)/details/?$ /exhibitions/details.php?id=$1 [L]

# Booking routes
RewriteRule ^booking/?$ /booking/create.php [L]
RewriteRule ^booking/([0-9]+)/([0-9]+)/?$ /booking/create.php?booth_id=$1&exhibition_id=$2 [L]
RewriteRule ^book/([0-9]+)/([0-9]+)/?$ /booking/create.php?booth_id=$1&exhibition_id=$2 [L]

# Legacy redirects (301 permanent redirects)
RewriteRule ^dashboard\.php$ /admin/dashboard.php [R=301,L]
RewriteRule ^login-simple\.php$ /auth/login.php [R=301,L]
RewriteRule ^logout\.php$ /auth/logout.php [R=301,L]
RewriteRule ^exhibitions-simple\.php$ /exhibitions/index.php [R=301,L]
RewriteRule ^exhibition-details\.php\?id=([0-9]+)$ /exhibitions/details.php?id=$1 [R=301,L]
RewriteRule ^booking-simple\.php$ /booking/create.php [R=301,L]

# API routes (if needed in future)
RewriteRule ^api/exhibitions/?$ /api/exhibitions.php [L]
RewriteRule ^api/booths/([0-9]+)/?$ /api/booths.php?exhibition_id=$1 [L]

# Static file handling
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^images/(.*)$ /images/$1 [L]
RewriteRule ^css/(.*)$ /css/$1 [L]
RewriteRule ^js/(.*)$ /js/$1 [L]

# Security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>

# Compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Cache control
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/ico "access plus 1 year"
    ExpiresByType image/icon "access plus 1 year"
    ExpiresByType text/html "access plus 1 hour"
</IfModule>

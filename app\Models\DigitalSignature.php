<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class DigitalSignature extends Model
{
    use HasFactory;

    protected $fillable = [
        'document_type',
        'document_id',
        'user_id',
        'signer_name',
        'signer_email',
        'signer_ip',
        'signature_data',
        'signature_hash',
        'metadata',
        'signed_at',
        'is_verified',
        'verified_at',
        'verification_token',
        'certificate_path',
    ];

    protected $casts = [
        'metadata' => 'array',
        'signed_at' => 'datetime',
        'verified_at' => 'datetime',
        'is_verified' => 'boolean',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($signature) {
            $signature->verification_token = Str::uuid();
            $signature->signed_at = now();
            $signature->signature_hash = hash('sha256', $signature->signature_data . $signature->signer_email . $signature->signed_at);
        });
    }

    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    // Scopes
    public function scopeVerified($query)
    {
        return $query->where('is_verified', true);
    }

    public function scopeUnverified($query)
    {
        return $query->where('is_verified', false);
    }

    public function scopeByDocumentType($query, $type)
    {
        return $query->where('document_type', $type);
    }

    public function scopeByDocument($query, $type, $id)
    {
        return $query->where('document_type', $type)->where('document_id', $id);
    }

    // Methods
    public function verify(): bool
    {
        $this->update([
            'is_verified' => true,
            'verified_at' => now(),
        ]);

        return true;
    }

    public function generateCertificate(): string
    {
        $certificateData = [
            'signature_id' => $this->id,
            'document_type' => $this->document_type,
            'document_id' => $this->document_id,
            'signer_name' => $this->signer_name,
            'signer_email' => $this->signer_email,
            'signed_at' => $this->signed_at->toISOString(),
            'verification_token' => $this->verification_token,
            'signature_hash' => $this->signature_hash,
            'is_verified' => $this->is_verified,
            'verified_at' => $this->verified_at?->toISOString(),
        ];

        $certificateJson = json_encode($certificateData, JSON_PRETTY_PRINT);
        $fileName = 'certificate_' . $this->id . '_' . time() . '.json';
        $filePath = 'certificates/' . $fileName;

        Storage::disk('public')->put($filePath, $certificateJson);

        $this->update(['certificate_path' => $filePath]);

        return $filePath;
    }

    public function isValid(): bool
    {
        $expectedHash = hash('sha256', $this->signature_data . $this->signer_email . $this->signed_at);
        return $this->signature_hash === $expectedHash;
    }

    public function getVerificationUrl(): string
    {
        return route('signatures.verify', $this->verification_token);
    }

    // Constants
    const TYPE_CONTRACT = 'contract';
    const TYPE_AGREEMENT = 'agreement';
    const TYPE_BOOKING = 'booking';
    const TYPE_DOCUMENT = 'document';

    public static function getTypes(): array
    {
        return [
            self::TYPE_CONTRACT => 'Contract',
            self::TYPE_AGREEMENT => 'Agreement',
            self::TYPE_BOOKING => 'Booking',
            self::TYPE_DOCUMENT => 'Document',
        ];
    }
}

<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class DemoUsersSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create demo users for testing
        $demoUsers = [
            [
                'name' => 'Admin User',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'admin',
                'phone' => '+971 50 123 4567',
                'company_name' => 'Season Expo Admin',
                'country' => 'United Arab Emirates',
                'industry' => 'Technology & Innovation',
            ],
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'exhibitor',
                'phone' => '+971 50 234 5678',
                'company_name' => 'Tech Innovations LLC',
                'country' => 'United Arab Emirates',
                'industry' => 'Technology & Innovation',
            ],
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'organizer',
                'phone' => '+966 50 345 6789',
                'company_name' => 'Global Events Management',
                'country' => 'Saudi Arabia',
                'industry' => 'Education & Training',
            ],
            [
                'name' => 'Ahmed Al-Rashid',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'visitor',
                'phone' => '+965 50 456 7890',
                'company_name' => 'Kuwait Business Solutions',
                'country' => 'Kuwait',
                'industry' => 'Finance & Banking',
            ],
        ];

        foreach ($demoUsers as $userData) {
            User::firstOrCreate(
                ['email' => $userData['email']],
                $userData
            );
        }

        $this->command->info('Demo users created successfully!');
        $this->command->info('Login credentials:');
        $this->command->info('Admin: <EMAIL> / password');
        $this->command->info('Exhibitor: <EMAIL> / password');
        $this->command->info('Organizer: <EMAIL> / password');
        $this->command->info('Visitor: <EMAIL> / password');
    }
}

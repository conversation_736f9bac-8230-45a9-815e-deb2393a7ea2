<?php
// Absolute minimal Laravel test
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔬 MINIMAL LARAVEL TEST</h1>";

try {
    echo "Step 1: Basic PHP... ";
    echo "✅ OK<br>";
    
    echo "Step 2: Loading autoloader... ";
    require dirname(__DIR__) . '/vendor/autoload.php';
    echo "✅ OK<br>";
    
    echo "Step 3: Creating simple Laravel app... ";
    
    // Create the most basic Laravel app possible
    $app = new Illuminate\Foundation\Application(dirname(__DIR__));
    echo "✅ OK<br>";
    
    echo "Step 4: Basic container test... ";
    $app->singleton('test', function() { return 'working'; });
    $result = $app->make('test');
    echo "✅ OK (result: {$result})<br>";
    
    echo "Step 5: Environment loading... ";
    $app->useEnvironmentPath(dirname(__DIR__));
    $app->loadEnvironmentFrom('.env');
    echo "✅ OK<br>";
    
    echo "Step 6: Basic config... ";
    $app->singleton('config', function() {
        return new Illuminate\Config\Repository([
            'app' => ['name' => 'Test App', 'debug' => true]
        ]);
    });
    $config = $app->make('config');
    echo "✅ OK (app name: " . $config->get('app.name') . ")<br>";
    
    echo "<br><div style='background: #d4edda; padding: 15px; border-radius: 5px;'>";
    echo "<h3>✅ MINIMAL LARAVEL WORKS!</h3>";
    echo "<p>Basic Laravel components are functional. The issue is with the full bootstrap process.</p>";
    echo "</div>";
    
} catch (Throwable $e) {
    echo "<br><div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
    echo "<h3>❌ MINIMAL LARAVEL FAILED!</h3>";
    echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>File:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    echo "<p><strong>This confirms a fundamental Laravel/server issue.</strong></p>";
    echo "</div>";
}

echo "<h2>🚨 EMERGENCY ACTIONS:</h2>";
echo "<ol>";
echo "<li><strong>Contact Hostinger Support</strong> - This is likely a server issue</li>";
echo "<li><strong>Try different PHP version</strong> in Hostinger control panel</li>";
echo "<li><strong>Check if mod_rewrite is enabled</strong></li>";
echo "<li><strong>Verify file permissions</strong> are correct</li>";
echo "<li><strong>Consider fresh Laravel installation</strong></li>";
echo "</ol>";
?>

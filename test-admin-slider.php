<?php
// Test Admin Slider Functionality

echo "<h1>🧪 اختبار لوحة تحكم إدارة السلايد</h1>";

echo "<h2>1. اختبار قاعدة البيانات:</h2>";

try {
    $pdo = new PDO('sqlite:' . __DIR__ . '/database/database.sqlite');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Check if table exists
    $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='slider_images'");
    $table = $stmt->fetch();
    
    if ($table) {
        echo "<p style='color: green;'>✅ جدول slider_images موجود</p>";
        
        // Get slides count
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM slider_images");
        $total = $stmt->fetch()['total'];
        
        $stmt = $pdo->query("SELECT COUNT(*) as active FROM slider_images WHERE is_active = 1");
        $active = $stmt->fetch()['active'];
        
        echo "<p>📊 إجمالي السلايدات: <strong>$total</strong></p>";
        echo "<p>👁️ السلايدات النشطة: <strong>$active</strong></p>";
        
        // Show current slides
        $stmt = $pdo->query("SELECT id, title, is_active, sort_order FROM slider_images ORDER BY sort_order ASC");
        $slides = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if ($slides) {
            echo "<h3>السلايدات الحالية:</h3>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
            echo "<tr style='background: #f0f0f0;'><th>ID</th><th>العنوان</th><th>الترتيب</th><th>الحالة</th></tr>";
            foreach ($slides as $slide) {
                $status = $slide['is_active'] ? '✅ نشط' : '❌ غير نشط';
                echo "<tr>";
                echo "<td>{$slide['id']}</td>";
                echo "<td>" . htmlspecialchars($slide['title']) . "</td>";
                echo "<td>{$slide['sort_order']}</td>";
                echo "<td>$status</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
    } else {
        echo "<p style='color: red;'>❌ جدول slider_images غير موجود</p>";
        echo "<p><a href='/setup-database.php'>إنشاء الجدول</a></p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
}

echo "<h2>2. اختبار الروابط:</h2>";

$links = [
    '/admin/slider' => 'لوحة تحكم إدارة السلايد (Laravel Route)',
    '/admin-slider-management.php' => 'إدارة السلايد المباشرة (PHP)',
    '/' => 'الصفحة الرئيسية (مع السلايد)',
    '/setup-database.php' => 'إعداد قاعدة البيانات',
    '/dashboard' => 'لوحة التحكم الرئيسية'
];

echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; margin: 20px 0;'>";
foreach ($links as $url => $description) {
    $color = strpos($url, 'admin') !== false ? '#dc3545' : '#007bff';
    echo "<a href='$url' target='_blank' style='background: $color; color: white; padding: 15px; text-decoration: none; border-radius: 8px; text-align: center; display: block;'>";
    echo "<strong>" . basename($url) . "</strong><br>";
    echo "<small>$description</small>";
    echo "</a>";
}
echo "</div>";

echo "<h2>3. تعليمات الاختبار:</h2>";

echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
echo "<h3>خطوات الاختبار:</h3>";
echo "<ol>";
echo "<li><strong>اختبر لوحة التحكم:</strong> اذهب إلى /admin/slider</li>";
echo "<li><strong>أضف سلايد جديد:</strong> اضغط 'إضافة سلايد جديد'</li>";
echo "<li><strong>املأ البيانات:</strong>";
echo "<ul>";
echo "<li>العنوان: مثل 'معرض جديد 2025'</li>";
echo "<li>الوصف: وصف المعرض مع التاريخ والمكان</li>";
echo "<li>رابط الصورة: https://picsum.photos/1200/600?random=5</li>";
echo "<li>نص الزر: 'عرض التفاصيل'</li>";
echo "<li>رابط الزر: '/exhibitions/5'</li>";
echo "<li>لون الخلفية: اختر لون مناسب</li>";
echo "<li>ترتيب العرض: رقم أكبر من الموجود</li>";
echo "</ul>";
echo "</li>";
echo "<li><strong>احفظ واختبر:</strong> احفظ السلايد واذهب للصفحة الرئيسية</li>";
echo "<li><strong>اختبر التعديل:</strong> عدل سلايد موجود</li>";
echo "<li><strong>اختبر الإخفاء:</strong> أخف سلايد وتأكد من اختفائه</li>";
echo "</ol>";
echo "</div>";

echo "<h2>4. المشاكل الشائعة وحلولها:</h2>";

echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
echo "<h3>إذا واجهت مشاكل:</h3>";
echo "<ul>";
echo "<li><strong>خطأ 'no such table':</strong> اذهب إلى /setup-database.php</li>";
echo "<li><strong>خطأ 403 Forbidden:</strong> تأكد من تسجيل الدخول كأدمن</li>";
echo "<li><strong>الصور لا تظهر:</strong> تأكد من صحة رابط الصورة</li>";
echo "<li><strong>السلايد لا يظهر:</strong> تأكد من أن is_active = true</li>";
echo "<li><strong>ترتيب خاطئ:</strong> تأكد من رقم sort_order</li>";
echo "</ul>";
echo "</div>";

echo "<h2>5. اختبار سريع للوظائف:</h2>";

// Quick function test
echo "<div style='background: #e7f3ff; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
echo "<h3>اختبار سريع:</h3>";

try {
    $pdo = new PDO('sqlite:' . __DIR__ . '/database/database.sqlite');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Test adding a slide
    echo "<p>🧪 اختبار إضافة سلايد تجريبي...</p>";
    
    $testSlide = [
        'title' => 'سلايد تجريبي - ' . date('Y-m-d H:i:s'),
        'description' => 'هذا سلايد تجريبي لاختبار النظام\n📅 ' . date('d-m-Y') . ' | 📍 مكان تجريبي',
        'image_url' => 'https://picsum.photos/1200/600?random=' . rand(100, 999),
        'button_text' => 'اختبار',
        'button_link' => '/test',
        'background_color' => '#' . substr(md5(rand()), 0, 6),
        'sort_order' => 999,
        'is_active' => 0  // Hidden by default
    ];
    
    $stmt = $pdo->prepare("
        INSERT INTO slider_images (title, description, image_url, button_text, button_link, background_color, sort_order, is_active)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    ");
    
    $result = $stmt->execute([
        $testSlide['title'],
        $testSlide['description'],
        $testSlide['image_url'],
        $testSlide['button_text'],
        $testSlide['button_link'],
        $testSlide['background_color'],
        $testSlide['sort_order'],
        $testSlide['is_active']
    ]);
    
    if ($result) {
        $newId = $pdo->lastInsertId();
        echo "<p style='color: green;'>✅ تم إضافة سلايد تجريبي بنجاح (ID: $newId)</p>";
        echo "<p>يمكنك رؤيته في لوحة التحكم (مخفي افتراضياً)</p>";
        
        // Clean up - delete the test slide
        $stmt = $pdo->prepare("DELETE FROM slider_images WHERE id = ?");
        $stmt->execute([$newId]);
        echo "<p style='color: blue;'>🗑️ تم حذف السلايد التجريبي تلقائياً</p>";
    } else {
        echo "<p style='color: red;'>❌ فشل في إضافة السلايد التجريبي</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في الاختبار: " . $e->getMessage() . "</p>";
}

echo "</div>";

echo "<h2>6. النتيجة:</h2>";
echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 20px; border-radius: 8px; text-align: center;'>";
echo "<h3 style='color: #155724; margin: 0;'>🎉 نظام إدارة السلايد جاهز للاستخدام!</h3>";
echo "<p style='margin: 10px 0;'>يمكنك الآن إدارة السلايدات بسهولة من لوحة التحكم</p>";
echo "</div>";

?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
    background-color: #f8f9fa;
}

h1, h2, h3 {
    color: #333;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
}

table {
    font-size: 14px;
    margin: 10px 0;
}

th, td {
    padding: 8px;
    text-align: right;
    border: 1px solid #ddd;
}

th {
    background-color: #f0f0f0;
}

ul, ol {
    margin: 10px 0;
    padding-left: 20px;
}

a {
    text-decoration: none;
    transition: all 0.3s ease;
}

a:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}
</style>

# دليل نظام التوقيع الإلكتروني - Season Expo

## نظرة عامة

تم تطوير نظام التوقيع الإلكتروني المتكامل في مشروع Season Expo ليشمل:

### ✅ الميزات المتوفرة:

1. **نظام التوقيع الإلكتروني الأساسي**
   - إنشاء توقيعات رقمية آمنة
   - حفظ التوقيعات في قاعدة البيانات
   - التحقق من صحة التوقيعات
   - إنشاء شهادات رقمية

2. **نموذج إقرار وتعهد الشركات المشاركة**
   - النموذج الكامل بالمحتوى المطلوب
   - حقول البيانات المطلوبة (الاسم التجاري، رقم الترخيص، إلخ)
   - التوقيع الرقمي المدمج
   - التحقق من الموافقات المطلوبة

3. **نظام التحقق**
   - رموز تحقق فريدة لكل توقيع
   - صفحات التحقق من صحة التوقيعات
   - عرض تفاصيل التوقيع والبيانات الوصفية

## 📁 الملفات والمسارات:

### الملفات الرئيسية:
- `app/Models/DigitalSignature.php` - نموذج قاعدة البيانات
- `app/Http/Controllers/DigitalSignatureController.php` - تحكم التوقيعات
- `resources/views/signatures/create-simple.blade.php` - صفحة إنشاء توقيع عام
- `resources/views/signatures/company-declaration.blade.php` - صفحة إقرار الشركات
- `routes/web.php` - المسارات والمعالجات

### المسارات المتاحة:
- `/signatures` - الصفحة الرئيسية للتوقيعات
- `/signatures/create-simple` - إنشاء توقيع عام
- `/signatures/company-declaration` - إقرار وتعهد الشركات
- `/signatures/{id}/view-signature` - عرض التوقيع الرقمي (جديد!)
- `/signatures/verify/{token}` - التحقق من التوقيع
- `/signatures/{id}/view` - عرض المستند
- `/signatures/{id}/download` - تحميل المستند
- `/api/signatures/{id}` - API لجلب بيانات التوقيع

## 🔧 كيفية الاستخدام:

### 1. الوصول لنظام التوقيع:
```
http://localhost/season_expo_2/signatures
```

### 2. إنشاء إقرار وتعهد الشركة:
1. اذهب إلى `/signatures`
2. اضغط على "إنشاء إقرار وتعهد الشركة"
3. املأ البيانات المطلوبة:
   - الاسم التجاري
   - اسم صاحب الترخيص
   - رقم الترخيص التجاري
   - تاريخ الترخيص
4. ارسم التوقيع في المربع المخصص
5. وافق على الشروط المطلوبة
6. اضغط "إرسال الإقرار والتعهد"

### 3. التحقق من التوقيع:
- كل توقيع يحصل على رمز تحقق فريد
- يمكن التحقق من صحة التوقيع عبر الرابط المُولد تلقائياً
- صفحة التحقق تعرض جميع تفاصيل التوقيع والبيانات

## 📋 محتوى إقرار وتعهد الشركات:

النموذج يتضمن النص الكامل المطلوب:

```
إقرار وتعهد
الشركات المشاركة

أتعهد أنا الموقع أدناه
المشارك فى معرض
المزمع إقامته خلال الفترة ..

بأن ألتزم بكل ما تم ذكره في القرار الوزاري رقم ( 303) لسنة 2018 
بشأن القواعد العامة لتنظيم إقامة المعارض التجارية المؤقتة بدولة الكويت...
```

## 🔒 الأمان والتحقق:

1. **التشفير**: كل توقيع يحصل على hash فريد
2. **التحقق**: نظام التحقق يضمن عدم تعديل التوقيع
3. **البيانات الوصفية**: حفظ IP، التاريخ، والوقت
4. **الشهادات**: إنشاء شهادات رقمية قابلة للتحميل

## 🧪 الاختبار:

استخدم ملف الاختبار للتحقق من النظام:
```
http://localhost/season_expo_2/test-digital-signature.php
```

## 🔗 التكامل مع نظام الحجز:

النظام مُدمج مع عملية الحجز:
- يتطلب توقيع رقمي معتمد قبل إتمام الدفع
- التحقق التلقائي من وجود التوقيع
- ربط التوقيع بعملية الحجز

## 📱 الدعم للأجهزة المحمولة:

- دعم كامل للرسم باللمس على الأجهزة المحمولة
- واجهة متجاوبة تعمل على جميع الأحجام
- تحسين تجربة المستخدم للهواتف والأجهزة اللوحية

## 🚀 الميزات المتقدمة:

1. **إنشاء مستندات HTML/PDF**
2. **نظام التحقق المتقدم**
3. **حفظ صور التوقيعات**
4. **البيانات الوصفية الشاملة**
5. **نظام الأذونات والصلاحيات**

## 📞 الدعم الفني:

في حالة وجود مشاكل:
1. تحقق من ملف الاختبار أولاً
2. تأكد من وجود جميع الملفات المطلوبة
3. تحقق من صلاحيات مجلدات التخزين
4. راجع سجلات الأخطاء في Laravel

---

**ملاحظة**: النظام جاهز للاستخدام ويتضمن جميع المتطلبات المذكورة في طلبك الأصلي.

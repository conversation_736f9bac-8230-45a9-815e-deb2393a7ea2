<?php
// Test Dynamic Exhibition System
// This file tests the new dynamic exhibition system

session_start();

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='utf-8'>";
echo "<title>اختبار النظام الديناميكي للمعارض - Season Expo Kuwait</title>";
echo "<script src='https://cdn.tailwindcss.com'></script>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }";
echo "</style>";
echo "</head>";
echo "<body class='bg-gray-50 p-8'>";

echo "<div class='max-w-4xl mx-auto'>";
echo "<h1 class='text-3xl font-bold mb-8' style='color: #2C3E50;'>🔄 اختبار النظام الديناميكي للمعارض</h1>";

// Database connection test
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>📊 اختبار قاعدة البيانات</h2>";

// Include database configuration
require_once 'config-database.php';

try {
    $pdo = getDatabaseConnection();

    echo "<div class='bg-green-50 border border-green-200 rounded-lg p-4 mb-4'>";
    echo "<h3 class='font-semibold text-green-700 mb-2'>✅ الاتصال بقاعدة البيانات ناجح</h3>";
    echo "</div>";

    // Check exhibitions
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM exhibitions WHERE status = 'published'");
    $exhibitionCount = $stmt->fetch()['count'];

    echo "<div class='bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4'>";
    echo "<h3 class='font-semibold text-blue-700 mb-2'>📈 إحصائيات المعارض</h3>";
    echo "<p>عدد المعارض المنشورة: <strong>{$exhibitionCount}</strong></p>";
    echo "</div>";

    // Get all exhibitions
    $stmt = $pdo->query("SELECT id, title, status, start_date, end_date FROM exhibitions ORDER BY start_date DESC");
    $exhibitions = $stmt->fetchAll(PDO::FETCH_OBJ);

    if (count($exhibitions) > 0) {
        echo "<div class='bg-white border border-gray-200 rounded-lg p-4'>";
        echo "<h3 class='font-semibold mb-3'>📋 قائمة المعارض في قاعدة البيانات:</h3>";
        echo "<div class='space-y-2'>";

        foreach ($exhibitions as $exhibition) {
            $statusColor = $exhibition->status === 'published' ? 'text-green-600' : 'text-yellow-600';
            echo "<div class='flex items-center justify-between p-2 border border-gray-100 rounded'>";
            echo "<div>";
            echo "<span class='font-medium'>{$exhibition->title}</span>";
            echo "<span class='text-sm text-gray-500 mr-2'>(ID: {$exhibition->id})</span>";
            echo "</div>";
            echo "<span class='text-sm {$statusColor}'>{$exhibition->status}</span>";
            echo "</div>";
        }

        echo "</div>";
        echo "</div>";
    }

} catch(PDOException $e) {
    echo "<div class='bg-red-50 border border-red-200 rounded-lg p-4'>";
    echo "<h3 class='font-semibold text-red-700 mb-2'>❌ خطأ في قاعدة البيانات</h3>";
    echo "<p class='text-red-600'>" . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "</div>";

// Test dynamic exhibition page
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>🧪 اختبار الصفحة الديناميكية</h2>";

echo "<div class='space-y-4'>";

echo "<div class='bg-green-50 border border-green-200 rounded-lg p-4'>";
echo "<h3 class='font-semibold text-green-700 mb-2'>✅ المميزات الجديدة:</h3>";
echo "<ul class='text-sm space-y-1 list-disc list-inside ml-4'>";
echo "<li>ملف واحد يتعامل مع جميع المعارض</li>";
echo "<li>بيانات ديناميكية من قاعدة البيانات</li>";
echo "<li>لا حاجة لإنشاء ملفات منفصلة</li>";
echo "<li>يدعم إضافة معارض جديدة تلقائياً</li>";
echo "</ul>";
echo "</div>";

echo "<div class='bg-blue-50 border border-blue-200 rounded-lg p-4'>";
echo "<h3 class='font-semibold text-blue-700 mb-2'>🔗 اختبار الروابط الجديدة:</h3>";

if (isset($exhibitions) && count($exhibitions) > 0) {
    echo "<div class='space-y-2'>";
    foreach ($exhibitions as $exhibition) {
        if ($exhibition->status === 'published') {
            echo "<a href='/exhibition-details.php?id={$exhibition->id}' target='_blank' class='block w-full text-center px-3 py-2 border border-blue-500 text-blue-600 rounded hover:bg-blue-50'>";
            echo "🏢 {$exhibition->title}";
            echo "</a>";
        }
    }
    echo "</div>";
} else {
    echo "<p class='text-gray-600'>لا توجد معارض منشورة للاختبار</p>";
}

echo "</div>";

echo "</div>";
echo "</div>";

// Test old vs new system
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>⚖️ مقارنة النظام القديم والجديد</h2>";

echo "<div class='grid grid-cols-1 md:grid-cols-2 gap-6'>";

echo "<div class='bg-red-50 border border-red-200 rounded-lg p-4'>";
echo "<h3 class='font-semibold text-red-700 mb-3'>❌ النظام القديم (مشاكل):</h3>";
echo "<ul class='text-sm space-y-1 list-disc list-inside ml-4'>";
echo "<li>ملفات منفصلة لكل معرض</li>";
echo "<li>exhibitions-1-simple.php</li>";
echo "<li>exhibitions-2-simple.php</li>";
echo "<li>exhibitions-3-simple.php</li>";
echo "<li>صعوبة في الإدارة والصيانة</li>";
echo "<li>المعارض 2 و 3 لا تعمل</li>";
echo "</ul>";
echo "</div>";

echo "<div class='bg-green-50 border border-green-200 rounded-lg p-4'>";
echo "<h3 class='font-semibold text-green-700 mb-3'>✅ النظام الجديد (حلول):</h3>";
echo "<ul class='text-sm space-y-1 list-disc list-inside ml-4'>";
echo "<li>ملف واحد: exhibition-details.php</li>";
echo "<li>يتعامل مع أي معرض: ?id=1, ?id=2, ?id=3</li>";
echo "<li>بيانات من قاعدة البيانات</li>";
echo "<li>إضافة معارض جديدة تلقائياً</li>";
echo "<li>سهولة في الصيانة</li>";
echo "<li>جميع المعارض تعمل</li>";
echo "</ul>";
echo "</div>";

echo "</div>";
echo "</div>";

// File status
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>📁 حالة الملفات</h2>";

$files = [
    'exhibition-details.php' => 'الملف الديناميكي الجديد',
    'homepage-fixed.php' => 'الصفحة الرئيسية (محدثة)',
    'exhibitions-simple.php' => 'صفحة المعارض (محدثة)',
    'exhibitions-1-simple.php' => 'ملف قديم (يمكن حذفه)',
    'exhibitions-2-simple.php' => 'ملف قديم (يمكن حذفه)',
    'exhibitions-3-simple.php' => 'ملف قديم (يمكن حذفه)'
];

echo "<div class='grid grid-cols-1 md:grid-cols-2 gap-4'>";

foreach ($files as $file => $description) {
    $fullPath = __DIR__ . '/' . $file;
    echo "<div class='border border-gray-200 rounded-lg p-4'>";
    echo "<h3 class='font-semibold mb-2'>{$description}</h3>";
    echo "<p class='text-sm text-gray-600 mb-2'>/{$file}</p>";

    if (file_exists($fullPath)) {
        $size = round(filesize($fullPath)/1024, 2);
        echo "<div class='text-green-600 mb-2'>✅ موجود ({$size} KB)</div>";

        if (strpos($file, 'exhibition-details') !== false) {
            echo "<div class='text-blue-600 text-sm'>🆕 ملف جديد</div>";
        } elseif (strpos($file, 'exhibitions-') !== false && strpos($file, 'simple.php') !== false) {
            echo "<div class='text-yellow-600 text-sm'>⚠️ يمكن حذفه</div>";
        } else {
            echo "<div class='text-green-600 text-sm'>✅ محدث</div>";
        }

    } else {
        echo "<div class='text-red-600'>❌ مفقود</div>";
    }
    echo "</div>";
}

echo "</div>";
echo "</div>";

// Instructions
echo "<div class='bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>📋 تعليمات التطبيق</h2>";

echo "<div class='space-y-4'>";

echo "<div class='bg-white p-4 rounded-lg border'>";
echo "<h3 class='font-semibold mb-2' style='color: #2C3E50;'>1. ارفع الملفات الجديدة:</h3>";
echo "<ul class='text-sm space-y-1 list-disc list-inside ml-4'>";
echo "<li><code>exhibition-details.php</code> - الملف الديناميكي الجديد</li>";
echo "<li><code>homepage-fixed.php</code> - محدث بالروابط الجديدة</li>";
echo "<li><code>exhibitions-simple.php</code> - محدث بالروابط الجديدة</li>";
echo "</ul>";
echo "</div>";

echo "<div class='bg-white p-4 rounded-lg border'>";
echo "<h3 class='font-semibold mb-2' style='color: #2C3E50;'>2. اختبر النظام الجديد:</h3>";
echo "<ul class='text-sm space-y-1 list-disc list-inside ml-4'>";
echo "<li>اذهب للصفحة الرئيسية واختبر أزرار المعارض</li>";
echo "<li>اختبر صفحة المعارض</li>";
echo "<li>تأكد من عمل جميع المعارض</li>";
echo "</ul>";
echo "</div>";

echo "<div class='bg-white p-4 rounded-lg border'>";
echo "<h3 class='font-semibold mb-2' style='color: #2C3E50;'>3. احذف الملفات القديمة (اختياري):</h3>";
echo "<ul class='text-sm space-y-1 list-disc list-inside ml-4'>";
echo "<li><code>exhibitions-1-simple.php</code></li>";
echo "<li><code>exhibitions-2-simple.php</code></li>";
echo "<li><code>exhibitions-3-simple.php</code></li>";
echo "</ul>";
echo "</div>";

echo "</div>";
echo "</div>";

// Quick actions
echo "<div class='text-center space-x-reverse space-x-4'>";
echo "<a href='/homepage-fixed.php?lang=ar' class='text-white px-6 py-3 rounded-lg hover:opacity-80' style='background: #2C3E50;'>🏠 الصفحة الرئيسية</a>";
echo "<a href='/exhibitions-simple.php' class='bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700'>🏢 المعارض</a>";
echo "<a href='/exhibition-details.php?id=1' class='bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700'>🧪 اختبار المعرض 1</a>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>

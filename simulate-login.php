<?php
// Simulate Login for Testing
// This file simulates a user login for testing purposes

session_start();

// Simulate user login
$_SESSION['user_id'] = 'test_user_123';
$_SESSION['user_name'] = 'مستخدم تجريبي';
$_SESSION['user_email'] = '<EMAIL>';

// Return success response
header('Content-Type: application/json');
echo json_encode([
    'success' => true,
    'message' => 'تم تسجيل الدخول بنجاح',
    'user_id' => $_SESSION['user_id']
]);
?>

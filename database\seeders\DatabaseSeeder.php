<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Create admin user
        User::factory()->create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'role' => 'admin',
        ]);

        // Create organizer user
        User::factory()->create([
            'name' => 'Exhibition Organizer',
            'email' => '<EMAIL>',
            'role' => 'organizer',
            'company' => 'Event Management Co.',
        ]);

        // Create exhibitor user
        User::factory()->create([
            'name' => 'Test Exhibitor',
            'email' => '<EMAIL>',
            'role' => 'exhibitor',
            'company' => 'Tech Solutions Inc.',
        ]);

        // Create regular users
        User::factory(10)->create();

        // Seed categories and exhibitions
        $this->call([
            CategorySeeder::class,
            ExhibitionSeeder::class,
        ]);
    }
}

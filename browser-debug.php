<?php
echo "<h1>Browser Debug Information</h1>";

// Show browser information
echo "<h2>Browser Information:</h2>";
echo "User Agent: " . $_SERVER['HTTP_USER_AGENT'] . "<br>";
echo "Accept: " . ($_SERVER['HTTP_ACCEPT'] ?? 'Not set') . "<br>";
echo "Accept-Encoding: " . ($_SERVER['HTTP_ACCEPT_ENCODING'] ?? 'Not set') . "<br>";
echo "Accept-Language: " . ($_SERVER['HTTP_ACCEPT_LANGUAGE'] ?? 'Not set') . "<br>";

// Check if this is Edge
$isEdge = strpos($_SERVER['HTTP_USER_AGENT'], 'Edg') !== false;
$isChrome = strpos($_SERVER['HTTP_USER_AGENT'], 'Chrome') !== false && !$isEdge;

echo "<br><h2>Browser Detection:</h2>";
echo "Is Microsoft Edge: " . ($isEdge ? "YES" : "NO") . "<br>";
echo "Is Google Chrome: " . ($isChrome ? "YES" : "NO") . "<br>";

// Test Laravel application
echo "<br><h2>Laravel Application Test:</h2>";

try {
    require '../vendor/autoload.php';
    $app = require_once '../bootstrap/app.php';
    
    echo "✅ Laravel loaded successfully<br>";
    
    // Test database
    $exhibitions = \App\Models\Exhibition::count();
    echo "✅ Database working: {$exhibitions} exhibitions<br>";
    
    // Test routes
    $routes = \Illuminate\Support\Facades\Route::getRoutes();
    echo "✅ Routes loaded: " . count($routes) . " routes<br>";
    
    // Check if home route exists
    $homeRoute = null;
    foreach ($routes as $route) {
        if ($route->uri() === '/') {
            $homeRoute = $route;
            break;
        }
    }
    
    if ($homeRoute) {
        echo "✅ Home route found: " . $homeRoute->getActionName() . "<br>";
    } else {
        echo "❌ Home route not found<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Laravel error: " . $e->getMessage() . "<br>";
}

// Test basic PHP output
echo "<br><h2>PHP Output Test:</h2>";
echo "✅ PHP is working<br>";
echo "✅ Output buffering: " . (ob_get_level() > 0 ? "ON" : "OFF") . "<br>";

// Check headers
echo "<br><h2>Response Headers:</h2>";
$headers = headers_list();
if (empty($headers)) {
    echo "No headers sent yet<br>";
} else {
    foreach ($headers as $header) {
        echo $header . "<br>";
    }
}

// JavaScript test for browser compatibility
echo "<br><h2>JavaScript Test:</h2>";
echo '<div id="js-test">JavaScript not working</div>';
echo '<script>
document.getElementById("js-test").innerHTML = "✅ JavaScript is working";
console.log("Browser debug script loaded");
</script>';

echo "<br><h2>Recommendations:</h2>";
if ($isEdge) {
    echo "For Microsoft Edge:<br>";
    echo "1. Clear browser cache and cookies<br>";
    echo "2. Disable ad blockers<br>";
    echo "3. Check developer tools (F12) for errors<br>";
    echo "4. Try incognito/private mode<br>";
}

if ($isChrome) {
    echo "For Google Chrome:<br>";
    echo "1. Check if you're seeing cached content<br>";
    echo "2. Hard refresh (Ctrl+Shift+R)<br>";
    echo "3. Clear browser cache<br>";
}

echo "<br><a href='/'>Test Main Application</a>";
?>

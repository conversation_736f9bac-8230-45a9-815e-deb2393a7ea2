<?php
echo "<h1><PERSON><PERSON> Error Logs</h1>";

$logPath = dirname(__DIR__) . '/storage/logs/laravel.log';

echo "<h2>Log File Status:</h2>";

if (file_exists($logPath)) {
    echo "✅ Laravel log file exists<br>";
    echo "Size: " . filesize($logPath) . " bytes<br>";
    echo "Modified: " . date('Y-m-d H:i:s', filemtime($logPath)) . "<br><br>";
    
    // Read the last 50 lines of the log
    $lines = file($logPath);
    if ($lines) {
        $totalLines = count($lines);
        $lastLines = array_slice($lines, -50); // Last 50 lines
        
        echo "<h2>Last 50 Log Entries:</h2>";
        echo "<div style='background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; border-radius: 5px; max-height: 600px; overflow-y: auto;'>";
        echo "<pre style='margin: 0; font-size: 12px;'>";
        
        foreach ($lastLines as $lineNum => $line) {
            $actualLineNum = $totalLines - 50 + $lineNum + 1;
            
            // Highlight error lines
            if (strpos($line, 'ERROR') !== false || strpos($line, 'CRITICAL') !== false) {
                echo "<span style='background: #f8d7da; color: #721c24;'>";
                echo htmlspecialchars($line);
                echo "</span>";
            } elseif (strpos($line, 'WARNING') !== false) {
                echo "<span style='background: #fff3cd; color: #856404;'>";
                echo htmlspecialchars($line);
                echo "</span>";
            } else {
                echo htmlspecialchars($line);
            }
        }
        
        echo "</pre>";
        echo "</div>";
        
        // Look for recent errors
        echo "<h2>Recent Error Analysis:</h2>";
        $recentErrors = [];
        $today = date('Y-m-d');
        
        foreach ($lastLines as $line) {
            if (strpos($line, $today) !== false && (strpos($line, 'ERROR') !== false || strpos($line, 'CRITICAL') !== false)) {
                $recentErrors[] = $line;
            }
        }
        
        if (!empty($recentErrors)) {
            echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px;'>";
            echo "<h3>🚨 Recent Errors Found:</h3>";
            foreach ($recentErrors as $error) {
                echo "<p style='margin: 5px 0; font-family: monospace; font-size: 12px;'>" . htmlspecialchars($error) . "</p>";
            }
            echo "</div>";
        } else {
            echo "<div style='background: #d1ecf1; padding: 15px; border: 1px solid #bee5eb; border-radius: 5px;'>";
            echo "<p>ℹ️ No recent errors found in logs.</p>";
            echo "</div>";
        }
        
    } else {
        echo "❌ Could not read log file<br>";
    }
    
} else {
    echo "❌ Laravel log file not found<br>";
    echo "Expected location: {$logPath}<br>";
    
    // Check if storage/logs directory exists
    $logsDir = dirname($logPath);
    if (is_dir($logsDir)) {
        echo "✅ Logs directory exists<br>";
        echo "Directory contents:<br>";
        $files = scandir($logsDir);
        foreach ($files as $file) {
            if ($file !== '.' && $file !== '..') {
                echo "- {$file}<br>";
            }
        }
    } else {
        echo "❌ Logs directory missing: {$logsDir}<br>";
    }
}

echo "<h2>Actions:</h2>";
echo "<ul>";
echo "<li><a href='get-full-error.php'>Get Full Error Details</a></li>";
echo "<li><a href='check-routes.php'>Check Routes</a></li>";
echo "<li><a href='check-database.php'>Check Database Connection</a></li>";
echo "</ul>";
?>

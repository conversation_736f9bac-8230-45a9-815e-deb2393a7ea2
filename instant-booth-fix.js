// Instant Booth Color Fix - JavaScript Solution
// Add this script to fix booth colors immediately

console.log("🚀 Starting Instant Booth Color Fix...");

// Function to fix booth colors
function fixBoothColors() {
    console.log("🔧 Fixing booth colors...");
    
    // Find all divs that might be booths
    const allDivs = document.querySelectorAll('div');
    let fixedCount = 0;
    
    allDivs.forEach(function(div, index) {
        const style = div.getAttribute('style') || '';
        const hasBoothContent = div.textContent.includes('KWD') || 
                               div.textContent.includes('متاح') || 
                               div.textContent.includes('محجوز') ||
                               div.textContent.includes('احجز الآن');
        
        // Check if this div has dark background and booth content
        if (style.includes('background: #2C3E50') && hasBoothContent) {
            console.log(`🎨 Fixing booth ${fixedCount + 1}:`, div);
            
            // Change background to light
            div.style.setProperty('background', 'linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)', 'important');
            div.style.setProperty('border', '2px solid #2C3E50', 'important');
            div.style.setProperty('border-radius', '8px', 'important');
            
            // Fix all text elements inside
            const textElements = div.querySelectorAll('h1, h2, h3, h4, h5, h6, p, div, span');
            textElements.forEach(function(element) {
                // Skip buttons and special elements
                if (!element.closest('a') && !element.classList.contains('bg-green-100') && !element.classList.contains('bg-red-100')) {
                    element.style.setProperty('color', '#2C3E50', 'important');
                    
                    // Special handling for prices
                    if (element.textContent.includes('KWD')) {
                        element.style.setProperty('color', '#E74C3C', 'important');
                        element.style.setProperty('font-weight', 'bold', 'important');
                    }
                }
            });
            
            // Fix status badges
            const badges = div.querySelectorAll('span');
            badges.forEach(function(badge) {
                if (badge.textContent.includes('متاح')) {
                    badge.style.setProperty('background', '#27AE60', 'important');
                    badge.style.setProperty('color', 'white', 'important');
                } else if (badge.textContent.includes('محجوز')) {
                    badge.style.setProperty('background', '#E74C3C', 'important');
                    badge.style.setProperty('color', 'white', 'important');
                }
            });
            
            // Add visual confirmation
            div.style.setProperty('box-shadow', '0 4px 6px rgba(0, 0, 0, 0.1)', 'important');
            
            fixedCount++;
        }
    });
    
    console.log(`✅ Fixed ${fixedCount} booths successfully!`);
    
    // Add global CSS to prevent future issues
    const style = document.createElement('style');
    style.textContent = `
        /* Global booth fix */
        div[style*="background: #2C3E50"] {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%) !important;
            border: 2px solid #2C3E50 !important;
        }
        
        div[style*="background: #2C3E50"] h3,
        div[style*="background: #2C3E50"] p,
        div[style*="background: #2C3E50"] div {
            color: #2C3E50 !important;
        }
        
        div[style*="background: #2C3E50"] div:contains("KWD") {
            color: #E74C3C !important;
            font-weight: bold !important;
        }
    `;
    document.head.appendChild(style);
    
    return fixedCount;
}

// Run immediately when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', fixBoothColors);
} else {
    fixBoothColors();
}

// Also run after a short delay to catch any dynamically loaded content
setTimeout(fixBoothColors, 1000);
setTimeout(fixBoothColors, 3000);

// Run when page is fully loaded
window.addEventListener('load', fixBoothColors);

console.log("🎯 Instant Booth Fix script loaded successfully!");

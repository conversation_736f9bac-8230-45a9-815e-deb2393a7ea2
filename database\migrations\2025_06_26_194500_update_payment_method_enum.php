<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update the enum to include 'myfatoorah'
        DB::statement("ALTER TABLE payments MODIFY COLUMN payment_method ENUM('credit_card', 'bank_transfer', 'paypal', 'stripe', 'cash', 'myfatoorah') DEFAULT 'credit_card'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert back to original enum
        DB::statement("ALTER TABLE payments MODIFY COLUMN payment_method ENUM('credit_card', 'bank_transfer', 'paypal', 'stripe', 'cash') DEFAULT 'credit_card'");
    }
};

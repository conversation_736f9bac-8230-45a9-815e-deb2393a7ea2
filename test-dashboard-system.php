<?php
// Test Dashboard System
// This file tests the user dashboard and login flow

session_start();

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='utf-8'>";
echo "<title>اختبار نظام حسابي - Season Expo Kuwait</title>";
echo "<script src='https://cdn.tailwindcss.com'></script>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }";
echo "</style>";
echo "</head>";
echo "<body class='bg-gray-50 p-8'>";

echo "<div class='max-w-4xl mx-auto'>";
echo "<h1 class='text-3xl font-bold mb-8' style='color: #2C3E50;'>👤 اختبار نظام \"حسابي\"</h1>";

// Current login status
$isLoggedIn = isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);

echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>🔐 حالة تسجيل الدخول</h2>";

if ($isLoggedIn) {
    echo "<div class='bg-green-50 border border-green-200 rounded-lg p-4'>";
    echo "<div class='flex items-center justify-between'>";
    echo "<div>";
    echo "<h3 class='font-semibold text-green-700'>✅ مسجل الدخول</h3>";
    echo "<p class='text-green-600'>معرف المستخدم: " . $_SESSION['user_id'] . "</p>";
    echo "<p class='text-green-600'>البريد الإلكتروني: " . ($_SESSION['user_email'] ?? 'غير محدد') . "</p>";
    echo "</div>";
    echo "<div class='space-x-reverse space-x-2'>";
    echo "<a href='/dashboard.php' class='bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 text-sm'>حسابي</a>";
    echo "<a href='/logout.php' class='bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 text-sm'>تسجيل الخروج</a>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
} else {
    echo "<div class='bg-yellow-50 border border-yellow-200 rounded-lg p-4'>";
    echo "<div class='flex items-center justify-between'>";
    echo "<div>";
    echo "<h3 class='font-semibold text-yellow-700'>⚠️ غير مسجل الدخول</h3>";
    echo "<p class='text-yellow-600'>يجب تسجيل الدخول للوصول لصفحة \"حسابي\"</p>";
    echo "</div>";
    echo "<div class='space-x-reverse space-x-2'>";
    echo "<a href='/login-simple.php' class='bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 text-sm'>تسجيل الدخول</a>";
    echo "<a href='?simulate_login=1' class='bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 text-sm'>تسجيل دخول تجريبي</a>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
}

// Handle simulate login
if (isset($_GET['simulate_login'])) {
    $_SESSION['user_id'] = 'test_user_' . time();
    $_SESSION['user_name'] = 'مستخدم تجريبي';
    $_SESSION['user_email'] = '<EMAIL>';
    header('Location: ' . $_SERVER['PHP_SELF']);
    exit;
}

echo "</div>";

// Test dashboard access
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>🏠 اختبار الوصول لصفحة \"حسابي\"</h2>";

echo "<div class='space-y-4'>";

echo "<div class='bg-blue-50 border border-blue-200 rounded-lg p-4'>";
echo "<h3 class='font-semibold text-blue-700 mb-2'>📋 الروابط المختلفة لصفحة \"حسابي\":</h3>";
echo "<div class='space-y-2'>";

$dashboardUrls = [
    '/dashboard.php' => 'رابط مباشر',
    '/dashboard' => 'رابط نظيف',
    '/حسابي' => 'رابط عربي'
];

foreach ($dashboardUrls as $url => $description) {
    echo "<a href='{$url}' target='_blank' class='block w-full text-center px-3 py-2 border border-blue-500 text-blue-600 rounded hover:bg-blue-50'>";
    echo "🔗 {$description}";
    echo "<div class='text-xs text-gray-500'>{$url}</div>";
    echo "</a>";
}

echo "</div>";
echo "</div>";

if (!$isLoggedIn) {
    echo "<div class='bg-yellow-50 border border-yellow-200 rounded-lg p-4'>";
    echo "<h3 class='font-semibold text-yellow-700 mb-2'>⚠️ ملاحظة:</h3>";
    echo "<p class='text-yellow-600 text-sm'>إذا لم تكن مسجل الدخول، ستتم إعادة توجيهك لصفحة تسجيل الدخول</p>";
    echo "</div>";
}

echo "</div>";
echo "</div>";

// Test login flow
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>🔄 اختبار تدفق تسجيل الدخول</h2>";

echo "<div class='space-y-6'>";

// Step 1: Login page
echo "<div class='border border-gray-200 rounded-lg p-4'>";
echo "<h3 class='font-semibold mb-3 text-blue-700'>الخطوة 1: صفحة تسجيل الدخول</h3>";
echo "<p class='text-sm text-gray-600 mb-3'>المستخدم يدخل بياناته</p>";
echo "<a href='/login-simple.php' target='_blank' class='bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 text-sm'>🔐 صفحة تسجيل الدخول</a>";
echo "</div>";

// Step 2: After login
echo "<div class='border border-gray-200 rounded-lg p-4'>";
echo "<h3 class='font-semibold mb-3 text-green-700'>الخطوة 2: بعد تسجيل الدخول</h3>";
echo "<p class='text-sm text-gray-600 mb-3'>يتم توجيه المستخدم للصفحة الرئيسية (تم التحديث)</p>";
echo "<div class='bg-green-50 border border-green-200 rounded p-3'>";
echo "<p class='text-green-700 text-sm'><strong>التحديث الجديد:</strong> بعد تسجيل الدخول، يتم التوجيه للصفحة الرئيسية بدلاً من dashboard</p>";
echo "</div>";
echo "</div>";

// Step 3: Access dashboard
echo "<div class='border border-gray-200 rounded-lg p-4'>";
echo "<h3 class='font-semibold mb-3 text-purple-700'>الخطوة 3: الوصول لصفحة \"حسابي\"</h3>";
echo "<p class='text-sm text-gray-600 mb-3'>المستخدم يمكنه الوصول لصفحة \"حسابي\" من القائمة</p>";
echo "<div class='space-x-reverse space-x-2'>";
echo "<a href='/dashboard.php' target='_blank' class='bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700 text-sm'>👤 حسابي</a>";
echo "<a href='/homepage-fixed.php?lang=ar' target='_blank' class='bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700 text-sm'>🏠 الصفحة الرئيسية</a>";
echo "</div>";
echo "</div>";

echo "</div>";
echo "</div>";

// File status
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>📁 حالة الملفات</h2>";

$files = [
    'dashboard.php' => 'صفحة حسابي (جديد)',
    'logout.php' => 'صفحة تسجيل الخروج (جديد)',
    'login-simple.php' => 'صفحة تسجيل الدخول (محدث)',
    '.htaccess' => 'ملف إعادة التوجيه (محدث)'
];

echo "<div class='grid grid-cols-1 md:grid-cols-2 gap-3'>";

foreach ($files as $file => $description) {
    $fullPath = __DIR__ . '/' . $file;
    echo "<div class='flex items-center justify-between p-3 border border-gray-200 rounded-lg'>";
    echo "<div>";
    echo "<h3 class='font-semibold text-sm'>{$description}</h3>";
    echo "<p class='text-xs text-gray-600'>/{$file}</p>";
    echo "</div>";
    echo "<div>";
    
    if (file_exists($fullPath)) {
        $size = round(filesize($fullPath)/1024, 2);
        echo "<div class='text-green-600 text-sm'>✅ موجود ({$size} KB)</div>";
    } else {
        echo "<div class='text-red-600 text-sm'>❌ مفقود</div>";
    }
    
    echo "</div>";
    echo "</div>";
}

echo "</div>";
echo "</div>";

// Expected behavior
echo "<div class='bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>✅ السلوك المتوقع</h2>";

echo "<div class='space-y-4'>";

echo "<div class='bg-white p-4 rounded-lg border'>";
echo "<h3 class='font-semibold mb-2' style='color: #2C3E50;'>1. تسجيل الدخول العادي:</h3>";
echo "<ul class='text-sm space-y-1 list-disc list-inside ml-4'>";
echo "<li>المستخدم يسجل الدخول من /login-simple.php</li>";
echo "<li>يتم توجيهه للصفحة الرئيسية (homepage-fixed.php)</li>";
echo "<li>يمكنه الوصول لصفحة \"حسابي\" من القائمة</li>";
echo "</ul>";
echo "</div>";

echo "<div class='bg-white p-4 rounded-lg border'>";
echo "<h3 class='font-semibold mb-2' style='color: #2C3E50;'>2. تسجيل الدخول مع إعادة التوجيه:</h3>";
echo "<ul class='text-sm space-y-1 list-disc list-inside ml-4'>";
echo "<li>المستخدم يحاول الوصول لصفحة محمية (مثل الحجز)</li>";
echo "<li>يتم توجيهه لتسجيل الدخول مع حفظ الرابط الأصلي</li>";
echo "<li>بعد تسجيل الدخول، يعود للصفحة المطلوبة</li>";
echo "</ul>";
echo "</div>";

echo "<div class='bg-white p-4 rounded-lg border'>";
echo "<h3 class='font-semibold mb-2' style='color: #2C3E50;'>3. صفحة \"حسابي\":</h3>";
echo "<ul class='text-sm space-y-1 list-disc list-inside ml-4'>";
echo "<li>عرض معلومات المستخدم</li>";
echo "<li>عرض حجوزات المستخدم</li>";
echo "<li>إجراءات سريعة (تصفح المعارض، إدارة السلايد)</li>";
echo "<li>إعدادات الحساب وتسجيل الخروج</li>";
echo "</ul>";
echo "</div>";

echo "</div>";
echo "</div>";

// Quick actions
echo "<div class='text-center space-x-reverse space-x-4'>";
echo "<a href='/login-simple.php' class='text-white px-6 py-3 rounded-lg hover:opacity-80' style='background: #2C3E50;'>🔐 تسجيل الدخول</a>";
echo "<a href='/dashboard.php' class='bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700'>👤 حسابي</a>";
echo "<a href='/homepage-fixed.php?lang=ar' class='bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700'>🏠 الصفحة الرئيسية</a>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>

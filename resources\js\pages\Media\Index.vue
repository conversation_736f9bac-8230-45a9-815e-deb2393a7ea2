<script setup>
import { Head, Link, router } from '@inertiajs/vue3';
import { ref } from 'vue';
import LanguageSwitcher from '@/components/LanguageSwitcher.vue';

const props = defineProps({
    media: Object,
    types: Object,
    filters: Object,
    locale: String,
    translations: Object,
});

const t = (key) => {
    return props.translations?.app?.[key] || key;
};

const isRTL = props.locale === 'ar';

const selectedType = ref(props.filters.type || '');
const selectedCategory = ref(props.filters.category || '');

const filterMedia = () => {
    router.get(route('media.index'), {
        type: selectedType.value,
        category: selectedCategory.value,
    }, {
        preserveState: true,
        replace: true,
    });
};

const deleteMedia = (mediaId) => {
    if (confirm(t('confirm_delete'))) {
        router.delete(route('media.destroy', mediaId));
    }
};

const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};
</script>

<template>
    <Head :title="t('media_management')" />

    <div class="min-h-screen bg-gray-50" :dir="isRTL ? 'rtl' : 'ltr'">
        <!-- Fixed Navigation -->
        <nav class="fixed top-0 left-0 right-0 fixed-nav z-50">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <div class="flex items-center">
                        <Link href="/" class="text-2xl font-bold text-blue-600 hover:text-blue-700 transition-colors">
                            {{ t('site_name') }}
                        </Link>
                    </div>
                    <div class="flex items-center" :class="isRTL ? 'space-x-reverse space-x-4' : 'space-x-4'">
                        <LanguageSwitcher :current-locale="locale" />
                        <Link href="/dashboard" class="nav-link text-gray-600">{{ t('dashboard') }}</Link>
                        <Link href="/" class="nav-link text-gray-600">{{ t('view_homepage') }}</Link>
                    </div>
                </div>
            </div>
        </nav>

        <div class="py-12 pt-24">
            <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
                <!-- Header -->
                <div class="mb-8">
                    <div class="flex justify-between items-center">
                        <div>
                            <h1 class="text-3xl font-bold text-gray-900">{{ t('media_management') }}</h1>
                            <p class="text-gray-600 mt-2">{{ t('manage_all_images') }}</p>
                        </div>
                        <Link 
                            :href="route('media.create')"
                            class="btn-primary bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700"
                        >
                            {{ t('upload_new_media') }}
                        </Link>
                    </div>
                </div>

                <!-- Filters -->
                <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">{{ t('type') }}</label>
                            <select 
                                v-model="selectedType" 
                                @change="filterMedia"
                                class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                            >
                                <option value="">{{ t('all_types') }}</option>
                                <option v-for="(label, value) in types" :key="value" :value="value">
                                    {{ label }}
                                </option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">{{ t('category') }}</label>
                            <input 
                                v-model="selectedCategory" 
                                @input="filterMedia"
                                type="text" 
                                :placeholder="t('filter_by_category')"
                                class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                            />
                        </div>
                        <div class="flex items-end">
                            <button 
                                @click="selectedType = ''; selectedCategory = ''; filterMedia()"
                                class="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors"
                            >
                                {{ t('clear_filters') }}
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Media Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                    <div 
                        v-for="item in media.data" 
                        :key="item.id"
                        class="bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-lg transition-shadow"
                    >
                        <!-- Image -->
                        <div class="aspect-w-16 aspect-h-9 bg-gray-200">
                            <img 
                                :src="item.url" 
                                :alt="item.alt_text"
                                class="w-full h-48 object-cover"
                                @error="$event.target.src = 'https://via.placeholder.com/400x300/E5E7EB/9CA3AF?text=No+Image'"
                            />
                        </div>

                        <!-- Content -->
                        <div class="p-4">
                            <h3 class="font-semibold text-gray-900 mb-2 truncate">{{ item.name }}</h3>
                            
                            <!-- Type and Category -->
                            <div class="flex items-center gap-2 mb-2">
                                <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                                    {{ types[item.type] }}
                                </span>
                                <span v-if="item.category" class="px-2 py-1 bg-gray-100 text-gray-800 text-xs rounded-full">
                                    {{ item.category }}
                                </span>
                            </div>

                            <!-- File Info -->
                            <div class="text-sm text-gray-500 mb-3">
                                <div>{{ formatFileSize(item.file_size) }}</div>
                                <div>{{ item.mime_type }}</div>
                            </div>

                            <!-- Actions -->
                            <div class="flex items-center justify-between">
                                <div class="flex items-center gap-2">
                                    <Link 
                                        :href="route('media.edit', item.id)"
                                        class="text-blue-600 hover:text-blue-800 text-sm font-medium"
                                    >
                                        {{ t('edit') }}
                                    </Link>
                                    <Link 
                                        :href="route('media.show', item.id)"
                                        class="text-green-600 hover:text-green-800 text-sm font-medium"
                                    >
                                        {{ t('view') }}
                                    </Link>
                                </div>
                                <button 
                                    @click="deleteMedia(item.id)"
                                    class="text-red-600 hover:text-red-800 text-sm font-medium"
                                >
                                    {{ t('delete') }}
                                </button>
                            </div>

                            <!-- Status -->
                            <div class="mt-2">
                                <span 
                                    :class="item.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'"
                                    class="px-2 py-1 text-xs rounded-full"
                                >
                                    {{ item.is_active ? t('active') : t('inactive') }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Empty State -->
                <div v-if="!media.data.length" class="text-center py-12">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">{{ t('no_media_found') }}</h3>
                    <p class="mt-1 text-sm text-gray-500">{{ t('upload_first_media') }}</p>
                    <div class="mt-6">
                        <Link 
                            :href="route('media.create')"
                            class="btn-primary bg-blue-600 text-white px-4 py-2 rounded-lg font-semibold hover:bg-blue-700"
                        >
                            {{ t('upload_new_media') }}
                        </Link>
                    </div>
                </div>

                <!-- Pagination -->
                <div v-if="media.links && media.links.length > 3" class="mt-8">
                    <nav class="flex justify-center">
                        <div class="flex space-x-1">
                            <Link 
                                v-for="link in media.links" 
                                :key="link.label"
                                :href="link.url"
                                v-html="link.label"
                                :class="[
                                    'px-3 py-2 text-sm rounded-lg',
                                    link.active 
                                        ? 'bg-blue-600 text-white' 
                                        : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300'
                                ]"
                            />
                        </div>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</template>

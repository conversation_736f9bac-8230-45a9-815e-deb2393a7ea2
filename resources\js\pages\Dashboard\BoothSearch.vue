<template>
  <Head :title="t('search_booths', 'Search Booths') + ' - Dashboard'" />

  <div class="min-h-screen bg-gray-50" :dir="isRTL ? 'rtl' : 'ltr'">
    <!-- Navigation -->
    <nav class="bg-white shadow">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex items-center">
            <Link href="/" class="text-2xl font-bold text-blue-600">Season Expo</Link>
          </div>
          <div :class="isRTL ? 'flex items-center space-x-reverse space-x-4' : 'flex items-center space-x-4'">
            <Link href="/dashboard" class="text-gray-600 hover:text-gray-900">{{ t('dashboard', 'Dashboard') }}</Link>
            <Link href="/dashboard/reservations" class="text-gray-600 hover:text-gray-900">{{ t('my_reservations', 'My Reservations') }}</Link>
            <Link href="/logout" method="post" class="text-red-600 hover:text-red-800">{{ t('logout', 'Logout') }}</Link>
          </div>
        </div>
      </div>
    </nav>

    <div class="py-12">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
          <h1 class="text-3xl font-bold text-gray-900 mb-4">🔍 {{ t('search_booths', 'Search Exhibition Booths') }}</h1>
          <p class="text-gray-600">{{ t('find_perfect_booth', 'Find the perfect booth for your business at upcoming exhibitions') }}</p>
        </div>

        <!-- Search Filters -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
          <h2 class="text-lg font-semibold text-gray-900 mb-4">{{ t('search_filters', 'Search Filters') }}</h2>

          <form @submit.prevent="search" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <!-- Exhibition Filter -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">{{ t('exhibition', 'Exhibition') }}</label>
              <select v-model="searchForm.exhibition_id" class="w-full rounded-md border-gray-300">
                <option value="">{{ t('all_exhibitions', 'All Exhibitions') }}</option>
                <option v-for="exhibition in exhibitions" :key="exhibition.id" :value="exhibition.id">
                  {{ exhibition.title }} - {{ formatDate(exhibition.start_date) }}
                </option>
              </select>
            </div>

            <!-- Category Filter -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">{{ t('category', 'Category') }}</label>
              <select v-model="searchForm.category_id" class="w-full rounded-md border-gray-300">
                <option value="">{{ t('all_categories', 'All Categories') }}</option>
                <option v-for="category in categories" :key="category.id" :value="category.id">
                  {{ category.name }}
                </option>
              </select>
            </div>

            <!-- City Filter -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">{{ t('city', 'City') }}</label>
              <input
                v-model="searchForm.city"
                type="text"
                :placeholder="t('enter_city', 'Enter city name')"
                class="w-full rounded-md border-gray-300"
              />
            </div>

            <!-- Price Range -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">{{ t('min_price', 'Min Price (KWD)') }}</label>
              <input
                v-model="searchForm.min_price"
                type="number"
                placeholder="0"
                class="w-full rounded-md border-gray-300"
              />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">{{ t('max_price', 'Max Price (KWD)') }}</label>
              <input
                v-model="searchForm.max_price"
                type="number"
                placeholder="10000"
                class="w-full rounded-md border-gray-300"
              />
            </div>

            <!-- Minimum Size -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">{{ t('min_size', 'Min Size (sqm)') }}</label>
              <input
                v-model="searchForm.min_size"
                type="number"
                placeholder="0"
                class="w-full rounded-md border-gray-300"
              />
            </div>

            <!-- Date Range -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">{{ t('start_date', 'Start Date') }}</label>
              <input
                v-model="searchForm.start_date"
                type="date"
                class="w-full rounded-md border-gray-300"
              />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">{{ t('end_date', 'End Date') }}</label>
              <input
                v-model="searchForm.end_date"
                type="date"
                class="w-full rounded-md border-gray-300"
              />
            </div>

            <!-- Sort Options -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">{{ t('sort_by', 'Sort By') }}</label>
              <select v-model="searchForm.sort" class="w-full rounded-md border-gray-300">
                <option value="price">{{ t('price', 'Price') }}</option>
                <option value="size">{{ t('size', 'Size') }}</option>
                <option value="booth_number">{{ t('booth_number', 'Booth Number') }}</option>
                <option value="exhibition_date">{{ t('exhibition_date', 'Exhibition Date') }}</option>
              </select>
            </div>

            <!-- Search Button -->
            <div class="flex items-end">
              <button
                type="submit"
                class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
              >
                🔍 {{ t('search_booths_btn', 'Search Booths') }}
              </button>
            </div>

            <!-- Clear Filters -->
            <div class="flex items-end">
              <button
                type="button"
                @click="clearFilters"
                class="w-full bg-gray-200 text-gray-800 py-2 px-4 rounded-md hover:bg-gray-300 transition-colors"
              >
                {{ t('clear_filters', 'Clear Filters') }}
              </button>
            </div>
          </form>
        </div>

        <!-- Results Summary -->
        <div class="bg-white rounded-lg shadow-md p-4 mb-6">
          <div :class="isRTL ? 'flex flex-row-reverse justify-between items-center' : 'flex justify-between items-center'">
            <div class="text-sm text-gray-600">
              {{ t('showing', 'Showing') }} {{ booths.from || 0 }} {{ t('to', 'to') }} {{ booths.to || 0 }} {{ t('of', 'of') }} {{ booths.total || 0 }} {{ t('booths', 'booths') }}
            </div>
            <div :class="isRTL ? 'flex items-center space-x-reverse space-x-2' : 'flex items-center space-x-2'">
              <label class="text-sm text-gray-600">{{ t('order', 'Order') }}:</label>
              <select v-model="searchForm.order" @change="search" class="text-sm rounded border-gray-300">
                <option value="asc">{{ t('low_to_high', 'Low to High') }}</option>
                <option value="desc">{{ t('high_to_low', 'High to Low') }}</option>
              </select>
            </div>
          </div>
        </div>

        <!-- Booth Results -->
        <div v-if="booths.data && booths.data.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          <div
            v-for="booth in booths.data"
            :key="booth.id"
            class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow"
          >
            <div class="p-6">
              <!-- Booth Header -->
              <div :class="isRTL ? 'flex flex-row-reverse justify-between items-start mb-4' : 'flex justify-between items-start mb-4'">
                <div>
                  <h3 class="text-lg font-semibold text-gray-900">{{ t('booth', 'Booth') }} #{{ booth.booth_number }}</h3>
                  <p class="text-sm text-gray-600">{{ booth.exhibition.title }}</p>
                </div>
                <div :class="isRTL ? 'text-left' : 'text-right'">
                  <div class="text-lg font-bold text-green-600">د.ك {{ formatKWD(booth.price) }}</div>
                  <div class="text-xs text-gray-500">{{ booth.size }} {{ t('sqm', 'sqm') }}</div>
                </div>
              </div>

              <!-- Exhibition Info -->
              <div class="space-y-2 mb-4">
                <div class="flex items-center text-sm text-gray-600">
                  <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd" />
                  </svg>
                  {{ booth.exhibition.city }}, {{ booth.exhibition.country }}
                </div>
                <div class="flex items-center text-sm text-gray-600">
                  <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd" />
                  </svg>
                  {{ formatDateRange(booth.exhibition.start_date, booth.exhibition.end_date) }}
                </div>
                <div class="flex items-center text-sm text-gray-600">
                  <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  {{ booth.exhibition.category?.name || 'General' }}
                </div>
              </div>

              <!-- Booth Details -->
              <div class="bg-gray-50 rounded-lg p-3 mb-4">
                <div class="grid grid-cols-2 gap-2 text-sm">
                  <div>
                    <span class="font-medium">{{ t('size', 'Size') }}:</span> {{ booth.size }} {{ t('sqm', 'sqm') }}
                  </div>
                  <div>
                    <span class="font-medium">{{ t('type', 'Type') }}:</span> {{ t(booth.type?.toLowerCase() || 'standard', booth.type || 'Standard') }}
                  </div>
                  <div>
                    <span class="font-medium">{{ t('location', 'Location') }}:</span> {{ t((booth.location || 'main_hall').toLowerCase().replace(' ', '_'), booth.location || 'Main Hall') }}
                  </div>
                  <div>
                    <span class="font-medium">{{ t('status', 'Status') }}:</span>
                    <span class="text-green-600 font-medium">{{ t('available', 'Available') }}</span>
                  </div>
                </div>
              </div>

              <!-- Action Buttons -->
              <div :class="isRTL ? 'flex space-x-reverse space-x-2' : 'flex space-x-2'">
                <Link
                  :href="`/exhibitions/${booth.exhibition.slug}/booths/${booth.id}`"
                  class="flex-1 bg-blue-600 text-white text-center py-2 px-4 rounded-md hover:bg-blue-700 transition-colors text-sm"
                >
                  {{ t('view_details', 'View Details') }}
                </Link>
                <button
                  @click="bookBooth(booth)"
                  class="flex-1 bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 transition-colors text-sm"
                >
                  {{ t('book_now', 'Book Now') }}
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- No Results -->
        <div v-else class="text-center py-12">
          <div class="text-gray-400 text-6xl mb-4">🔍</div>
          <h3 class="text-xl font-semibold text-gray-900 mb-2">{{ t('no_booths_found', 'No booths found') }}</h3>
          <p class="text-gray-600 mb-4">{{ t('adjust_search', 'Try adjusting your search criteria to find available booths') }}</p>
          <button
            @click="clearFilters"
            class="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 transition-colors"
          >
            {{ t('clear_all_filters', 'Clear All Filters') }}
          </button>
        </div>

        <!-- Pagination -->
        <div v-if="booths.links && booths.links.length > 3" class="flex justify-center">
          <nav class="flex space-x-2">
            <Link
              v-for="link in booths.links"
              :key="link.label"
              :href="link.url"
              :class="[
                'px-3 py-2 rounded-md text-sm font-medium',
                link.active
                  ? 'bg-blue-600 text-white'
                  : link.url
                    ? 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300'
                    : 'bg-gray-100 text-gray-400 cursor-not-allowed'
              ]"
            >
              {{ link.label }}
            </Link>
          </nav>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { Head, Link, router } from '@inertiajs/vue3'
import { ref, reactive } from 'vue'

const props = defineProps({
  booths: Object,
  exhibitions: Array,
  categories: Array,
  filters: Object,
  locale: {
    type: String,
    default: 'ar'
  }
})

// Translation helper
const t = (key, fallback) => {
  const translations = {
    // Navigation
    'dashboard': 'لوحة التحكم',
    'my_reservations': 'حجوزاتي',
    'logout': 'تسجيل الخروج',

    // Page titles
    'search_booths': 'البحث عن الأجنحة',
    'find_perfect_booth': 'اعثر على الجناح المثالي لعملك في المعارض القادمة',

    // Search filters
    'search_filters': 'مرشحات البحث',
    'exhibition': 'المعرض',
    'all_exhibitions': 'جميع المعارض',
    'category': 'الفئة',
    'all_categories': 'جميع الفئات',
    'city': 'المدينة',
    'enter_city': 'أدخل اسم المدينة',
    'min_price': 'الحد الأدنى للسعر (د.ك)',
    'max_price': 'الحد الأقصى للسعر (د.ك)',
    'min_size': 'الحد الأدنى للمساحة (متر مربع)',
    'start_date': 'تاريخ البداية',
    'end_date': 'تاريخ النهاية',
    'sort_by': 'ترتيب حسب',
    'price': 'السعر',
    'size': 'المساحة',
    'booth_number': 'رقم الجناح',
    'exhibition_date': 'تاريخ المعرض',
    'search_booths_btn': 'البحث عن الأجنحة',
    'clear_filters': 'مسح المرشحات',

    // Results
    'showing': 'عرض',
    'to': 'إلى',
    'of': 'من',
    'booths': 'جناح',
    'order': 'الترتيب',
    'low_to_high': 'من الأقل إلى الأعلى',
    'high_to_low': 'من الأعلى إلى الأقل',

    // Booth details
    'booth': 'الجناح',
    'type': 'النوع',
    'location': 'الموقع',
    'status': 'الحالة',
    'available': 'متاح',
    'standard': 'عادي',
    'main_hall': 'القاعة الرئيسية',
    'view_details': 'عرض التفاصيل',
    'book_now': 'احجز الآن',

    // No results
    'no_booths_found': 'لم يتم العثور على أجنحة',
    'adjust_search': 'جرب تعديل معايير البحث للعثور على الأجنحة المتاحة',
    'clear_all_filters': 'مسح جميع المرشحات'
  };

  return props.locale === 'ar' ? (translations[key] || fallback) : fallback;
};

const isRTL = props.locale === 'ar';

// Search form
const searchForm = reactive({
  exhibition_id: props.filters.exhibition_id || '',
  category_id: props.filters.category_id || '',
  min_price: props.filters.min_price || '',
  max_price: props.filters.max_price || '',
  min_size: props.filters.min_size || '',
  city: props.filters.city || '',
  start_date: props.filters.start_date || '',
  end_date: props.filters.end_date || '',
  sort: props.filters.sort || 'price',
  order: props.filters.order || 'asc'
})

// Methods
const search = () => {
  router.get('/dashboard/booth-search', searchForm, {
    preserveState: true,
    preserveScroll: true
  })
}

const clearFilters = () => {
  Object.keys(searchForm).forEach(key => {
    if (key === 'sort') {
      searchForm[key] = 'price'
    } else if (key === 'order') {
      searchForm[key] = 'asc'
    } else {
      searchForm[key] = ''
    }
  })
  search()
}

const bookBooth = (booth) => {
  router.post(`/exhibitions/${booth.exhibition.slug}/booths/${booth.id}/book`, {}, {
    onSuccess: () => {
      alert('Booth booking initiated! Please complete the payment process.')
    },
    onError: (errors) => {
      alert('Error booking booth: ' + Object.values(errors).join(', '))
    }
  })
}

const formatKWD = (amount) => {
  return parseFloat(amount).toFixed(3)
}

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

const formatDateRange = (startDate, endDate) => {
  const start = formatDate(startDate)
  const end = formatDate(endDate)
  return start === end ? start : `${start} - ${end}`
}
</script>

<?php
// Admin Slider Management - Complete Interface

try {
    $pdo = new PDO('sqlite:' . __DIR__ . '/database/database.sqlite');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Handle form submissions
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'add':
                    $stmt = $pdo->prepare("
                        INSERT INTO slider_images (title, description, image_url, button_text, button_link, background_color, sort_order, is_active)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    ");
                    $stmt->execute([
                        $_POST['title'],
                        $_POST['description'],
                        $_POST['image_url'],
                        $_POST['button_text'],
                        $_POST['button_link'],
                        $_POST['background_color'],
                        (int)$_POST['sort_order'],
                        isset($_POST['is_active']) ? 1 : 0
                    ]);
                    $message = "تم إضافة السلايد بنجاح!";
                    break;
                    
                case 'edit':
                    $stmt = $pdo->prepare("
                        UPDATE slider_images 
                        SET title = ?, description = ?, image_url = ?, button_text = ?, button_link = ?, 
                            background_color = ?, sort_order = ?, is_active = ?, updated_at = CURRENT_TIMESTAMP
                        WHERE id = ?
                    ");
                    $stmt->execute([
                        $_POST['title'],
                        $_POST['description'],
                        $_POST['image_url'],
                        $_POST['button_text'],
                        $_POST['button_link'],
                        $_POST['background_color'],
                        (int)$_POST['sort_order'],
                        isset($_POST['is_active']) ? 1 : 0,
                        (int)$_POST['id']
                    ]);
                    $message = "تم تحديث السلايد بنجاح!";
                    break;
                    
                case 'delete':
                    $stmt = $pdo->prepare("DELETE FROM slider_images WHERE id = ?");
                    $stmt->execute([(int)$_POST['id']]);
                    $message = "تم حذف السلايد بنجاح!";
                    break;
                    
                case 'toggle':
                    $stmt = $pdo->prepare("UPDATE slider_images SET is_active = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?");
                    $stmt->execute([
                        $_POST['is_active'] === 'true' ? 1 : 0,
                        (int)$_POST['id']
                    ]);
                    $message = "تم تحديث حالة السلايد بنجاح!";
                    break;
            }
        }
    }
    
    // Get all slides
    $stmt = $pdo->query("SELECT * FROM slider_images ORDER BY sort_order ASC, id ASC");
    $slides = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    $error = "خطأ في قاعدة البيانات: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة السلايد - Season Expo</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .modal { display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; }
        .modal.active { display: flex; align-items: center; justify-content: center; }
        .modal-content { background: white; padding: 20px; border-radius: 8px; max-width: 600px; width: 90%; max-height: 90vh; overflow-y: auto; }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-blue-600 text-white shadow-lg">
        <div class="container mx-auto px-4 py-4">
            <div class="flex justify-between items-center">
                <h1 class="text-2xl font-bold">🖼️ إدارة السلايد</h1>
                <nav class="flex items-center space-x-reverse space-x-4">
                    <a href="/admin/exhibitions-management" class="hover:text-blue-200">إدارة المعارض</a>
                    <a href="/dashboard" class="hover:text-blue-200">لوحة التحكم</a>
                    <a href="/" class="hover:text-blue-200">الصفحة الرئيسية</a>
                </nav>
            </div>
        </div>
    </header>

    <div class="container mx-auto px-4 py-8">
        <!-- Messages -->
        <?php if (isset($message)): ?>
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
                <?= htmlspecialchars($message) ?>
            </div>
        <?php endif; ?>
        
        <?php if (isset($error)): ?>
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
                <?= htmlspecialchars($error) ?>
            </div>
        <?php endif; ?>

        <!-- Action Buttons -->
        <div class="mb-6 flex space-x-reverse space-x-4">
            <button onclick="openAddModal()" class="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 flex items-center">
                <span class="text-lg mr-2">➕</span>
                إضافة سلايد جديد
            </button>
            <a href="/" target="_blank" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 flex items-center">
                <span class="text-lg mr-2">👁️</span>
                معاينة الصفحة الرئيسية
            </a>
            <button onclick="reorderSlides()" class="bg-purple-600 text-white px-6 py-3 rounded-lg hover:bg-purple-700 flex items-center">
                <span class="text-lg mr-2">🔄</span>
                إعادة ترتيب
            </button>
        </div>

        <!-- Slides List -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="p-6">
                <h2 class="text-xl font-bold mb-4">السلايدات الحالية (<?= count($slides) ?>)</h2>
                
                <?php if (empty($slides)): ?>
                    <div class="text-center py-8 text-gray-500">
                        <p class="text-lg">لا توجد سلايدات حالياً</p>
                        <p>اضغط على "إضافة سلايد جديد" لإنشاء أول سلايد</p>
                    </div>
                <?php else: ?>
                    <div class="overflow-x-auto">
                        <table class="w-full border-collapse border border-gray-300">
                            <thead>
                                <tr class="bg-gray-50">
                                    <th class="border border-gray-300 p-3 text-right">الترتيب</th>
                                    <th class="border border-gray-300 p-3 text-right">العنوان</th>
                                    <th class="border border-gray-300 p-3 text-right">الصورة</th>
                                    <th class="border border-gray-300 p-3 text-right">اللون</th>
                                    <th class="border border-gray-300 p-3 text-right">الحالة</th>
                                    <th class="border border-gray-300 p-3 text-right">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($slides as $slide): ?>
                                    <tr class="hover:bg-gray-50">
                                        <td class="border border-gray-300 p-3 text-center font-bold"><?= $slide['sort_order'] ?></td>
                                        <td class="border border-gray-300 p-3">
                                            <div class="font-semibold"><?= htmlspecialchars($slide['title']) ?></div>
                                            <div class="text-sm text-gray-600 mt-1">
                                                <?= htmlspecialchars(substr($slide['description'], 0, 100)) ?>...
                                            </div>
                                        </td>
                                        <td class="border border-gray-300 p-3">
                                            <div class="flex items-center">
                                                <img src="<?= htmlspecialchars($slide['image_url']) ?>" 
                                                     alt="<?= htmlspecialchars($slide['title']) ?>" 
                                                     class="w-20 h-12 object-cover rounded border"
                                                     onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                                                <div style="display:none; width:80px; height:48px; background:<?= $slide['background_color'] ?>; border-radius:4px; display:flex; align-items:center; justify-content:center; color:white; font-size:12px; border: 1px solid #ccc;">
                                                    صورة
                                                </div>
                                            </div>
                                        </td>
                                        <td class="border border-gray-300 p-3">
                                            <div class="flex items-center">
                                                <div class="w-8 h-8 rounded border border-gray-300 mr-2" 
                                                     style="background-color: <?= $slide['background_color'] ?>;"></div>
                                                <span class="text-sm font-mono"><?= $slide['background_color'] ?></span>
                                            </div>
                                        </td>
                                        <td class="border border-gray-300 p-3 text-center">
                                            <?php if ($slide['is_active']): ?>
                                                <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">نشط</span>
                                            <?php else: ?>
                                                <span class="bg-red-100 text-red-800 px-3 py-1 rounded-full text-sm font-medium">غير نشط</span>
                                            <?php endif; ?>
                                        </td>
                                        <td class="border border-gray-300 p-3">
                                            <div class="flex flex-wrap gap-1">
                                                <button onclick="editSlide(<?= htmlspecialchars(json_encode($slide)) ?>)" 
                                                        class="bg-blue-500 text-white px-2 py-1 rounded text-xs hover:bg-blue-600">
                                                    تعديل
                                                </button>
                                                <button onclick="toggleSlide(<?= $slide['id'] ?>, <?= $slide['is_active'] ? 'false' : 'true' ?>)" 
                                                        class="bg-yellow-500 text-white px-2 py-1 rounded text-xs hover:bg-yellow-600">
                                                    <?= $slide['is_active'] ? 'إخفاء' : 'إظهار' ?>
                                                </button>
                                                <button onclick="deleteSlide(<?= $slide['id'] ?>)" 
                                                        class="bg-red-500 text-white px-2 py-1 rounded text-xs hover:bg-red-600">
                                                    حذف
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Add/Edit Modal -->
    <div id="slideModal" class="modal">
        <div class="modal-content">
            <h3 id="modalTitle" class="text-xl font-bold mb-4">إضافة سلايد جديد</h3>
            <form id="slideForm" method="POST">
                <input type="hidden" name="action" id="formAction" value="add">
                <input type="hidden" name="id" id="slideId">
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-2">العنوان *</label>
                        <input type="text" name="title" id="slideTitle" required 
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                    </div>
                    
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-2">الوصف *</label>
                        <textarea name="description" id="slideDescription" required rows="3"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                                  placeholder="مثال:&#10;أحدث التقنيات والابتكارات التكنولوجية&#10;📅 15-20 مارس 2025 | 📍 مركز المعارض الدولي"></textarea>
                    </div>
                    
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-2">رابط الصورة *</label>
                        <input type="url" name="image_url" id="slideImageUrl" required 
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                               placeholder="https://example.com/image.jpg">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">نص الزر</label>
                        <input type="text" name="button_text" id="slideButtonText" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                               placeholder="عرض التفاصيل">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">رابط الزر</label>
                        <input type="text" name="button_link" id="slideButtonLink" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                               placeholder="/exhibitions/1">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">لون الخلفية *</label>
                        <input type="color" name="background_color" id="slideBackgroundColor" required 
                               class="w-full h-10 border border-gray-300 rounded-lg">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">ترتيب العرض *</label>
                        <input type="number" name="sort_order" id="slideSortOrder" required min="1" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                    </div>
                    
                    <div class="md:col-span-2">
                        <label class="flex items-center">
                            <input type="checkbox" name="is_active" id="slideIsActive" checked 
                                   class="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <span class="text-sm font-medium text-gray-700">نشط (يظهر في الصفحة الرئيسية)</span>
                        </label>
                    </div>
                </div>
                
                <div class="flex justify-end space-x-reverse space-x-4 mt-6">
                    <button type="button" onclick="closeModal()" 
                            class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
                        إلغاء
                    </button>
                    <button type="submit" 
                            class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                        حفظ
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        function openAddModal() {
            document.getElementById('modalTitle').textContent = 'إضافة سلايد جديد';
            document.getElementById('formAction').value = 'add';
            document.getElementById('slideForm').reset();
            document.getElementById('slideIsActive').checked = true;
            document.getElementById('slideBackgroundColor').value = '#1e40af';
            document.getElementById('slideSortOrder').value = <?= count($slides) + 1 ?>;
            document.getElementById('slideModal').classList.add('active');
        }

        function editSlide(slide) {
            document.getElementById('modalTitle').textContent = 'تعديل السلايد';
            document.getElementById('formAction').value = 'edit';
            document.getElementById('slideId').value = slide.id;
            document.getElementById('slideTitle').value = slide.title;
            document.getElementById('slideDescription').value = slide.description;
            document.getElementById('slideImageUrl').value = slide.image_url;
            document.getElementById('slideButtonText').value = slide.button_text || '';
            document.getElementById('slideButtonLink').value = slide.button_link || '';
            document.getElementById('slideBackgroundColor').value = slide.background_color;
            document.getElementById('slideSortOrder').value = slide.sort_order;
            document.getElementById('slideIsActive').checked = slide.is_active == 1;
            document.getElementById('slideModal').classList.add('active');
        }

        function closeModal() {
            document.getElementById('slideModal').classList.remove('active');
        }

        function deleteSlide(id) {
            if (confirm('هل أنت متأكد من حذف هذا السلايد؟')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="delete">
                    <input type="hidden" name="id" value="${id}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }

        function toggleSlide(id, isActive) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.innerHTML = `
                <input type="hidden" name="action" value="toggle">
                <input type="hidden" name="id" value="${id}">
                <input type="hidden" name="is_active" value="${isActive}">
            `;
            document.body.appendChild(form);
            form.submit();
        }

        function reorderSlides() {
            alert('ميزة إعادة الترتيب ستكون متاحة قريباً. يمكنك حالياً تعديل رقم الترتيب لكل سلايد.');
        }

        // Close modal when clicking outside
        document.getElementById('slideModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });
    </script>
</body>
</html>

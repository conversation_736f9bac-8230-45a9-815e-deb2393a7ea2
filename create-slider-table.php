<?php
// Create slider images table

try {
    $pdo = new PDO('sqlite:' . __DIR__ . '/database/database.sqlite');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Create slider_images table
    $sql = "CREATE TABLE IF NOT EXISTS slider_images (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        image_url VARCHAR(500) NOT NULL,
        button_text VARCHAR(100),
        button_link VARCHAR(500),
        background_color VARCHAR(20) DEFAULT '#1e40af',
        text_color VARCHAR(20) DEFAULT '#ffffff',
        is_active BOOLEAN DEFAULT 1,
        sort_order INTEGER DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    
    $pdo->exec($sql);
    echo "<p style='color: green;'>✅ تم إنشاء جدول slider_images بنجاح</p>";
    
    // Check if table has data, if not add sample data
    $stmt = $pdo->query("SELECT COUNT(*) FROM slider_images");
    $count = $stmt->fetchColumn();
    
    if ($count == 0) {
        echo "<p>إضافة بيانات تجريبية...</p>";
        
        $sampleData = [
            [
                'title' => 'معرض التكنولوجيا 2025',
                'description' => 'أحدث التقنيات والابتكارات التكنولوجية\n📅 15-20 مارس 2025 | 📍 مركز المعارض الدولي',
                'image_url' => 'https://picsum.photos/1200/600?random=tech1',
                'button_text' => 'عرض التفاصيل',
                'button_link' => '/exhibitions/1',
                'background_color' => '#1e40af',
                'sort_order' => 1
            ],
            [
                'title' => 'معرض الصحة والجمال',
                'description' => 'منتجات العناية والصحة والجمال\n📅 22-27 أبريل 2025 | 📍 مركز الكويت التجاري',
                'image_url' => 'https://picsum.photos/1200/600?random=health2',
                'button_text' => 'عرض التفاصيل',
                'button_link' => '/exhibitions/2',
                'background_color' => '#059669',
                'sort_order' => 2
            ],
            [
                'title' => 'معرض الأزياء والموضة',
                'description' => 'أحدث صيحات الموضة والأزياء\n📅 10-15 مايو 2025 | 📍 مجمع الأفنيوز',
                'image_url' => 'https://picsum.photos/1200/600?random=fashion3',
                'button_text' => 'عرض التفاصيل',
                'button_link' => '/exhibitions/3',
                'background_color' => '#7c3aed',
                'sort_order' => 3
            ],
            [
                'title' => 'معرض الطعام والمشروبات',
                'description' => 'أشهى المأكولات والمشروبات\n📅 5-10 يونيو 2025 | 📍 مركز الشيخ جابر الثقافي',
                'image_url' => 'https://picsum.photos/1200/600?random=food4',
                'button_text' => 'عرض التفاصيل',
                'button_link' => '/exhibitions/4',
                'background_color' => '#ea580c',
                'sort_order' => 4
            ]
        ];
        
        $insertStmt = $pdo->prepare("
            INSERT INTO slider_images (title, description, image_url, button_text, button_link, background_color, sort_order)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ");
        
        foreach ($sampleData as $slide) {
            $insertStmt->execute([
                $slide['title'],
                $slide['description'],
                $slide['image_url'],
                $slide['button_text'],
                $slide['button_link'],
                $slide['background_color'],
                $slide['sort_order']
            ]);
        }
        
        echo "<p style='color: green;'>✅ تم إضافة " . count($sampleData) . " سلايد تجريبي</p>";
    } else {
        echo "<p style='color: blue;'>ℹ️ الجدول يحتوي على $count سلايد بالفعل</p>";
    }
    
    // Show current slides
    $stmt = $pdo->query("SELECT * FROM slider_images ORDER BY sort_order ASC");
    $slides = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h2>السلايدات الحالية:</h2>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='padding: 10px;'>الترتيب</th>";
    echo "<th style='padding: 10px;'>العنوان</th>";
    echo "<th style='padding: 10px;'>الوصف</th>";
    echo "<th style='padding: 10px;'>الصورة</th>";
    echo "<th style='padding: 10px;'>اللون</th>";
    echo "<th style='padding: 10px;'>نشط</th>";
    echo "</tr>";
    
    foreach ($slides as $slide) {
        echo "<tr>";
        echo "<td style='padding: 10px; text-align: center;'>{$slide['sort_order']}</td>";
        echo "<td style='padding: 10px;'>{$slide['title']}</td>";
        echo "<td style='padding: 10px;'>" . nl2br(substr($slide['description'], 0, 100)) . "...</td>";
        echo "<td style='padding: 10px;'><a href='{$slide['image_url']}' target='_blank'>عرض الصورة</a></td>";
        echo "<td style='padding: 10px;'><span style='display: inline-block; width: 30px; height: 20px; background: {$slide['background_color']}; border: 1px solid #ccc;'></span> {$slide['background_color']}</td>";
        echo "<td style='padding: 10px; text-align: center;'>" . ($slide['is_active'] ? '✅' : '❌') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h2>الخطوات التالية:</h2>";
    echo "<ol>";
    echo "<li>تم إنشاء جدول قاعدة البيانات بنجاح</li>";
    echo "<li>تم إضافة بيانات تجريبية</li>";
    echo "<li>الآن سيتم إنشاء لوحة تحكم الأدمن لإدارة السلايدات</li>";
    echo "<li>سيتم تحديث الصفحة الرئيسية لتقرأ من قاعدة البيانات</li>";
    echo "</ol>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
}
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h1, h2 {
    color: #333;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
}

table {
    font-size: 14px;
}

th {
    background-color: #f8f9fa !important;
}

td, th {
    border: 1px solid #ddd;
}
</style>

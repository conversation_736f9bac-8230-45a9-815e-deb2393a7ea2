<?php
echo "<h1>Check Season Expo Installation</h1>";

echo "<h2>1. Critical Files Check:</h2>";

$criticalFiles = [
    'routes/web.php' => 'Main routes file',
    'app/Http/Controllers/HomeController.php' => 'Home controller',
    'app/Http/Controllers/ExhibitionController.php' => 'Exhibition controller',
    'app/Models/Exhibition.php' => 'Exhibition model',
    'app/Models/Category.php' => 'Category model',
    'resources/js/Pages/Welcome.vue' => 'Homepage component',
    '.env' => 'Environment configuration'
];

$missingFiles = [];
foreach ($criticalFiles as $file => $description) {
    $fullPath = __DIR__ . '/' . $file;
    if (file_exists($fullPath)) {
        $size = filesize($fullPath);
        echo "✅ {$file} - {$description} ({$size} bytes)<br>";
    } else {
        echo "❌ {$file} - {$description} MISSING<br>";
        $missingFiles[] = $file;
    }
}

echo "<h2>2. Routes File Analysis:</h2>";

$routesFile = __DIR__ . '/routes/web.php';
if (file_exists($routesFile)) {
    echo "✅ routes/web.php exists<br>";
    echo "Size: " . filesize($routesFile) . " bytes<br>";
    
    $content = file_get_contents($routesFile);
    echo "<h3>Routes Content Preview:</h3>";
    echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 5px; max-height: 300px; overflow-y: auto;'>";
    echo htmlspecialchars(substr($content, 0, 1000));
    if (strlen($content) > 1000) {
        echo "\n... (truncated)";
    }
    echo "</pre>";
    
    // Check for Season Expo routes
    if (strpos($content, 'HomeController') !== false) {
        echo "✅ HomeController found in routes<br>";
    } else {
        echo "❌ HomeController NOT found in routes<br>";
    }
    
    if (strpos($content, "Route::get('/', ") !== false || strpos($content, 'Route::get("/", ') !== false) {
        echo "✅ Home route (/) found<br>";
    } else {
        echo "❌ Home route (/) NOT found<br>";
    }
    
} else {
    echo "❌ routes/web.php missing!<br>";
}

echo "<h2>3. Laravel Routes Test:</h2>";

try {
    require __DIR__ . '/vendor/autoload.php';
    $app = require_once __DIR__ . '/bootstrap/app.php';
    
    // Get all routes
    $routes = \Illuminate\Support\Facades\Route::getRoutes();
    echo "✅ Total routes loaded: " . count($routes) . "<br>";
    
    // Check for home route
    $homeRoute = null;
    $seasonExpoRoutes = 0;
    
    foreach ($routes as $route) {
        $uri = $route->uri();
        $action = $route->getActionName();
        
        if ($uri === '/') {
            $homeRoute = $route;
        }
        
        if (strpos($action, 'HomeController') !== false || 
            strpos($action, 'ExhibitionController') !== false) {
            $seasonExpoRoutes++;
        }
    }
    
    if ($homeRoute) {
        echo "✅ Home route found: " . $homeRoute->getActionName() . "<br>";
    } else {
        echo "❌ Home route NOT found - this is why you see Laravel page<br>";
    }
    
    echo "✅ Season Expo routes found: {$seasonExpoRoutes}<br>";
    
} catch (Exception $e) {
    echo "❌ Error loading routes: " . $e->getMessage() . "<br>";
}

echo "<h2>4. Database Test:</h2>";

try {
    $exhibitions = \App\Models\Exhibition::count();
    echo "✅ Exhibitions in database: {$exhibitions}<br>";
    
    $categories = \App\Models\Category::count();
    echo "✅ Categories in database: {$categories}<br>";
    
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "<br>";
}

echo "<h2>5. Diagnosis:</h2>";

if (!empty($missingFiles)) {
    echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px;'>";
    echo "<h3>❌ Missing Files Found</h3>";
    echo "<p>These critical files are missing:</p>";
    echo "<ul>";
    foreach ($missingFiles as $file) {
        echo "<li>{$file}</li>";
    }
    echo "</ul>";
    echo "<p><strong>Action:</strong> Upload these files to public_html/</p>";
    echo "</div>";
} elseif (!$homeRoute) {
    echo "<div style='background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; border-radius: 5px;'>";
    echo "<h3>⚠️ Routes Not Loading</h3>";
    echo "<p>Files exist but routes aren't loading properly.</p>";
    echo "<p><strong>Action:</strong> <a href='fix-routes.php'>Fix Routes Loading</a></p>";
    echo "</div>";
} else {
    echo "<div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px;'>";
    echo "<h3>✅ Season Expo Should Be Working</h3>";
    echo "<p><a href='/'>Test Your Application</a></p>";
    echo "</div>";
}

echo "<h2>6. Next Steps:</h2>";
echo "<ul>";
echo "<li><a href='fix-routes.php'>Fix Routes Loading</a></li>";
echo "<li><a href='clear-caches.php'>Clear All Caches</a></li>";
echo "<li><a href='show-current-routes.php'>Show Current Routes</a></li>";
echo "</ul>";
?>

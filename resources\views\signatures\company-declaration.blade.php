<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>إقرار وتعهد الشركات المشاركة - Season Expo</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.8;
        }
        .declaration-form {
            background: white;
            border: 2px solid #0066cc;
            border-radius: 10px;
            padding: 30px;
            margin: 20px 0;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .form-header {
            text-align: center;
            border-bottom: 2px solid #0066cc;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .form-title {
            font-size: 24px;
            font-weight: bold;
            color: #0066cc;
            margin-bottom: 10px;
        }
        .form-subtitle {
            font-size: 18px;
            color: #333;
            margin-bottom: 5px;
        }
        .declaration-text {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            line-height: 2;
            text-align: justify;
        }
        .form-field {
            margin: 15px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .form-field label {
            font-weight: bold;
            color: #333;
            display: block;
            margin-bottom: 5px;
        }
        .form-field input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 14px;
        }
        .signature-section {
            background: #e8f4fd;
            border: 2px solid #0066cc;
            border-radius: 10px;
            padding: 20px;
            margin: 30px 0;
            text-align: center;
        }
        #signature-canvas {
            border: 2px dashed #0066cc;
            cursor: crosshair;
            background: white;
            border-radius: 5px;
        }
        #signature-canvas:hover {
            border-color: #004499;
        }
        .signature-buttons {
            margin: 15px 0;
            display: flex;
            gap: 10px;
            justify-content: center;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s;
        }
        .btn-primary {
            background: #0066cc;
            color: white;
        }
        .btn-primary:hover {
            background: #004499;
        }
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        .btn-secondary:hover {
            background: #545b62;
        }
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        .btn-danger:hover {
            background: #c82333;
        }
        .checkbox-section {
            background: #fff3cd;
            border: 2px solid #ffc107;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .checkbox-item {
            display: flex;
            align-items: center;
            margin: 10px 0;
        }
        .checkbox-item input[type="checkbox"] {
            margin-left: 10px;
            transform: scale(1.3);
        }
        .checkbox-item label {
            font-weight: 500;
            cursor: pointer;
        }
    </style>
</head>
<body class="min-h-screen bg-gray-50">
    <!-- Fixed Navigation -->
    <nav class="fixed top-0 left-0 right-0 bg-white shadow-sm z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="/" class="text-2xl font-bold text-blue-600 hover:text-blue-700">Season Expo</a>
                </div>
                <div class="flex items-center space-x-reverse space-x-4">
                    <a href="/signatures" class="text-gray-600 hover:text-gray-900">التوقيعات الرقمية</a>
                    <a href="/dashboard" class="text-gray-600 hover:text-gray-900">لوحة التحكم</a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="pt-16 min-h-screen">
        <div class="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">

            <!-- Company Declaration Form -->
            <div class="declaration-form">
                <div class="form-header">
                    <div class="form-title">إقرار وتعهد</div>
                    <div class="form-subtitle">الشركات المشاركة</div>
                </div>

                <form method="POST" action="/signatures/store-company-declaration" id="declarationForm">
                    @csrf

                    <!-- Declaration Text -->
                    <div class="declaration-text">
                        <p><strong>أتعهد أنا الموقع أدناه</strong></p>
                        <p><strong>المشارك فى معرض</strong></p>
                        <p><strong>المزمع إقامته خلال الفترة</strong> ..</p>

                        <p>بأن ألتزم بكل ما تم ذكره في القرار الوزاري رقم ( 303) لسنة 2018 بشأن القواعد العامة لتنظيم إقامة المعارض التجارية المؤقتة بدولة الكويت، وأقر بصحة كل ماورد فى كشف السلع والخدمات الخاص بي والمقدم من المنظم، وأتحمل كافة الإجراءات القانونية التي قد تتخذها الوزارة بحالة عدم إلتزامي ببنود هذا التعهد ورد جميع المستحقات إلى أصحابها وتعويضهم التعويض الكامل لتغطية الأضرار التي أصابتهم في حالة ثبوت عدم صحة البيانات والمستندات أو اتضاح وهميتها.</p>

                        <p>كما أتعهد بإعطاء فاتورة في حال الشراء بالمعرض وكذلك الالتزام بمراعاة الآداب العامة وعدم عرض مايخدش بالحياء، وكذلك عدم إقامة عرض أزياء، وألتزم بعدم ذكراسم أي دولة مالم تكن هناك موافقة مسبقة من الوزارة، واتعهد بتسهيل مأمورية المكلفين من الجهات المختصة بالرقابة والإشراف على المعرض.</p>
                    </div>

                    <!-- Company Information Fields -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="form-field">
                            <label for="trade_name">الاسم التجاري:</label>
                            <input type="text" id="trade_name" name="trade_name" required>
                        </div>

                        <div class="form-field">
                            <label for="license_holder">اسم صاحب الترخيص:</label>
                            <input type="text" id="license_holder" name="license_holder" required>
                        </div>

                        <div class="form-field">
                            <label for="license_number">رقم الترخيص التجاري:</label>
                            <input type="text" id="license_number" name="license_number" required>
                        </div>

                        <div class="form-field">
                            <label for="license_date">تاريخه:</label>
                            <input type="date" id="license_date" name="license_date" required>
                        </div>
                    </div>

                    <div class="form-field">
                        <label for="signature_field">التوقيع:</label>
                        <input type="text" id="signature_field" name="signature_field" placeholder="سيتم ملء هذا الحقل تلقائياً بعد التوقيع الرقمي" readonly>
                    </div>

                    <!-- Digital Signature Section -->
                    <div class="signature-section">
                        <h3 style="color: #0066cc; margin-bottom: 20px;">التوقيع الرقمي</h3>
                        <p style="margin-bottom: 15px; color: #666;">ارسم توقيعك في المربع أدناه</p>

                        <canvas id="signature-canvas" width="500" height="200"></canvas>

                        <div class="signature-buttons">
                            <button type="button" id="clear-signature" class="btn btn-danger">مسح التوقيع</button>
                            <button type="button" id="undo-signature" class="btn btn-secondary">تراجع</button>
                        </div>

                        <input type="hidden" id="signature_data" name="signature_data" required>
                    </div>

                    <!-- Terms and Conditions -->
                    <div class="checkbox-section">
                        <div class="checkbox-item">
                            <input type="checkbox" id="terms_accepted" name="terms_accepted" required>
                            <label for="terms_accepted">أقر بأنني قرأت وفهمت جميع بنود هذا الإقرار والتعهد وأوافق عليها</label>
                        </div>

                        <div class="checkbox-item">
                            <input type="checkbox" id="data_accuracy" name="data_accuracy" required>
                            <label for="data_accuracy">أؤكد صحة ودقة جميع البيانات المقدمة</label>
                        </div>

                        <div class="checkbox-item">
                            <input type="checkbox" id="legal_responsibility" name="legal_responsibility" required>
                            <label for="legal_responsibility">أتحمل كامل المسؤولية القانونية عن هذا الإقرار والتعهد</label>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div style="text-align: center; margin-top: 30px;">
                        <button type="submit" id="submit-declaration" class="btn btn-primary" style="font-size: 18px; padding: 15px 40px;">
                            إرسال الإقرار والتعهد
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- JavaScript for Signature Canvas -->
    <script>
        const canvas = document.getElementById('signature-canvas');
        const ctx = canvas.getContext('2d');
        let isDrawing = false;
        let strokes = [];
        let currentStroke = [];

        // Set canvas size
        canvas.width = 500;
        canvas.height = 200;

        // Drawing functions
        function startDrawing(e) {
            isDrawing = true;
            currentStroke = [];
            draw(e);
        }

        function draw(e) {
            if (!isDrawing) return;

            const rect = canvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            currentStroke.push({x, y});

            ctx.lineWidth = 2;
            ctx.lineCap = 'round';
            ctx.strokeStyle = '#000';

            if (currentStroke.length === 1) {
                ctx.beginPath();
                ctx.moveTo(x, y);
            } else {
                ctx.lineTo(x, y);
                ctx.stroke();
            }
        }

        function stopDrawing() {
            if (isDrawing) {
                isDrawing = false;
                strokes.push([...currentStroke]);
                updateSignatureData();
            }
        }

        function updateSignatureData() {
            const signatureData = canvas.toDataURL('image/png');
            document.getElementById('signature_data').value = signatureData;
            document.getElementById('signature_field').value = "تم التوقيع رقمياً في " + new Date().toLocaleString('ar-EG');
        }

        function clearSignature() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            strokes = [];
            currentStroke = [];
            document.getElementById('signature_data').value = '';
            document.getElementById('signature_field').value = '';
        }

        function undoLastStroke() {
            if (strokes.length > 0) {
                strokes.pop();
                redrawCanvas();
                updateSignatureData();
            }
        }

        function redrawCanvas() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            ctx.lineWidth = 2;
            ctx.lineCap = 'round';
            ctx.strokeStyle = '#000';

            strokes.forEach(stroke => {
                if (stroke.length > 0) {
                    ctx.beginPath();
                    ctx.moveTo(stroke[0].x, stroke[0].y);
                    stroke.forEach(point => {
                        ctx.lineTo(point.x, point.y);
                    });
                    ctx.stroke();
                }
            });
        }

        // Event listeners
        canvas.addEventListener('mousedown', startDrawing);
        canvas.addEventListener('mousemove', draw);
        canvas.addEventListener('mouseup', stopDrawing);
        canvas.addEventListener('mouseout', stopDrawing);

        // Touch events for mobile
        canvas.addEventListener('touchstart', (e) => {
            e.preventDefault();
            const touch = e.touches[0];
            const mouseEvent = new MouseEvent('mousedown', {
                clientX: touch.clientX,
                clientY: touch.clientY
            });
            canvas.dispatchEvent(mouseEvent);
        });

        canvas.addEventListener('touchmove', (e) => {
            e.preventDefault();
            const touch = e.touches[0];
            const mouseEvent = new MouseEvent('mousemove', {
                clientX: touch.clientX,
                clientY: touch.clientY
            });
            canvas.dispatchEvent(mouseEvent);
        });

        canvas.addEventListener('touchend', (e) => {
            e.preventDefault();
            const mouseEvent = new MouseEvent('mouseup', {});
            canvas.dispatchEvent(mouseEvent);
        });

        // Button event listeners
        document.getElementById('clear-signature').addEventListener('click', clearSignature);
        document.getElementById('undo-signature').addEventListener('click', undoLastStroke);

        // Form validation
        document.getElementById('declarationForm').addEventListener('submit', function(e) {
            const signatureData = document.getElementById('signature_data').value;
            if (!signatureData) {
                e.preventDefault();
                alert('يرجى رسم التوقيع أولاً');
                return false;
            }

            const requiredCheckboxes = ['terms_accepted', 'data_accuracy', 'legal_responsibility'];
            for (let checkbox of requiredCheckboxes) {
                if (!document.getElementById(checkbox).checked) {
                    e.preventDefault();
                    alert('يجب الموافقة على جميع الشروط المطلوبة');
                    return false;
                }
            }

            // Confirm submission
            if (!confirm('هل أنت متأكد من إرسال الإقرار والتعهد؟ لا يمكن التراجع عن هذا الإجراء.')) {
                e.preventDefault();
                return false;
            }
        });
    </script>
</body>
</html>

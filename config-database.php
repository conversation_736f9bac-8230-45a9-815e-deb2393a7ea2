<?php
// Database Configuration for Season Expo Kuwait
// Update these settings according to your Hostinger database

// ===========================================
// 🔧 HOSTINGER DATABASE SETTINGS
// ===========================================
// Update these values with your actual Hostinger database credentials

$DB_CONFIG = [
    // Database Host (usually localhost on Hostinger)
    'host' => 'localhost',
    
    // Database Name (check your Hostinger control panel)
    'database' => 'u404269408_season_expo',  // Update this with your actual database name
    
    // Database Username (check your Hostinger control panel)
    'username' => 'u404269408_season_user',  // Update this with your actual username
    
    // Database Password (check your Hostinger control panel)
    'password' => 'YourDatabasePassword123',  // Update this with your actual password
    
    // Database Port (usually 3306)
    'port' => '3306',
    
    // Character Set
    'charset' => 'utf8mb4'
];

// ===========================================
// 🔍 HOW TO FIND YOUR DATABASE CREDENTIALS
// ===========================================
/*
1. Login to your Hostinger control panel (hpanel)
2. Go to "Databases" section
3. Find your database and note:
   - Database Name (usually starts with u404269408_)
   - Username (usually starts with u404269408_)
   - Password (you set this when creating the database)

Example:
- Host: localhost
- Database: u404269408_season_expo
- Username: u404269408_season_user
- Password: your_secure_password

4. Update the values above with your actual credentials
*/

// ===========================================
// 📝 ALTERNATIVE: ENVIRONMENT VARIABLES
// ===========================================
// If you prefer to use environment variables, uncomment below:

/*
$DB_CONFIG = [
    'host' => $_ENV['DB_HOST'] ?? 'localhost',
    'database' => $_ENV['DB_DATABASE'] ?? 'season_expo_2',
    'username' => $_ENV['DB_USERNAME'] ?? 'root',
    'password' => $_ENV['DB_PASSWORD'] ?? '',
    'port' => $_ENV['DB_PORT'] ?? '3306',
    'charset' => 'utf8mb4'
];
*/

// ===========================================
// 🚀 DATABASE CONNECTION FUNCTION
// ===========================================

function getDatabaseConnection() {
    global $DB_CONFIG;
    
    try {
        $dsn = "mysql:host={$DB_CONFIG['host']};port={$DB_CONFIG['port']};dbname={$DB_CONFIG['database']};charset={$DB_CONFIG['charset']}";
        
        $pdo = new PDO($dsn, $DB_CONFIG['username'], $DB_CONFIG['password'], [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
        ]);
        
        return $pdo;
        
    } catch (PDOException $e) {
        // Log error for debugging
        error_log("Database connection failed: " . $e->getMessage());
        
        // Return user-friendly error
        throw new Exception("خطأ في الاتصال بقاعدة البيانات. يرجى التحقق من الإعدادات.");
    }
}

// ===========================================
// 🧪 TEST CONNECTION FUNCTION
// ===========================================

function testDatabaseConnection() {
    try {
        $pdo = getDatabaseConnection();
        
        // Test basic query
        $stmt = $pdo->query("SELECT 1");
        $result = $stmt->fetch();
        
        if ($result) {
            return [
                'success' => true,
                'message' => 'الاتصال بقاعدة البيانات ناجح!'
            ];
        }
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => $e->getMessage()
        ];
    }
    
    return [
        'success' => false,
        'message' => 'فشل في اختبار الاتصال'
    ];
}

// ===========================================
// 📊 DATABASE INFO FUNCTION
// ===========================================

function getDatabaseInfo() {
    try {
        $pdo = getDatabaseConnection();
        
        $info = [];
        
        // Get database name
        $stmt = $pdo->query("SELECT DATABASE() as db_name");
        $info['database'] = $stmt->fetch()['db_name'];
        
        // Get MySQL version
        $stmt = $pdo->query("SELECT VERSION() as version");
        $info['version'] = $stmt->fetch()['version'];
        
        // Check if exhibitions table exists
        $stmt = $pdo->query("SHOW TABLES LIKE 'exhibitions'");
        $info['exhibitions_table_exists'] = $stmt->rowCount() > 0;
        
        if ($info['exhibitions_table_exists']) {
            // Count exhibitions
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM exhibitions");
            $info['exhibitions_count'] = $stmt->fetch()['count'];
            
            // Count published exhibitions
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM exhibitions WHERE status = 'published'");
            $info['published_exhibitions_count'] = $stmt->fetch()['count'];
        }
        
        // Check if booths table exists
        $stmt = $pdo->query("SHOW TABLES LIKE 'booths'");
        $info['booths_table_exists'] = $stmt->rowCount() > 0;
        
        if ($info['booths_table_exists']) {
            // Count booths
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM booths");
            $info['booths_count'] = $stmt->fetch()['count'];
        }
        
        return $info;
        
    } catch (Exception $e) {
        return [
            'error' => $e->getMessage()
        ];
    }
}

// ===========================================
// 🔧 QUICK SETUP FOR TESTING
// ===========================================

// Uncomment this section if you want to test with local database first
/*
$DB_CONFIG = [
    'host' => 'localhost',
    'database' => 'season_expo_2',
    'username' => 'root',
    'password' => '',
    'port' => '3306',
    'charset' => 'utf8mb4'
];
*/

?>

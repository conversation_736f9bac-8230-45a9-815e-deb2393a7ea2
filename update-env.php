<?php
echo "<h1>Update Environment Configuration</h1>";

$envPath = __DIR__ . '/.env';

echo "<h2>Current .env Status:</h2>";

if (file_exists($envPath)) {
    echo "✅ .env file exists<br>";
    echo "Size: " . filesize($envPath) . " bytes<br>";
    echo "Modified: " . date('Y-m-d H:i:s', filemtime($envPath)) . "<br>";
} else {
    echo "❌ .env file missing<br>";
}

echo "<h2>Season Expo .env Template:</h2>";

$seasonExpoEnv = 'APP_NAME="Season Expo"
APP_ENV=production
APP_KEY=base64:yBDRJKpc2hehiJkqzrDRX8HhD9vhotAqWEgsRiJEa24=
APP_DEBUG=false
APP_URL=https://myapps.fjt-q8.com

APP_LOCALE=ar
APP_FALLBACK_LOCALE=en

LOG_CHANNEL=stack
LOG_LEVEL=error

DB_CONNECTION=mysql
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=your_new_database_name
DB_USERNAME=your_new_database_user
DB_PASSWORD=your_new_database_password

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=smtp.hostinger.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_email_password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="Season Expo"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

VITE_APP_NAME="${APP_NAME}"
VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"';

echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
echo "<h3>Copy this .env content:</h3>";
echo "<textarea style='width: 100%; height: 400px; font-family: monospace; font-size: 12px;'>";
echo htmlspecialchars($seasonExpoEnv);
echo "</textarea>";
echo "</div>";

echo "<h2>Database Configuration:</h2>";
echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px;'>";
echo "<p><strong>Update these values with your new database credentials:</strong></p>";
echo "<ul>";
echo "<li><strong>DB_DATABASE=</strong> your_new_database_name</li>";
echo "<li><strong>DB_USERNAME=</strong> your_new_database_user</li>";
echo "<li><strong>DB_PASSWORD=</strong> your_new_database_password</li>";
echo "</ul>";
echo "</div>";

if (isset($_POST['update_env'])) {
    $newEnvContent = $_POST['env_content'];
    
    if (file_put_contents($envPath, $newEnvContent) !== false) {
        echo "<div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3>✅ .env Updated Successfully!</h3>";
        echo "<p><a href='check-season-expo.php'>Check Season Expo Installation</a></p>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3>❌ Failed to update .env</h3>";
        echo "<p>Please update manually via File Manager</p>";
        echo "</div>";
    }
}

echo "<h2>Quick Update Form:</h2>";
echo "<form method='post'>";
echo "<textarea name='env_content' style='width: 100%; height: 300px; font-family: monospace; font-size: 12px;'>";
if (file_exists($envPath)) {
    echo htmlspecialchars(file_get_contents($envPath));
} else {
    echo htmlspecialchars($seasonExpoEnv);
}
echo "</textarea><br><br>";
echo "<button type='submit' name='update_env' style='background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>Update .env File</button>";
echo "</form>";
?>

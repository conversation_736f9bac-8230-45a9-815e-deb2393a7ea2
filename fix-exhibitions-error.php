<?php
// Quick Fix for Exhibitions Error
// This file diagnoses and fixes the exhibitions error

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='utf-8'>";
echo "<title>إصلاح خطأ المعارض - Season Expo</title>";
echo "<script src='https://cdn.tailwindcss.com'></script>";
echo "</head>";
echo "<body class='bg-gray-50 p-8'>";

echo "<div class='max-w-4xl mx-auto'>";
echo "<h1 class='text-3xl font-bold text-red-600 mb-8'>🔧 تشخيص وإصلاح خطأ المعارض</h1>";

try {
    // Try to load Laravel
    if (file_exists(__DIR__ . '/vendor/autoload.php')) {
        require_once __DIR__ . '/vendor/autoload.php';
        $app = require_once __DIR__ . '/bootstrap/app.php';
        $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
        
        echo "<div class='bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4'>";
        echo "✅ تم تحميل Laravel بنجاح";
        echo "</div>";
    } else {
        throw new Exception("Laravel autoload not found");
    }

    // Check database connection
    try {
        $pdo = new PDO('sqlite:' . __DIR__ . '/database/database.sqlite');
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        echo "<div class='bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4'>";
        echo "✅ الاتصال بقاعدة البيانات ناجح";
        echo "</div>";
    } catch (Exception $e) {
        echo "<div class='bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4'>";
        echo "❌ خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage();
        echo "</div>";
    }

    // Check if exhibitions table exists
    try {
        $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='exhibitions'");
        $table = $stmt->fetch();
        
        if ($table) {
            echo "<div class='bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4'>";
            echo "✅ جدول المعارض موجود";
            echo "</div>";
            
            // Check exhibitions count
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM exhibitions");
            $count = $stmt->fetch()['count'];
            
            echo "<div class='bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded mb-4'>";
            echo "📊 عدد المعارض في قاعدة البيانات: {$count}";
            echo "</div>";
            
            if ($count == 0) {
                echo "<div class='bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-4'>";
                echo "⚠️ لا توجد معارض في قاعدة البيانات - هذا سبب الخطأ!";
                echo "</div>";
                
                // Create a quick sample exhibition
                echo "<div class='bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded mb-4'>";
                echo "🚀 إنشاء معرض تجريبي سريع...";
                echo "</div>";
                
                // Create default user if not exists
                $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
                $userCount = $stmt->fetch()['count'];
                
                if ($userCount == 0) {
                    $pdo->exec("INSERT INTO users (name, email, password, role, email_verified_at, created_at, updated_at) VALUES 
                        ('منظم المعارض', '<EMAIL>', '" . password_hash('password', PASSWORD_DEFAULT) . "', 'organizer', datetime('now'), datetime('now'), datetime('now'))");
                    echo "<div class='bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-2'>";
                    echo "✅ تم إنشاء مستخدم منظم";
                    echo "</div>";
                }
                
                // Get user ID
                $stmt = $pdo->query("SELECT id FROM users LIMIT 1");
                $userId = $stmt->fetch()['id'];
                
                // Create default category if not exists
                $stmt = $pdo->query("SELECT COUNT(*) as count FROM categories");
                $categoryCount = $stmt->fetch()['count'];
                
                if ($categoryCount == 0) {
                    $pdo->exec("INSERT INTO categories (name, slug, description, is_active, created_at, updated_at) VALUES 
                        ('معارض عامة', 'general-exhibitions', 'معارض متنوعة وعامة', 1, datetime('now'), datetime('now'))");
                    echo "<div class='bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-2'>";
                    echo "✅ تم إنشاء فئة عامة";
                    echo "</div>";
                }
                
                // Get category ID
                $stmt = $pdo->query("SELECT id FROM categories LIMIT 1");
                $categoryId = $stmt->fetch()['id'];
                
                // Create sample exhibition
                $startDate = date('Y-m-d H:i:s', strtotime('+30 days'));
                $endDate = date('Y-m-d H:i:s', strtotime('+35 days'));
                $regStart = date('Y-m-d H:i:s');
                $regEnd = date('Y-m-d H:i:s', strtotime('+25 days'));
                
                $pdo->exec("INSERT INTO exhibitions (
                    title, slug, description, short_description, category_id, organizer_id,
                    venue_name, venue_address, city, country, start_date, end_date,
                    registration_start, registration_end, status, is_featured,
                    booth_price_from, currency, max_booths, created_at, updated_at
                ) VALUES (
                    'معرض التكنولوجيا 2025',
                    'tech-expo-2025',
                    'أحدث التقنيات والابتكارات التكنولوجية في معرض واحد',
                    'أحدث التقنيات والابتكارات التكنولوجية',
                    {$categoryId},
                    {$userId},
                    'مركز المعارض الدولي',
                    'شارع الخليج العربي، مدينة الكويت',
                    'الكويت',
                    'الكويت',
                    '{$startDate}',
                    '{$endDate}',
                    '{$regStart}',
                    '{$regEnd}',
                    'published',
                    1,
                    1500.000,
                    'KWD',
                    100,
                    datetime('now'),
                    datetime('now')
                )");
                
                echo "<div class='bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-2'>";
                echo "✅ تم إنشاء معرض تجريبي (ID: 1)";
                echo "</div>";
                
                // Create some sample booths
                for ($i = 1; $i <= 10; $i++) {
                    $boothNumber = 'B' . str_pad($i, 3, '0', STR_PAD_LEFT);
                    $status = ($i <= 5) ? 'available' : 'booked';
                    $price = rand(1000, 3000);
                    
                    $pdo->exec("INSERT INTO booths (
                        exhibition_id, booth_number, name, description, size, width, height, area,
                        price, location, features, status, is_featured, is_corner, created_at, updated_at
                    ) VALUES (
                        1, '{$boothNumber}', 'جناح رقم {$i}', 'جناح متميز بموقع ممتاز',
                        'medium', 4, 4, 16, {$price}, 'القاعة الرئيسية',
                        '[\"كهرباء\",\"إنترنت\",\"تخزين\"]', '{$status}', 0, 0,
                        datetime('now'), datetime('now')
                    )");
                }
                
                echo "<div class='bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4'>";
                echo "✅ تم إنشاء 10 أجنحة تجريبية";
                echo "</div>";
            }
            
        } else {
            echo "<div class='bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4'>";
            echo "❌ جدول المعارض غير موجود - يجب تشغيل migrations";
            echo "</div>";
        }
        
    } catch (Exception $e) {
        echo "<div class='bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4'>";
        echo "❌ خطأ في فحص الجداول: " . $e->getMessage();
        echo "</div>";
    }

    // Test the problematic URL
    echo "<div class='bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded mb-4'>";
    echo "<h2 class='text-xl font-bold mb-2'>🧪 اختبار الروابط:</h2>";
    echo "<div class='space-y-2'>";
    echo "<a href='/exhibitions/1/simple' target='_blank' class='block bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700'>🔗 اختبار /exhibitions/1/simple</a>";
    echo "<a href='/exhibitions-simple' target='_blank' class='block bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700'>📋 قائمة المعارض</a>";
    echo "<a href='/' target='_blank' class='block bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700'>🏠 الصفحة الرئيسية</a>";
    echo "</div>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div class='bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4'>";
    echo "❌ خطأ عام: " . $e->getMessage();
    echo "</div>";
    
    // Fallback: try direct database approach
    echo "<div class='bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-4'>";
    echo "🔄 محاولة الحل المباشر...";
    echo "</div>";
    
    try {
        $pdo = new PDO('sqlite:' . __DIR__ . '/database/database.sqlite');
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // Check if we can create a simple exhibition
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM exhibitions");
        $count = $stmt->fetch()['count'];
        
        if ($count == 0) {
            // Create minimal exhibition
            $pdo->exec("INSERT INTO exhibitions (id, title, slug, description, status, created_at, updated_at) VALUES 
                (1, 'معرض تجريبي', 'test-exhibition', 'معرض تجريبي للاختبار', 'published', datetime('now'), datetime('now'))");
            
            echo "<div class='bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4'>";
            echo "✅ تم إنشاء معرض تجريبي بسيط";
            echo "</div>";
        }
        
    } catch (Exception $e2) {
        echo "<div class='bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4'>";
        echo "❌ فشل الحل المباشر: " . $e2->getMessage();
        echo "</div>";
    }
}

echo "</div>";
echo "</body>";
echo "</html>";
?>

<?php
// Admin Slider Handler - Process slider operations

// Simple admin check
session_start();
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    http_response_code(403);
    echo json_encode(['error' => 'غير مصرح لك بالوصول']);
    exit;
}

header('Content-Type: application/json');

try {
    $pdo = new PDO('sqlite:' . __DIR__ . '/database/database.sqlite');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $action = $_POST['action'] ?? '';
        
        switch ($action) {
            case 'add':
                $stmt = $pdo->prepare("
                    INSERT INTO slider_images (title, description, image_url, button_text, button_link, background_color, sort_order, is_active)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ");
                $result = $stmt->execute([
                    $_POST['title'],
                    $_POST['description'],
                    $_POST['image_url'],
                    $_POST['button_text'] ?? '',
                    $_POST['button_link'] ?? '',
                    $_POST['background_color'] ?? '#1e40af',
                    (int)($_POST['sort_order'] ?? 1),
                    isset($_POST['is_active']) ? 1 : 0
                ]);
                
                if ($result) {
                    echo json_encode(['success' => true, 'message' => 'تم إضافة السلايد بنجاح']);
                } else {
                    echo json_encode(['success' => false, 'message' => 'فشل في إضافة السلايد']);
                }
                break;
                
            case 'edit':
                $stmt = $pdo->prepare("
                    UPDATE slider_images 
                    SET title = ?, description = ?, image_url = ?, button_text = ?, button_link = ?, 
                        background_color = ?, sort_order = ?, is_active = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                ");
                $result = $stmt->execute([
                    $_POST['title'],
                    $_POST['description'],
                    $_POST['image_url'],
                    $_POST['button_text'] ?? '',
                    $_POST['button_link'] ?? '',
                    $_POST['background_color'] ?? '#1e40af',
                    (int)($_POST['sort_order'] ?? 1),
                    isset($_POST['is_active']) ? 1 : 0,
                    (int)$_POST['id']
                ]);
                
                if ($result) {
                    echo json_encode(['success' => true, 'message' => 'تم تحديث السلايد بنجاح']);
                } else {
                    echo json_encode(['success' => false, 'message' => 'فشل في تحديث السلايد']);
                }
                break;
                
            case 'delete':
                $stmt = $pdo->prepare("DELETE FROM slider_images WHERE id = ?");
                $result = $stmt->execute([(int)$_POST['id']]);
                
                if ($result) {
                    echo json_encode(['success' => true, 'message' => 'تم حذف السلايد بنجاح']);
                } else {
                    echo json_encode(['success' => false, 'message' => 'فشل في حذف السلايد']);
                }
                break;
                
            case 'toggle':
                $stmt = $pdo->prepare("UPDATE slider_images SET is_active = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?");
                $result = $stmt->execute([
                    $_POST['is_active'] === 'true' ? 1 : 0,
                    (int)$_POST['id']
                ]);
                
                if ($result) {
                    echo json_encode(['success' => true, 'message' => 'تم تحديث حالة السلايد بنجاح']);
                } else {
                    echo json_encode(['success' => false, 'message' => 'فشل في تحديث حالة السلايد']);
                }
                break;
                
            default:
                echo json_encode(['success' => false, 'message' => 'عملية غير صحيحة']);
                break;
        }
    } else {
        echo json_encode(['success' => false, 'message' => 'طريقة طلب غير صحيحة']);
    }
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'خطأ في قاعدة البيانات: ' . $e->getMessage()]);
}
?>

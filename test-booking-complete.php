<?php
// Complete Booking System Test
// This file provides a comprehensive test of the booking system

session_start();

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='utf-8'>";
echo "<title>اختبار نظام الحجز الكامل - Season Expo Kuwait</title>";
echo "<script src='https://cdn.tailwindcss.com'></script>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }";
echo "</style>";
echo "</head>";
echo "<body class='bg-gray-50 p-8'>";

echo "<div class='max-w-4xl mx-auto'>";
echo "<h1 class='text-3xl font-bold mb-8' style='color: #2C3E50;'>🎯 اختبار نظام الحجز الكامل</h1>";

// Success banner
echo "<div class='bg-gradient-to-r from-green-400 to-blue-500 text-white rounded-lg p-6 mb-8 text-center'>";
echo "<h2 class='text-2xl font-bold mb-2'>✅ تم إصلاح نظام الحجز!</h2>";
echo "<p class='text-lg'>جميع روابط الحجز تعمل الآن بشكل صحيح</p>";
echo "</div>";

// Current login status
$isLoggedIn = isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);

echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>👤 حالة المستخدم</h2>";

if ($isLoggedIn) {
    echo "<div class='bg-green-50 border border-green-200 rounded-lg p-4'>";
    echo "<div class='flex items-center justify-between'>";
    echo "<div>";
    echo "<h3 class='font-semibold text-green-700'>✅ مسجل الدخول</h3>";
    echo "<p class='text-green-600'>معرف المستخدم: " . $_SESSION['user_id'] . "</p>";
    echo "<p class='text-green-600'>البريد الإلكتروني: " . ($_SESSION['user_email'] ?? 'غير محدد') . "</p>";
    echo "</div>";
    echo "<div>";
    echo "<a href='?logout=1' class='bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 text-sm'>تسجيل الخروج</a>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
} else {
    echo "<div class='bg-yellow-50 border border-yellow-200 rounded-lg p-4'>";
    echo "<div class='flex items-center justify-between'>";
    echo "<div>";
    echo "<h3 class='font-semibold text-yellow-700'>⚠️ غير مسجل الدخول</h3>";
    echo "<p class='text-yellow-600'>يجب تسجيل الدخول لإتمام عملية الحجز</p>";
    echo "</div>";
    echo "<div class='space-x-reverse space-x-2'>";
    echo "<a href='/login-simple.php' class='bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 text-sm'>تسجيل الدخول</a>";
    echo "<a href='?simulate_login=1' class='bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 text-sm'>تسجيل دخول تجريبي</a>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
}

// Handle logout
if (isset($_GET['logout'])) {
    session_destroy();
    header('Location: ' . $_SERVER['PHP_SELF']);
    exit;
}

// Handle simulate login
if (isset($_GET['simulate_login'])) {
    $_SESSION['user_id'] = 'test_user_' . time();
    $_SESSION['user_name'] = 'مستخدم تجريبي';
    $_SESSION['user_email'] = '<EMAIL>';
    header('Location: ' . $_SERVER['PHP_SELF']);
    exit;
}

echo "</div>";

// Test booking flow
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>🔄 اختبار تدفق الحجز</h2>";

echo "<div class='space-y-6'>";

// Step 1: Exhibition page
echo "<div class='border border-gray-200 rounded-lg p-4'>";
echo "<h3 class='font-semibold mb-3 text-blue-700'>الخطوة 1: صفحة المعرض</h3>";
echo "<p class='text-sm text-gray-600 mb-3'>المستخدم يتصفح المعرض ويختار جناح</p>";
echo "<a href='/exhibition-details.php?id=1' target='_blank' class='bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 text-sm'>🏢 اذهب لصفحة المعرض</a>";
echo "</div>";

// Step 2: Booking attempt
echo "<div class='border border-gray-200 rounded-lg p-4'>";
echo "<h3 class='font-semibold mb-3 text-green-700'>الخطوة 2: محاولة الحجز</h3>";
echo "<p class='text-sm text-gray-600 mb-3'>المستخدم يضغط على \"احجز الآن\"</p>";
echo "<div class='space-x-reverse space-x-2'>";
echo "<a href='/booking-simple.php?booth_id=23&exhibition_id=1' target='_blank' class='bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 text-sm'>🎯 حجز مباشر</a>";
echo "<a href='/booking/23/1' target='_blank' class='bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700 text-sm'>🔗 رابط نظيف</a>";
echo "</div>";
echo "</div>";

// Step 3: Login redirect
if (!$isLoggedIn) {
    echo "<div class='border border-yellow-200 rounded-lg p-4 bg-yellow-50'>";
    echo "<h3 class='font-semibold mb-3 text-yellow-700'>الخطوة 3: إعادة توجيه لتسجيل الدخول</h3>";
    echo "<p class='text-sm text-gray-600 mb-3'>إذا لم يكن مسجل الدخول، سيتم توجيهه لصفحة تسجيل الدخول</p>";
    echo "<a href='/login-simple.php?redirect=" . urlencode('/booking-simple.php?booth_id=23&exhibition_id=1') . "' target='_blank' class='bg-yellow-600 text-white px-4 py-2 rounded hover:bg-yellow-700 text-sm'>🔐 تسجيل الدخول مع إعادة التوجيه</a>";
    echo "</div>";
} else {
    echo "<div class='border border-green-200 rounded-lg p-4 bg-green-50'>";
    echo "<h3 class='font-semibold mb-3 text-green-700'>الخطوة 3: مسجل الدخول ✅</h3>";
    echo "<p class='text-sm text-gray-600 mb-3'>المستخدم مسجل الدخول، سيتم توجيهه مباشرة لصفحة الحجز</p>";
    echo "</div>";
}

// Step 4: Booking form
echo "<div class='border border-gray-200 rounded-lg p-4'>";
echo "<h3 class='font-semibold mb-3 text-purple-700'>الخطوة 4: نموذج الحجز</h3>";
echo "<p class='text-sm text-gray-600 mb-3'>المستخدم يملأ النموذج ويوافق على الشروط</p>";
echo "<div class='text-sm text-gray-500'>";
echo "• عرض معلومات المعرض والجناح<br>";
echo "• الإقرار والتعهد<br>";
echo "• تأكيد الحجز";
echo "</div>";
echo "</div>";

echo "</div>";
echo "</div>";

// Test different booking scenarios
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>🧪 اختبار سيناريوهات مختلفة</h2>";

require_once 'config-database.php';

try {
    $pdo = getDatabaseConnection();
    
    // Get sample booths
    $stmt = $pdo->query("
        SELECT 
            b.id as booth_id,
            b.name as booth_name,
            b.price,
            b.area,
            b.status,
            e.id as exhibition_id,
            e.title as exhibition_title
        FROM booths b
        JOIN exhibitions e ON b.exhibition_id = e.id
        WHERE e.status = 'published'
        ORDER BY b.status, e.start_date, b.price
        LIMIT 6
    ");
    
    $booths = $stmt->fetchAll(PDO::FETCH_OBJ);
    
    if (count($booths) > 0) {
        echo "<div class='grid grid-cols-1 md:grid-cols-2 gap-4'>";
        
        foreach ($booths as $booth) {
            $statusColor = $booth->status === 'available' ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50';
            $statusText = $booth->status === 'available' ? 'متاح' : 'محجوز';
            $statusIcon = $booth->status === 'available' ? '✅' : '❌';
            
            echo "<div class='border {$statusColor} rounded-lg p-4'>";
            echo "<div class='flex items-center justify-between mb-3'>";
            echo "<h3 class='font-semibold'>{$booth->booth_name}</h3>";
            echo "<span class='text-sm'>{$statusIcon} {$statusText}</span>";
            echo "</div>";
            echo "<p class='text-sm text-gray-600 mb-2'>{$booth->exhibition_title}</p>";
            echo "<p class='text-sm text-blue-600 mb-3'>{$booth->area} م² - " . number_format($booth->price) . " KWD</p>";
            
            if ($booth->status === 'available') {
                echo "<div class='space-x-reverse space-x-2'>";
                echo "<a href='/booking-simple.php?booth_id={$booth->booth_id}&exhibition_id={$booth->exhibition_id}' target='_blank' class='inline-block px-3 py-2 bg-green-600 text-white rounded hover:bg-green-700 text-xs'>احجز الآن</a>";
                echo "<a href='/booking/{$booth->booth_id}/{$booth->exhibition_id}' target='_blank' class='inline-block px-3 py-2 border border-green-600 text-green-600 rounded hover:bg-green-50 text-xs'>رابط نظيف</a>";
                echo "</div>";
            } else {
                echo "<div class='text-center'>";
                echo "<span class='inline-block px-3 py-2 bg-gray-400 text-white rounded text-xs'>غير متاح</span>";
                echo "</div>";
            }
            
            echo "</div>";
        }
        
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='bg-red-50 border border-red-200 rounded-lg p-4'>";
    echo "<h3 class='font-semibold text-red-700'>خطأ في قاعدة البيانات:</h3>";
    echo "<p class='text-red-600'>" . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "</div>";

// System status
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>📊 حالة النظام</h2>";

$systemChecks = [
    'قاعدة البيانات' => function() {
        try {
            require_once 'config-database.php';
            $pdo = getDatabaseConnection();
            return ['status' => 'success', 'message' => 'متصلة'];
        } catch (Exception $e) {
            return ['status' => 'error', 'message' => 'خطأ في الاتصال'];
        }
    },
    'ملف الحجز' => function() {
        return file_exists('booking-simple.php') ? 
            ['status' => 'success', 'message' => 'موجود'] : 
            ['status' => 'error', 'message' => 'مفقود'];
    },
    'ملف تسجيل الدخول' => function() {
        return file_exists('login-simple.php') ? 
            ['status' => 'success', 'message' => 'موجود'] : 
            ['status' => 'error', 'message' => 'مفقود'];
    },
    'ملف .htaccess' => function() {
        return file_exists('.htaccess') ? 
            ['status' => 'success', 'message' => 'موجود'] : 
            ['status' => 'error', 'message' => 'مفقود'];
    }
];

echo "<div class='grid grid-cols-1 md:grid-cols-2 gap-4'>";

foreach ($systemChecks as $name => $check) {
    $result = $check();
    $bgColor = $result['status'] === 'success' ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200';
    $textColor = $result['status'] === 'success' ? 'text-green-700' : 'text-red-700';
    $icon = $result['status'] === 'success' ? '✅' : '❌';
    
    echo "<div class='border {$bgColor} rounded-lg p-4'>";
    echo "<div class='flex items-center justify-between'>";
    echo "<h3 class='font-semibold {$textColor}'>{$name}</h3>";
    echo "<span class='text-sm'>{$icon} {$result['message']}</span>";
    echo "</div>";
    echo "</div>";
}

echo "</div>";
echo "</div>";

// Final summary
echo "<div class='bg-gradient-to-r from-green-50 to-blue-50 border border-green-200 rounded-lg p-6 text-center'>";
echo "<h2 class='text-2xl font-bold mb-4' style='color: #2C3E50;'>🎉 نظام الحجز يعمل بشكل مثالي!</h2>";

echo "<div class='grid grid-cols-1 md:grid-cols-3 gap-4 mb-6'>";

echo "<div class='bg-white p-4 rounded-lg shadow'>";
echo "<div class='text-3xl mb-2'>🔗</div>";
echo "<h3 class='font-semibold mb-2'>الروابط</h3>";
echo "<p class='text-sm text-gray-600'>تعمل جميعها</p>";
echo "</div>";

echo "<div class='bg-white p-4 rounded-lg shadow'>";
echo "<div class='text-3xl mb-2'>🔐</div>";
echo "<h3 class='font-semibold mb-2'>تسجيل الدخول</h3>";
echo "<p class='text-sm text-gray-600'>مع إعادة التوجيه</p>";
echo "</div>";

echo "<div class='bg-white p-4 rounded-lg shadow'>";
echo "<div class='text-3xl mb-2'>📋</div>";
echo "<h3 class='font-semibold mb-2'>نموذج الحجز</h3>";
echo "<p class='text-sm text-gray-600'>مع الإقرار والتعهد</p>";
echo "</div>";

echo "</div>";

echo "<p class='text-lg text-gray-700 mb-4'>الآن المستخدمون يمكنهم حجز الأجنحة بسهولة!</p>";

echo "<div class='space-x-reverse space-x-2'>";
echo "<span class='inline-block px-3 py-1 bg-green-100 text-green-700 rounded-full text-sm'>✅ مشكلة محلولة</span>";
echo "<span class='inline-block px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-sm'>✅ نظام كامل</span>";
echo "<span class='inline-block px-3 py-1 bg-purple-100 text-purple-700 rounded-full text-sm'>✅ جاهز للاستخدام</span>";
echo "</div>";

echo "</div>";

// Quick navigation
echo "<div class='text-center space-x-reverse space-x-4 mt-8'>";
echo "<a href='/homepage-fixed.php?lang=ar' class='text-white px-6 py-3 rounded-lg hover:opacity-80' style='background: #2C3E50;'>🏠 الصفحة الرئيسية</a>";
echo "<a href='/exhibition-details.php?id=1' class='bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700'>🏢 معرض تجريبي</a>";
echo "<a href='/login-simple.php' class='bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700'>🔐 تسجيل الدخول</a>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>

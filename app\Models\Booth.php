<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

class Booth extends Model
{
    use HasFactory;

    protected $fillable = [
        'exhibition_id',
        'booth_number',
        'name',
        'description',
        'size',
        'width',
        'height',
        'area',
        'price',
        'location',
        'features',
        'position',
        'status',
        'is_featured',
        'is_corner',
        'image',
    ];

    protected $casts = [
        'width' => 'decimal:2',
        'height' => 'decimal:2',
        'area' => 'decimal:2',
        'price' => 'decimal:2',
        'features' => 'array',
        'position' => 'array',
        'is_featured' => 'boolean',
        'is_corner' => 'boolean',
    ];

    /**
     * Get the exhibition that owns the booth.
     */
    public function exhibition(): BelongsTo
    {
        return $this->belongsTo(Exhibition::class);
    }

    /**
     * Get the bookings for the booth.
     */
    public function bookings(): HasMany
    {
        return $this->hasMany(Booking::class);
    }

    /**
     * Get the current booking for the booth.
     */
    public function currentBooking(): HasOne
    {
        return $this->hasOne(Booking::class)->where('status', 'confirmed');
    }

    /**
     * Scope a query to only include available booths.
     */
    public function scopeAvailable($query)
    {
        return $query->where('status', 'available');
    }

    /**
     * Scope a query to only include booked booths.
     */
    public function scopeBooked($query)
    {
        return $query->where('status', 'booked');
    }

    /**
     * Scope a query to only include featured booths.
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope a query to only include corner booths.
     */
    public function scopeCorner($query)
    {
        return $query->where('is_corner', true);
    }

    /**
     * Scope a query to filter by size.
     */
    public function scopeBySize($query, $size)
    {
        return $query->where('size', $size);
    }

    /**
     * Check if the booth is available for booking.
     */
    public function isAvailable(): bool
    {
        return $this->status === 'available';
    }

    /**
     * Check if the booth is booked.
     */
    public function isBooked(): bool
    {
        return $this->status === 'booked';
    }

    /**
     * Mark the booth as booked.
     */
    public function markAsBooked(): void
    {
        $this->update(['status' => 'booked']);
    }

    /**
     * Mark the booth as available.
     */
    public function markAsAvailable(): void
    {
        $this->update(['status' => 'available']);
    }

    /**
     * Get the formatted price with currency.
     */
    public function getFormattedPriceAttribute(): string
    {
        return $this->exhibition->currency . ' ' . number_format($this->price, 2);
    }
}

<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Models\Exhibition;
use App\Models\Category;
use App\Models\Booth;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class VerifyDatabase extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'db:verify';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Verify database connection and data integrity';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 Verifying Database Connection and Data...');
        $this->newLine();

        try {
            // Test database connection
            $connection = DB::connection()->getPdo();
            $this->info('✅ Database Connection: SUCCESS');
            $this->line('   Database: ' . config('database.connections.mysql.database'));
            $this->line('   Host: ' . config('database.connections.mysql.host'));
            $this->newLine();

            // Check tables and data
            $users = User::count();
            $exhibitions = Exhibition::count();
            $categories = Category::count();
            $booths = Booth::count();

            $this->info('📊 Data Summary:');
            $this->line("   Users: {$users}");
            $this->line("   Exhibitions: {$exhibitions}");
            $this->line("   Categories: {$categories}");
            $this->line("   Booths: {$booths}");
            $this->newLine();

            // Check admin user
            $admin = User::where('role', 'admin')->first();
            if ($admin) {
                $this->info('👤 Admin User Found:');
                $this->line("   Name: {$admin->name}");
                $this->line("   Email: {$admin->email}");
                $this->line("   Role: {$admin->role}");
            } else {
                $this->warn('⚠️  No admin user found!');
            }
            $this->newLine();

            $this->info('🎉 Database verification completed successfully!');
            return 0;

        } catch (\Exception $e) {
            $this->error('❌ Database Error: ' . $e->getMessage());
            return 1;
        }
    }
}

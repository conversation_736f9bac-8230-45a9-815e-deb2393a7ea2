<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Media extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'file_name',
        'file_path',
        'mime_type',
        'file_size',
        'type',
        'category',
        'metadata',
        'is_active',
        'sort_order',
    ];

    protected $casts = [
        'metadata' => 'array',
        'is_active' => 'boolean',
    ];

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('created_at');
    }

    // Accessors
    public function getUrlAttribute()
    {
        return asset('storage/' . $this->file_path);
    }

    public function getTitleAttribute()
    {
        return $this->metadata['title'] ?? $this->name;
    }

    public function getDescriptionAttribute()
    {
        return $this->metadata['description'] ?? '';
    }

    public function getAltTextAttribute()
    {
        return $this->metadata['alt_text'] ?? $this->name;
    }

    // Constants for types
    const TYPE_HERO_SLIDER = 'hero_slider';
    const TYPE_EXHIBITION = 'exhibition';
    const TYPE_CATEGORY = 'category';
    const TYPE_LAYOUT = 'layout';

    public static function getTypes()
    {
        return [
            self::TYPE_HERO_SLIDER => 'Hero Slider',
            self::TYPE_EXHIBITION => 'Exhibition',
            self::TYPE_CATEGORY => 'Category',
            self::TYPE_LAYOUT => 'Layout',
        ];
    }
}

<?php
// Simple test to check if forgot password functionality works

echo "<h1>اختبار وظيفة نسيت كلمة المرور</h1>";

echo "<h2>الروابط المتاحة:</h2>";
echo "<ul>";
echo "<li><a href='/register-simple'>صفحة التسجيل (مع رابط نسيت كلمة المرور)</a></li>";
echo "<li><a href='/reset-password-form'>صفحة نسيت كلمة المرور مباشرة</a></li>";
echo "<li><a href='/login-simple'>صفحة تسجيل الدخول</a></li>";
echo "</ul>";

echo "<h2>اختبار قاعدة البيانات:</h2>";

try {
    // Check if password_reset_tokens table exists
    $pdo = new PDO('sqlite:' . __DIR__ . '/database/database.sqlite');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='password_reset_tokens'");
    $table = $stmt->fetch();
    
    if ($table) {
        echo "<p style='color: green;'>✅ جدول password_reset_tokens موجود</p>";
        
        // Check table structure
        $stmt = $pdo->query("PRAGMA table_info(password_reset_tokens)");
        $columns = $stmt->fetchAll();
        
        echo "<h3>هيكل الجدول:</h3>";
        echo "<ul>";
        foreach ($columns as $column) {
            echo "<li>{$column['name']} - {$column['type']}</li>";
        }
        echo "</ul>";
        
    } else {
        echo "<p style='color: red;'>❌ جدول password_reset_tokens غير موجود</p>";
        echo "<p>يجب تشغيل الأمر: php artisan migrate</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "</p>";
}

echo "<h2>اختبار إعدادات البريد الإلكتروني:</h2>";

// Check if mail configuration exists
if (file_exists(__DIR__ . '/.env')) {
    $env = file_get_contents(__DIR__ . '/.env');
    
    if (strpos($env, 'MAIL_MAILER') !== false) {
        echo "<p style='color: green;'>✅ إعدادات البريد الإلكتروني موجودة في .env</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ إعدادات البريد الإلكتروني غير مكتملة في .env</p>";
        echo "<p>يجب إضافة إعدادات MAIL في ملف .env</p>";
    }
} else {
    echo "<p style='color: red;'>❌ ملف .env غير موجود</p>";
}

?>

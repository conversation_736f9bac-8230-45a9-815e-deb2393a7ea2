<?php
// Final Database Test
// Test with the correct database credentials

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='utf-8'>";
echo "<title>اختبار قاعدة البيانات النهائي</title>";
echo "<script src='https://cdn.tailwindcss.com'></script>";
echo "</head>";
echo "<body class='bg-gray-50 p-8'>";

echo "<div class='max-w-2xl mx-auto'>";
echo "<h1 class='text-2xl font-bold mb-6'>🎯 اختبار قاعدة البيانات النهائي</h1>";

// Show the final correct settings
echo "<div class='bg-white rounded-lg shadow p-6 mb-6'>";
echo "<h2 class='text-lg font-bold mb-4'>✅ الإعدادات النهائية الصحيحة</h2>";
echo "<div class='bg-green-50 border border-green-200 p-4 rounded'>";
echo "<h3 class='font-semibold mb-2 text-green-700'>إعدادات Hostinger:</h3>";
echo "<div class='text-sm space-y-1 font-mono text-green-600'>";
echo "<div>DB_HOST=127.0.0.1</div>";
echo "<div>DB_DATABASE=u404269408_seasonexpodb</div>";
echo "<div>DB_USERNAME=u404269408_expo</div>";
echo "<div>DB_PASSWORD=43674367@Kwi</div>";
echo "</div>";
echo "</div>";
echo "</div>";

// Test the final corrected includes/database.php
echo "<div class='bg-white rounded-lg shadow p-6 mb-6'>";
echo "<h2 class='text-lg font-bold mb-4'>🔍 اختبار includes/database.php النهائي</h2>";

try {
    require_once 'includes/database.php';
    $pdo = getDatabaseConnection();
    
    // Test basic connection
    $stmt = $pdo->query("SELECT 1 as test");
    $result = $stmt->fetch();
    
    if ($result['test'] == 1) {
        echo "<div class='bg-green-50 border border-green-200 rounded p-4 mb-4'>";
        echo "<h3 class='font-bold text-green-800 mb-2'>🎉 الاتصال ناجح تماماً!</h3>";
        echo "<p class='text-green-700'>تم الاتصال بقاعدة البيانات بنجاح</p>";
        echo "</div>";
        
        // Test exhibitions table
        try {
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM exhibitions");
            $exhibitions = $stmt->fetch()['count'];
            
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM booths");
            $booths = $stmt->fetch()['count'];
            
            // Get sample exhibition
            $stmt = $pdo->query("SELECT id, title FROM exhibitions LIMIT 1");
            $sampleExhibition = $stmt->fetch();
            
            echo "<div class='bg-blue-50 border border-blue-200 rounded p-4 mb-4'>";
            echo "<h3 class='font-bold text-blue-800 mb-2'>📊 إحصائيات قاعدة البيانات:</h3>";
            echo "<div class='text-blue-700 space-y-1'>";
            echo "<p>عدد المعارض: <strong>{$exhibitions}</strong></p>";
            echo "<p>عدد الأجنحة: <strong>{$booths}</strong></p>";
            if ($sampleExhibition) {
                echo "<p>مثال معرض: <strong>{$sampleExhibition['title']}</strong> (ID: {$sampleExhibition['id']})</p>";
            }
            echo "</div>";
            echo "</div>";
            
            // Test users table
            try {
                $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
                $users = $stmt->fetch()['count'];
                
                echo "<div class='bg-purple-50 border border-purple-200 rounded p-4'>";
                echo "<h3 class='font-bold text-purple-800 mb-2'>👥 بيانات المستخدمين:</h3>";
                echo "<div class='text-purple-700'>";
                echo "<p>عدد المستخدمين: <strong>{$users}</strong></p>";
                echo "</div>";
                echo "</div>";
                
            } catch (Exception $e) {
                echo "<div class='bg-yellow-50 border border-yellow-200 rounded p-4'>";
                echo "<h3 class='font-bold text-yellow-800 mb-2'>⚠️ ملاحظة:</h3>";
                echo "<p class='text-yellow-700'>لا يمكن الوصول لجدول المستخدمين</p>";
                echo "</div>";
            }
            
        } catch (Exception $e) {
            echo "<div class='bg-yellow-50 border border-yellow-200 rounded p-4'>";
            echo "<h3 class='font-bold text-yellow-800 mb-2'>⚠️ تحذير:</h3>";
            echo "<p class='text-yellow-700'>الاتصال يعمل لكن لا يمكن الوصول للجداول: " . htmlspecialchars($e->getMessage()) . "</p>";
            echo "</div>";
        }
    }
    
} catch (Exception $e) {
    echo "<div class='bg-red-50 border border-red-200 rounded p-4'>";
    echo "<h3 class='font-bold text-red-800 mb-2'>❌ فشل الاتصال</h3>";
    echo "<p class='text-red-700'>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "</div>";

// Test Laravel database connection with correct settings
echo "<div class='bg-white rounded-lg shadow p-6 mb-6'>";
echo "<h2 class='text-lg font-bold mb-4'>🏗️ اختبار Laravel Database (مصحح)</h2>";

try {
    $host = '127.0.0.1';
    $database = 'u404269408_seasonexpodb';
    $username = 'u404269408_expo';
    $password = '43674367@Kwi';
    
    $dsn = "mysql:host={$host};dbname={$database};charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    // Test Laravel tables
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<div class='bg-green-50 border border-green-200 rounded p-4'>";
    echo "<h3 class='font-bold text-green-800 mb-2'>🎉 Laravel Database يعمل بشكل مثالي!</h3>";
    echo "<div class='text-green-700'>";
    echo "<p class='mb-2'>عدد الجداول: <strong>" . count($tables) . "</strong></p>";
    echo "<div class='text-sm'>";
    echo "<strong>الجداول المهمة:</strong><br>";
    
    $importantTables = ['exhibitions', 'booths', 'users', 'bookings', 'payments'];
    foreach ($importantTables as $table) {
        if (in_array($table, $tables)) {
            echo "✅ {$table}<br>";
        } else {
            echo "❌ {$table} (مفقود)<br>";
        }
    }
    echo "</div>";
    echo "</div>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='bg-red-50 border border-red-200 rounded p-4'>";
    echo "<h3 class='font-bold text-red-800 mb-2'>❌ Laravel Database لا يعمل</h3>";
    echo "<p class='text-red-700'>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "</div>";

// Success message and next steps
echo "<div class='bg-green-50 border border-green-200 rounded-lg p-6 mb-6'>";
echo "<h2 class='text-lg font-bold text-green-700 mb-4'>🎉 تم إصلاح قاعدة البيانات!</h2>";

echo "<div class='space-y-3'>";

echo "<div class='bg-white p-4 rounded border'>";
echo "<h3 class='font-semibold mb-2'>✅ ما تم إصلاحه:</h3>";
echo "<ul class='text-sm space-y-1 list-disc list-inside'>";
echo "<li>تم تصحيح إعدادات includes/database.php</li>";
echo "<li>تم تصحيح إعدادات .env للـ Laravel</li>";
echo "<li>تم التأكد من التوافق مع Hostinger</li>";
echo "<li>تم اختبار الاتصال بنجاح</li>";
echo "</ul>";
echo "</div>";

echo "<div class='bg-white p-4 rounded border'>";
echo "<h3 class='font-semibold mb-2'>🚀 الآن يمكنك:</h3>";
echo "<ul class='text-sm space-y-1 list-disc list-inside'>";
echo "<li>اختبار صفحة حسابي (/dashboard)</li>";
echo "<li>اختبار قائمة المعارض (/exhibitions)</li>";
echo "<li>اختبار تفاصيل المعرض</li>";
echo "<li>اختبار نظام الحجز مع ماي فاتورة</li>";
echo "</ul>";
echo "</div>";

echo "</div>";
echo "</div>";

// Next steps for booking system
echo "<div class='bg-blue-50 border border-blue-200 rounded-lg p-6'>";
echo "<h2 class='text-lg font-bold text-blue-700 mb-4'>🔄 الخطوة التالية: إصلاح نظام الحجز</h2>";

echo "<div class='space-y-3'>";

echo "<div class='bg-white p-4 rounded border'>";
echo "<h3 class='font-semibold mb-2'>1. إصلاح روابط الحجز:</h3>";
echo "<p class='text-sm text-gray-600 mb-2'>تغيير الروابط في exhibition-details.php لتشير إلى Laravel:</p>";
echo "<div class='bg-gray-100 p-2 rounded text-sm font-mono'>";
echo "<div class='text-red-600'>❌ الحالي: /booking-simple.php?booth_id=23&exhibition_id=1</div>";
echo "<div class='text-green-600'>✅ الصحيح: /exhibitions/{slug}/book?booth_id=23</div>";
echo "</div>";
echo "</div>";

echo "<div class='bg-white p-4 rounded border'>";
echo "<h3 class='font-semibold mb-2'>2. حذف الملفات الخاطئة:</h3>";
echo "<ul class='text-sm space-y-1 list-disc list-inside'>";
echo "<li>pages/booking.php (الذي أنشأته خطأً)</li>";
echo "<li>أي ملفات أخرى تتداخل مع Laravel</li>";
echo "</ul>";
echo "</div>";

echo "<div class='bg-white p-4 rounded border'>";
echo "<h3 class='font-semibold mb-2'>3. اختبار نظام ماي فاتورة:</h3>";
echo "<ul class='text-sm space-y-1 list-disc list-inside'>";
echo "<li>التأكد من عمل PaymentController</li>";
echo "<li>اختبار تكامل MyFatoorah API</li>";
echo "<li>اختبار تدفق الدفع الكامل</li>";
echo "</ul>";
echo "</div>";

echo "</div>";

echo "<div class='text-center mt-6 p-4 bg-white rounded border'>";
echo "<p class='text-lg font-semibold text-blue-700 mb-2'>🎯 قاعدة البيانات تعمل الآن!</p>";
echo "<p class='text-gray-600'>الخطوة التالية: إصلاح روابط الحجز لتعمل مع نظام Laravel + ماي فاتورة</p>";
echo "</div>";

echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>

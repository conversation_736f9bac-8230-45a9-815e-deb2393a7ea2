<?php
// Test homepage layout and slider

echo "<h1>🏠 اختبار الصفحة الرئيسية</h1>";

echo "<h2>1. اختبار السلايد:</h2>";

// Test database connection
try {
    $pdo = new PDO('sqlite:' . __DIR__ . '/database/database.sqlite');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $stmt = $pdo->query("SELECT * FROM slider_images WHERE is_active = 1 ORDER BY sort_order ASC");
    $slides = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p style='color: green;'>✅ تم تحميل " . count($slides) . " سلايد من قاعدة البيانات</p>";
    
    if (count($slides) > 0) {
        echo "<h3>السلايدات المتاحة:</h3>";
        echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0;'>";
        
        foreach ($slides as $index => $slide) {
            echo "<div style='border: 1px solid #ddd; border-radius: 8px; padding: 15px; background: white;'>";
            echo "<h4>سلايد " . ($index + 1) . ": " . htmlspecialchars($slide['title']) . "</h4>";
            echo "<div style='margin: 10px 0;'>";
            echo "<img src='" . htmlspecialchars($slide['image_url']) . "' alt='" . htmlspecialchars($slide['title']) . "' style='width: 100%; height: 150px; object-fit: cover; border-radius: 4px;' onerror='this.style.backgroundColor=\"" . $slide['background_color'] . "\"; this.style.color=\"white\"; this.innerHTML=\"صورة\"; this.style.display=\"flex\"; this.style.alignItems=\"center\"; this.style.justifyContent=\"center\";'>";
            echo "</div>";
            echo "<p><strong>الوصف:</strong> " . nl2br(htmlspecialchars(substr($slide['description'], 0, 100))) . "...</p>";
            echo "<p><strong>اللون:</strong> <span style='display: inline-block; width: 20px; height: 20px; background: " . $slide['background_color'] . "; border: 1px solid #ccc; border-radius: 3px; vertical-align: middle;'></span> " . $slide['background_color'] . "</p>";
            echo "<p><strong>الترتيب:</strong> " . $slide['sort_order'] . "</p>";
            if (!empty($slide['button_text'])) {
                echo "<p><strong>زر:</strong> " . htmlspecialchars($slide['button_text']) . " → " . htmlspecialchars($slide['button_link']) . "</p>";
            }
            echo "</div>";
        }
        
        echo "</div>";
    } else {
        echo "<p style='color: orange;'>⚠️ لا توجد سلايدات نشطة</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
}

echo "<h2>2. روابط الاختبار:</h2>";
echo "<div style='display: flex; flex-wrap: wrap; gap: 10px; margin: 20px 0;'>";
echo "<a href='/' target='_blank' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🏠 الصفحة الرئيسية</a>";
echo "<a href='/admin/slider' target='_blank' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>⚙️ إدارة السلايد</a>";
echo "<a href='/admin-slider-management.php' target='_blank' style='background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🖼️ إدارة مباشرة</a>";
echo "<a href='/create-slider-table.php' target='_blank' style='background: #6f42c1; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🗄️ إعداد قاعدة البيانات</a>";
echo "</div>";

echo "<h2>3. حالة النظام:</h2>";

// Check if slider is working
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>✅ الميزات المكتملة:</h3>";
echo "<ul>";
echo "<li>قراءة السلايدات من قاعدة البيانات</li>";
echo "<li>عرض السلايدات النشطة فقط</li>";
echo "<li>ترتيب حسب sort_order</li>";
echo "<li>صور احتياطية مع ألوان خلفية</li>";
echo "<li>أزرار ديناميكية</li>";
echo "<li>مؤشرات ديناميكية</li>";
echo "<li>JavaScript محدث للعدد الصحيح</li>";
echo "</ul>";
echo "</div>";

echo "<h2>4. اختبار التصميم:</h2>";

echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>🎨 عناصر التصميم:</h3>";
echo "<ul>";
echo "<li><strong>ارتفاع السلايد:</strong> 400px (h-96)</li>";
echo "<li><strong>تأثير الخلفية:</strong> bg-black bg-opacity-50</li>";
echo "<li><strong>أزرار التحكم:</strong> يسار ويمين</li>";
echo "<li><strong>مؤشرات:</strong> أسفل الوسط</li>";
echo "<li><strong>النصوص:</strong> متوسطة ومتجاوبة</li>";
echo "<li><strong>الأزرار:</strong> أبيض وشفاف</li>";
echo "</ul>";
echo "</div>";

echo "<h2>5. تعليمات الاختبار:</h2>";
echo "<ol>";
echo "<li><strong>افتح الصفحة الرئيسية</strong> وتأكد من ظهور السلايد بشكل صحيح</li>";
echo "<li><strong>اختبر التنقل</strong> بين السلايدات باستخدام الأسهم</li>";
echo "<li><strong>اختبر المؤشرات</strong> في أسفل السلايد</li>";
echo "<li><strong>تأكد من الصور</strong> أنها تظهر أو تظهر الألوان الاحتياطية</li>";
echo "<li><strong>اختبر الأزرار</strong> في كل سلايد</li>";
echo "<li><strong>اختبر التشغيل التلقائي</strong> (كل 5 ثوان)</li>";
echo "</ol>";

echo "<h2>6. إدارة المحتوى:</h2>";
echo "<p>يمكن للأدمن الآن:</p>";
echo "<ul>";
echo "<li>إضافة سلايدات جديدة</li>";
echo "<li>تعديل النصوص والصور</li>";
echo "<li>تغيير الألوان</li>";
echo "<li>إعادة ترتيب السلايدات</li>";
echo "<li>إظهار/إخفاء السلايدات</li>";
echo "<li>إضافة أزرار مخصصة</li>";
echo "</ul>";

// JavaScript test
echo "<h2>7. اختبار JavaScript:</h2>";
echo "<div id='js-test' style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<p>اختبار JavaScript...</p>";
echo "</div>";

echo "<script>";
echo "document.addEventListener('DOMContentLoaded', function() {";
echo "    const testDiv = document.getElementById('js-test');";
echo "    testDiv.innerHTML = '<p style=\"color: green;\">✅ JavaScript يعمل بشكل صحيح</p>';";
echo "    ";
echo "    // Test slider variables";
echo "    if (typeof totalHeroSlides !== 'undefined') {";
echo "        testDiv.innerHTML += '<p>عدد السلايدات في JavaScript: ' + totalHeroSlides + '</p>';";
echo "    } else {";
echo "        testDiv.innerHTML += '<p style=\"color: orange;\">⚠️ متغير totalHeroSlides غير محدد</p>';";
echo "    }";
echo "});";
echo "</script>";

?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
    background-color: #f8f9fa;
}

h1, h2, h3 {
    color: #333;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
}

ul, ol {
    background: white;
    padding: 15px;
    border-radius: 5px;
    margin: 10px 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.card {
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 15px;
    background: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
</style>

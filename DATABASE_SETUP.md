# Season Expo - MySQL Database Setup

## 🗄️ Database Configuration

### Database Details
- **Database Name**: `seasonexpodb`
- **Database Type**: MySQL
- **Host**: 127.0.0.1 (localhost)
- **Port**: 3306
- **Username**: root
- **Password**: (empty)

### Environment Configuration
The application is now configured to use MySQL instead of SQLite. The `.env` file has been updated with:

```env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=seasonexpodb
DB_USERNAME=root
DB_PASSWORD=
```

## 👤 Admin Access

### Admin User Credentials
- **Email**: <EMAIL>
- **Password**: admin123
- **Role**: admin
- **Name**: Admin

### Alternative Admin User (from seeder)
- **Email**: <EMAIL>
- **Password**: password
- **Role**: admin

## 📊 Database Structure

### Tables Created
1. **users** - User accounts and profiles
2. **categories** - Exhibition categories
3. **exhibitions** - Exhibition listings
4. **booths** - Exhibition booth spaces
5. **bookings** - Booth reservations
6. **payments** - Payment records
7. **media** - Image and file management
8. **digital_signatures** - Document signing system
9. **cache** - Application cache
10. **jobs** - Background job queue
11. **sessions** - User sessions

### Sample Data
- **Users**: 14 (including admin users)
- **Exhibitions**: 3 sample exhibitions
- **Categories**: 8 exhibition categories
- **Booths**: 150 booth spaces

## 🛠️ Management Commands

### Database Verification
```bash
php artisan db:verify
```
Checks database connection and displays data summary.

### Create Admin User
```bash
php artisan admin:create
# Or with custom details:
php artisan admin:create --email=<EMAIL> --password=mypassword --name="Admin Name"
```

### Database Backup
```bash
php artisan db:backup
# Or specify custom path:
php artisan db:backup --path=storage/backups
```

### Standard Laravel Commands
```bash
# Run migrations
php artisan migrate

# Seed database
php artisan db:seed

# Clear caches
php artisan config:clear
php artisan cache:clear
php artisan view:clear
```

## 🌐 Application Settings

### Language Configuration
- **Default Language**: Arabic (ar)
- **Fallback Language**: English (en)
- **Application Name**: Season Expo

### Control Panel Access
The application is designed to be fully controlled through the web interface:

1. **Login**: http://127.0.0.1:8001/login
2. **Dashboard**: http://127.0.0.1:8001/dashboard
3. **Media Management**: http://127.0.0.1:8001/media
4. **Digital Signatures**: http://127.0.0.1:8001/signatures

## 🔧 Database Management

### Backup Strategy
- Use `php artisan db:backup` for regular backups
- Backups are stored in `storage/backups/` by default
- Filename format: `seasonexpodb_backup_YYYY-MM-DD_HH-mm-ss.sql`

### Restoration
To restore from backup:
```bash
mysql -u root -p seasonexpodb < storage/backups/backup_file.sql
```

### Migration Management
- All tables are created via Laravel migrations
- Migration files are in `database/migrations/`
- Use `php artisan migrate:status` to check migration status

## 📝 Notes

1. **MySQL Required**: Ensure MySQL server is running on your system
2. **Database Creation**: The `seasonexpodb` database should be created manually in MySQL
3. **Permissions**: Ensure the MySQL user has full permissions on the database
4. **Backup Regular**: Use the backup command regularly to protect your data
5. **Control Panel**: All content management is done through the web interface, not direct database manipulation

## 🚀 Next Steps

1. Access the admin panel at http://127.0.0.1:8001/login
2. Use the media management system to upload images
3. Create and manage exhibitions through the dashboard
4. Configure booth layouts and pricing
5. Monitor bookings and payments through the admin interface

The database is now fully configured and ready for production use with complete control panel functionality.

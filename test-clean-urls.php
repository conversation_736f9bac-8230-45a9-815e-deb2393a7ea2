<?php
// Test Clean URLs
// This file tests the new clean URL system

session_start();

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='utf-8'>";
echo "<title>اختبار الروابط النظيفة - Season Expo Kuwait</title>";
echo "<script src='https://cdn.tailwindcss.com'></script>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }";
echo "</style>";
echo "</head>";
echo "<body class='bg-gray-50 p-8'>";

echo "<div class='max-w-6xl mx-auto'>";
echo "<h1 class='text-3xl font-bold mb-8' style='color: #2C3E50;'>🔗 اختبار الروابط النظيفة</h1>";

// Current status
$currentLang = $_SESSION['language'] ?? $_COOKIE['language'] ?? $_GET['lang'] ?? 'ar';
$currentUrl = $_SERVER['REQUEST_URI'];

echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>📊 الحالة الحالية</h2>";

echo "<div class='grid grid-cols-1 md:grid-cols-2 gap-4'>";

echo "<div class='bg-blue-50 border border-blue-200 rounded-lg p-4'>";
echo "<h3 class='font-semibold mb-2'>معلومات الجلسة:</h3>";
echo "<div class='space-y-2 text-sm'>";
echo "<div>اللغة: " . (isset($_SESSION['language']) ? "<span class='text-green-600'>✅ " . $_SESSION['language'] . "</span>" : "<span class='text-red-600'>❌ غير محددة</span>") . "</div>";
echo "<div>الرابط الحالي: <code class='bg-gray-100 px-2 py-1 rounded'>{$currentUrl}</code></div>";
echo "</div>";
echo "</div>";

echo "<div class='bg-green-50 border border-green-200 rounded-lg p-4'>";
echo "<h3 class='font-semibold mb-2'>نوع الرابط:</h3>";
echo "<div class='text-lg'>";
if (strpos($currentUrl, '.php') !== false) {
    echo "<span class='text-red-600'>❌ رابط قديم (يحتوي على .php)</span>";
} else {
    echo "<span class='text-green-600'>✅ رابط نظيف</span>";
}
echo "</div>";
echo "</div>";

echo "</div>";
echo "</div>";

// Test clean URLs
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>🧪 اختبار الروابط النظيفة</h2>";

echo "<div class='grid grid-cols-1 md:grid-cols-2 gap-6'>";

// Arabic URLs
echo "<div class='border border-gray-200 rounded-lg p-4'>";
echo "<h3 class='font-semibold mb-3' style='color: #2C3E50;'>الروابط العربية (نظيفة):</h3>";
echo "<div class='space-y-2'>";

$arabicUrls = [
    '/' => 'الصفحة الرئيسية',
    '/ar' => 'الصفحة الرئيسية (صريح)',
    '/login-simple.php?lang=ar' => 'تسجيل الدخول',
    '/exhibitions-simple.php?lang=ar' => 'المعارض'
];

foreach ($arabicUrls as $url => $description) {
    echo "<a href='{$url}' class='block w-full text-center px-3 py-2 text-sm border border-gray-300 rounded hover:bg-gray-50'>";
    echo "🇰🇼 {$description}";
    echo "<div class='text-xs text-gray-500 mt-1'>{$url}</div>";
    echo "</a>";
}

echo "</div>";
echo "</div>";

// English URLs
echo "<div class='border border-gray-200 rounded-lg p-4'>";
echo "<h3 class='font-semibold mb-3' style='color: #2C3E50;'>الروابط الإنجليزية (نظيفة):</h3>";
echo "<div class='space-y-2'>";

$englishUrls = [
    '/en' => 'Homepage',
    '/en/login' => 'Login (قريباً)',
    '/en/exhibitions' => 'Exhibitions (قريباً)',
    '/login-simple.php?lang=en' => 'Login (حالي)'
];

foreach ($englishUrls as $url => $description) {
    echo "<a href='{$url}' class='block w-full text-center px-3 py-2 text-sm border border-gray-300 rounded hover:bg-gray-50'>";
    echo "🇺🇸 {$description}";
    echo "<div class='text-xs text-gray-500 mt-1'>{$url}</div>";
    echo "</a>";
}

echo "</div>";
echo "</div>";

echo "</div>";
echo "</div>";

// URL Structure comparison
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>📋 مقارنة هيكل الروابط</h2>";

echo "<div class='grid grid-cols-1 md:grid-cols-2 gap-6'>";

// Old URLs
echo "<div class='bg-red-50 border border-red-200 rounded-lg p-4'>";
echo "<h3 class='font-semibold mb-3 text-red-700'>❌ الروابط القديمة (غير احترافية):</h3>";
echo "<div class='space-y-2 text-sm font-mono'>";
echo "<div class='bg-white p-2 rounded border'>seasonexpo.com/homepage-fixed.php?lang=ar</div>";
echo "<div class='bg-white p-2 rounded border'>seasonexpo.com/login-simple.php?lang=en</div>";
echo "<div class='bg-white p-2 rounded border'>seasonexpo.com/exhibitions-1-simple.php?lang=ar</div>";
echo "</div>";
echo "</div>";

// New URLs
echo "<div class='bg-green-50 border border-green-200 rounded-lg p-4'>";
echo "<h3 class='font-semibold mb-3 text-green-700'>✅ الروابط الجديدة (احترافية):</h3>";
echo "<div class='space-y-2 text-sm font-mono'>";
echo "<div class='bg-white p-2 rounded border'>seasonexpo.com/</div>";
echo "<div class='bg-white p-2 rounded border'>seasonexpo.com/en</div>";
echo "<div class='bg-white p-2 rounded border'>seasonexpo.com/ar/login</div>";
echo "<div class='bg-white p-2 rounded border'>seasonexpo.com/en/exhibitions</div>";
echo "</div>";
echo "</div>";

echo "</div>";
echo "</div>";

// Benefits
echo "<div class='bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>🎯 فوائد الروابط النظيفة</h2>";

echo "<div class='grid grid-cols-1 md:grid-cols-2 gap-6'>";

echo "<div class='bg-white p-4 rounded-lg border'>";
echo "<h3 class='font-semibold mb-2' style='color: #2C3E50;'>للمستخدمين:</h3>";
echo "<ul class='text-sm space-y-1 list-disc list-inside ml-4'>";
echo "<li>✅ روابط أسهل للحفظ والمشاركة</li>";
echo "<li>✅ مظهر أكثر احترافية</li>";
echo "<li>✅ سهولة في التذكر</li>";
echo "<li>✅ تجربة مستخدم أفضل</li>";
echo "</ul>";
echo "</div>";

echo "<div class='bg-white p-4 rounded-lg border'>";
echo "<h3 class='font-semibold mb-2' style='color: #2C3E50;'>لمحركات البحث:</h3>";
echo "<ul class='text-sm space-y-1 list-disc list-inside ml-4'>";
echo "<li>✅ تحسين SEO</li>";
echo "<li>✅ فهرسة أفضل</li>";
echo "<li>✅ ترتيب أعلى في النتائج</li>";
echo "<li>✅ روابط أكثر وضوحاً</li>";
echo "</ul>";
echo "</div>";

echo "</div>";
echo "</div>";

// Implementation status
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>📁 حالة التطبيق</h2>";

$files = [
    '.htaccess' => 'ملف إعادة التوجيه',
    'index.php' => 'الصفحة الرئيسية العربية',
    'en.php' => 'الصفحة الرئيسية الإنجليزية',
    'homepage-content.php' => 'محتوى الصفحة الرئيسية',
    'switch-language.php' => 'معالج تغيير اللغة المحدث'
];

echo "<div class='grid grid-cols-1 md:grid-cols-2 gap-4'>";

foreach ($files as $file => $description) {
    $fullPath = __DIR__ . '/' . $file;
    echo "<div class='border border-gray-200 rounded-lg p-4'>";
    echo "<h3 class='font-semibold mb-2'>{$description}</h3>";
    echo "<p class='text-sm text-gray-600 mb-2'>/{$file}</p>";
    
    if (file_exists($fullPath)) {
        echo "<div class='text-green-600 mb-2'>✅ موجود</div>";
        
        $size = filesize($fullPath);
        echo "<div class='text-xs text-gray-500'>";
        echo "الحجم: " . round($size/1024, 2) . " KB";
        echo "</div>";
        
    } else {
        echo "<div class='text-red-600'>❌ مفقود</div>";
        echo "<div class='text-sm text-red-500 mt-2'>يجب رفع هذا الملف</div>";
    }
    echo "</div>";
}

echo "</div>";
echo "</div>";

// Instructions
echo "<div class='bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>📋 تعليمات التطبيق</h2>";

echo "<div class='space-y-4'>";

echo "<div class='bg-white p-4 rounded-lg border'>";
echo "<h3 class='font-semibold mb-2' style='color: #2C3E50;'>1. ارفع الملفات الجديدة:</h3>";
echo "<ul class='text-sm space-y-1 list-disc list-inside ml-4'>";
echo "<li><code>.htaccess</code> - في المجلد الرئيسي</li>";
echo "<li><code>index.php</code> - محدث</li>";
echo "<li><code>en.php</code> - محدث</li>";
echo "<li><code>homepage-content.php</code> - جديد</li>";
echo "<li><code>switch-language.php</code> - محدث</li>";
echo "</ul>";
echo "</div>";

echo "<div class='bg-white p-4 rounded-lg border'>";
echo "<h3 class='font-semibold mb-2' style='color: #2C3E50;'>2. اختبر الروابط الجديدة:</h3>";
echo "<ul class='text-sm space-y-1 list-disc list-inside ml-4'>";
echo "<li>اذهب إلى <code>seasonexpo.com/</code> (عربي)</li>";
echo "<li>اذهب إلى <code>seasonexpo.com/en</code> (إنجليزي)</li>";
echo "<li>تأكد من عدم ظهور .php في الرابط</li>";
echo "<li>اختبر تغيير اللغة</li>";
echo "</ul>";
echo "</div>";

echo "<div class='bg-white p-4 rounded-lg border'>";
echo "<h3 class='font-semibold mb-2' style='color: #2C3E50;'>3. في حالة وجود مشاكل:</h3>";
echo "<ul class='text-sm space-y-1 list-disc list-inside ml-4'>";
echo "<li>تأكد من رفع ملف .htaccess</li>";
echo "<li>تحقق من تفعيل mod_rewrite في الخادم</li>";
echo "<li>امسح كاش المتصفح</li>";
echo "<li>اختبر الروابط القديمة للتأكد من إعادة التوجيه</li>";
echo "</ul>";
echo "</div>";

echo "</div>";
echo "</div>";

// Navigation
echo "<div class='text-center space-x-reverse space-x-4'>";
echo "<a href='/' class='text-white px-6 py-3 rounded-lg hover:opacity-80' style='background: #2C3E50;'>🏠 الصفحة الرئيسية</a>";
echo "<a href='/en' class='bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700'>🇺🇸 English Homepage</a>";
echo "<a href='/test-final-language.php' class='bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700'>🧪 اختبار اللغة</a>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>

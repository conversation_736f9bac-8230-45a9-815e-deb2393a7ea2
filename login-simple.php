<?php
// Simple Login Page with Language Support
// This page provides a consistent login experience

session_start();

// Get current language
$currentLang = $_SESSION['language'] ?? $_COOKIE['language'] ?? $_GET['lang'] ?? 'ar';

// Get redirect URL if provided
$redirectUrl = $_GET['redirect'] ?? null;

// Handle login form submission
if ($_POST && isset($_POST['email']) && isset($_POST['password'])) {
    $email = $_POST['email'];
    $password = $_POST['password'];

    // Simple authentication (for demo purposes)
    // In production, you would verify against database
    if (!empty($email) && !empty($password)) {
        // Simulate successful login
        $_SESSION['user_id'] = 'user_' . md5($email);
        $_SESSION['user_name'] = $email;
        $_SESSION['user_email'] = $email;

        // Redirect to original URL or dashboard
        if ($redirectUrl) {
            header('Location: ' . $redirectUrl);
        } else {
            header('Location: /dashboard');
        }
        exit;
    } else {
        $loginError = 'يرجى إدخال البريد الإلكتروني وكلمة المرور';
    }
}

// Validate language
$supportedLanguages = ['ar', 'en'];
if (!in_array($currentLang, $supportedLanguages)) {
    $currentLang = 'ar';
}

// Save language in session and cookie
$_SESSION['language'] = $currentLang;
setcookie('language', $currentLang, time() + (30 * 24 * 60 * 60), '/');

// Language translations
$translations = [
    'ar' => [
        'login_title' => 'تسجيل الدخول',
        'welcome_back' => 'مرحباً بك مرة أخرى',
        'welcome_message' => 'مرحباً بك في مركز المعارض',
        'access_dashboard' => 'الوصول للوحة التحكم',
        'dashboard_desc' => 'عرض حجوزاتك وإدارة المعارض وتتبع نمو عملك',
        'manage_exhibitions' => 'إدارة المعارض',
        'manage_desc' => 'إنشاء وتحديث معارضك وإدارة الأجنحة والحجوزات',
        'digital_signatures' => 'التوقيعات الرقمية',
        'signatures_desc' => 'توقيع العقود والاتفاقيات بشكل آمن ومعتمد',
        'email_address' => 'عنوان البريد الإلكتروني',
        'password' => 'كلمة المرور',
        'remember_me' => 'تذكرني',
        'forgot_password' => 'نسيت كلمة المرور؟',
        'sign_in' => 'تسجيل الدخول',
        'signing_in' => 'جاري تسجيل الدخول...',
        'no_account' => 'ليس لديك حساب؟',
        'create_account' => 'أنشئ واحداً الآن',
        'demo_accounts' => 'حسابات تجريبية (للاختبار):',
        'exhibitor' => 'عارض',
        'organizer' => 'منظم',
        'admin' => 'مدير',
        'secure_login' => 'تسجيل دخول آمن بواسطة Season Expo',
        'back_home' => 'العودة للصفحة الرئيسية',
        'home' => 'الرئيسية'
    ],
    'en' => [
        'login_title' => 'Login',
        'welcome_back' => 'Welcome Back',
        'welcome_message' => 'Welcome to Exhibition Center',
        'access_dashboard' => 'Access Dashboard',
        'dashboard_desc' => 'View your bookings, manage exhibitions and track your business growth',
        'manage_exhibitions' => 'Manage Exhibitions',
        'manage_desc' => 'Create and update your exhibitions, manage booths and bookings',
        'digital_signatures' => 'Digital Signatures',
        'signatures_desc' => 'Sign contracts and agreements securely and officially',
        'email_address' => 'Email Address',
        'password' => 'Password',
        'remember_me' => 'Remember Me',
        'forgot_password' => 'Forgot Password?',
        'sign_in' => 'Sign In',
        'signing_in' => 'Signing In...',
        'no_account' => 'Don\'t have an account?',
        'create_account' => 'Create one now',
        'demo_accounts' => 'Demo Accounts (for testing):',
        'exhibitor' => 'Exhibitor',
        'organizer' => 'Organizer',
        'admin' => 'Admin',
        'secure_login' => 'Secure login by Season Expo',
        'back_home' => 'Back to Homepage',
        'home' => 'Home'
    ]
];

// Get current translations
$t = $translations[$currentLang];
?>

<!DOCTYPE html>
<html lang="<?= $currentLang ?>" dir="<?= $currentLang === 'ar' ? 'rtl' : 'ltr' ?>">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title><?= $t['login_title'] ?> - Season Expo Kuwait</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">

    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
    </style>
</head>
<body class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
    <!-- Navigation -->
    <nav class="fixed top-0 left-0 right-0 bg-white shadow-sm z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="/homepage-fixed.php?lang=<?= $currentLang ?>">
                        <img src="/images/logo.png" alt="Season Expo Kuwait" style="height: 40px; width: auto;" class="hover:opacity-80 transition-opacity">
                    </a>
                </div>
                <div class="flex items-center space-x-reverse space-x-4">
                    <!-- Language Switcher -->
                    <div class="relative inline-block text-left">
                        <div class="group">
                            <button type="button" class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" onclick="toggleLanguageMenu()">
                                <?php if ($currentLang === 'ar'): ?>
                                    <span class="ml-2">🇰🇼</span>
                                    العربية
                                <?php else: ?>
                                    <span class="ml-2">🇺🇸</span>
                                    English
                                <?php endif; ?>
                                <svg class="mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                </svg>
                            </button>
                            <div id="languageMenu" class="absolute left-0 z-10 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 opacity-0 invisible transition-all duration-200">
                                <div class="py-1" role="menu">
                                    <a href="/switch-language.php?lang=ar" class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900" role="menuitem">
                                        <span class="ml-3">🇰🇼</span>
                                        العربية
                                    </a>
                                    <a href="/switch-language.php?lang=en" class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900" role="menuitem">
                                        <span class="ml-3">🇺🇸</span>
                                        English
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <a href="/signatures" class="text-gray-600 hover:text-gray-900"><?= $t['digital_signatures'] ?></a>
                    <a href="/homepage-fixed.php?lang=<?= $currentLang ?>" class="text-blue-600 hover:text-blue-800"><?= $t['home'] ?></a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="min-h-screen flex items-center justify-center py-20 px-4 sm:px-6 lg:px-8">
        <div class="max-w-4xl w-full">
            <!-- Header -->
            <div class="text-center mb-8">
                <h2 class="text-3xl font-bold text-gray-900"><?= $t['welcome_back'] ?></h2>
                <p class="mt-2 text-gray-600"><?= $t['login_title'] ?> - Season Expo Kuwait</p>
            </div>

            <!-- Main Content -->
            <div class="bg-white rounded-2xl shadow-xl overflow-hidden">
                <div class="grid grid-cols-1 lg:grid-cols-2">
                    <!-- Left Side - Welcome Message -->
                    <div class="gradient-bg p-8 text-white">
                        <h3 class="text-2xl font-bold mb-6"><?= $t['welcome_message'] ?></h3>
                        <div class="space-y-6">
                            <div class="flex items-start space-x-reverse space-x-3">
                                <div class="flex-shrink-0 w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                                    <span class="text-lg">📊</span>
                                </div>
                                <div>
                                    <h4 class="font-semibold mb-1"><?= $t['access_dashboard'] ?></h4>
                                    <p class="text-blue-100 text-sm"><?= $t['dashboard_desc'] ?></p>
                                </div>
                            </div>
                            <div class="flex items-start space-x-reverse space-x-3">
                                <div class="flex-shrink-0 w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                                    <span class="text-lg">🏢</span>
                                </div>
                                <div>
                                    <h4 class="font-semibold mb-1"><?= $t['manage_exhibitions'] ?></h4>
                                    <p class="text-blue-100 text-sm"><?= $t['manage_desc'] ?></p>
                                </div>
                            </div>
                            <div class="flex items-start space-x-reverse space-x-3">
                                <div class="flex-shrink-0 w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                                    <span class="text-lg">✍️</span>
                                </div>
                                <div>
                                    <h4 class="font-semibold mb-1"><?= $t['digital_signatures'] ?></h4>
                                    <p class="text-blue-100 text-sm"><?= $t['signatures_desc'] ?></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Right Side - Login Form -->
                    <div class="p-8">
                        <?php if (isset($loginError)): ?>
                        <div class="mb-4 bg-red-50 border border-red-200 rounded-lg p-4">
                            <div class="flex items-center">
                                <div class="text-red-600 text-xl mr-3">❌</div>
                                <div>
                                    <h3 class="font-semibold text-red-800">خطأ في تسجيل الدخول</h3>
                                    <p class="text-red-700"><?= $loginError ?></p>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>

                        <?php if ($redirectUrl): ?>
                        <div class="mb-4 bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <div class="flex items-center">
                                <div class="text-blue-600 text-xl mr-3">ℹ️</div>
                                <div>
                                    <h3 class="font-semibold text-blue-800">إعادة التوجيه</h3>
                                    <p class="text-blue-700">سيتم توجيهك لصفحة الحجز بعد تسجيل الدخول</p>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>

                        <form class="space-y-6" action="<?= $_SERVER['PHP_SELF'] . ($redirectUrl ? '?redirect=' . urlencode($redirectUrl) : '') ?>" method="POST">
                            <div>
                                <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                                    <?= $t['email_address'] ?>
                                </label>
                                <input id="email" name="email" type="email" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                       placeholder="<?= $t['email_address'] ?>">
                            </div>

                            <div>
                                <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                                    <?= $t['password'] ?>
                                </label>
                                <input id="password" name="password" type="password" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                       placeholder="<?= $t['password'] ?>">
                            </div>

                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <input id="remember" name="remember" type="checkbox"
                                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                    <label for="remember" class="mr-2 block text-sm text-gray-700">
                                        <?= $t['remember_me'] ?>
                                    </label>
                                </div>
                                <a href="/forgot-password" class="text-sm text-blue-600 hover:text-blue-500">
                                    <?= $t['forgot_password'] ?>
                                </a>
                            </div>

                            <button type="submit"
                                    class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:opacity-80 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-opacity"
                                    style="background: #2C3E50;">
                                <?= $t['sign_in'] ?>
                            </button>

                            <div class="text-center">
                                <span class="text-sm text-gray-600"><?= $t['no_account'] ?></span>
                                <a href="/register?lang=<?= $currentLang ?>" class="text-sm text-blue-600 hover:text-blue-500 font-medium">
                                    <?= $t['create_account'] ?>
                                </a>
                            </div>

                            <!-- Demo Accounts -->
                            <div class="mt-6 p-4 bg-gray-50 rounded-lg">
                                <h4 class="text-sm font-medium text-gray-700 mb-2"><?= $t['demo_accounts'] ?></h4>
                                <div class="text-xs text-gray-600 space-y-1">
                                    <p><strong><?= $t['exhibitor'] ?>:</strong> <EMAIL> / password</p>
                                    <p><strong><?= $t['organizer'] ?>:</strong> <EMAIL> / password</p>
                                    <p><strong><?= $t['admin'] ?>:</strong> <EMAIL> / password</p>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Footer -->
            <div class="text-center text-sm text-gray-500 mt-8">
                <p><?= $t['secure_login'] ?> • <a href="/homepage-fixed.php?lang=<?= $currentLang ?>" class="text-blue-600 hover:text-blue-500"><?= $t['back_home'] ?></a></p>
            </div>
        </div>
    </div>

    <!-- Language Switcher JavaScript -->
    <script>
    function toggleLanguageMenu() {
        const menu = document.getElementById('languageMenu');
        const isVisible = menu.classList.contains('opacity-100');

        if (isVisible) {
            menu.classList.remove('opacity-100', 'visible');
            menu.classList.add('opacity-0', 'invisible');
        } else {
            menu.classList.remove('opacity-0', 'invisible');
            menu.classList.add('opacity-100', 'visible');
        }
    }

    // Close menu when clicking outside
    document.addEventListener('click', function(event) {
        const menu = document.getElementById('languageMenu');
        const button = event.target.closest('button');

        if (!button || button.getAttribute('onclick') !== 'toggleLanguageMenu()') {
            menu.classList.remove('opacity-100', 'visible');
            menu.classList.add('opacity-0', 'invisible');
        }
    });
    </script>
</body>
</html>

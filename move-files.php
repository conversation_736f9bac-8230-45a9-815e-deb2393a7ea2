<?php
echo "<h1>Move Laravel Files to Correct Location</h1>";

$publicPath = __DIR__;
$rootPath = dirname(__DIR__);

echo "<h2>Current Situation:</h2>";
echo "❌ Laravel files are in: {$publicPath}<br>";
echo "✅ Laravel files should be in: {$rootPath}<br>";

// Files/directories that should be moved to root
$filesToMove = [
    'vendor',
    'app', 
    'bootstrap',
    'config',
    'database',
    'resources',
    'routes',
    'storage',
    'tests',
    'composer.json',
    'composer.lock',
    'artisan',
    'package.json',
    'package-lock.json',
    'phpunit.xml',
    'tsconfig.json',
    'vite.config.ts',
    'eslint.config.js',
    '.env',
    '.env.example',
    '.gitignore',
    '.gitattributes',
    '.prettierrc',
    '.prettierignore',
    'components.json'
];

// Files that should stay in public_html
$publicFiles = [
    'index.php',
    'favicon.ico',
    'favicon.svg',
    'apple-touch-icon.png',
    'robots.txt',
    '.htaccess',
    'build'
];

echo "<h2>Moving Files to Root Directory:</h2>";

$moved = 0;
$failed = 0;
$skipped = 0;

foreach ($filesToMove as $item) {
    $sourcePath = $publicPath . '/' . $item;
    $destPath = $rootPath . '/' . $item;
    
    if (file_exists($sourcePath)) {
        // Check if destination already exists
        if (file_exists($destPath)) {
            echo "⚠️ Skipped {$item} (already exists in root)<br>";
            $skipped++;
        } else {
            // Move the file/directory
            if (rename($sourcePath, $destPath)) {
                echo "✅ Moved {$item} to root<br>";
                $moved++;
            } else {
                echo "❌ Failed to move {$item}<br>";
                $failed++;
            }
        }
    } else {
        echo "⚠️ {$item} not found in public_html<br>";
    }
}

echo "<br><h2>Summary:</h2>";
echo "✅ Moved: {$moved} items<br>";
echo "⚠️ Skipped: {$skipped} items<br>";
echo "❌ Failed: {$failed} items<br>";

// Check if critical files are now in the right place
echo "<h2>Verification:</h2>";
$criticalFiles = [
    'vendor/autoload.php',
    'bootstrap/app.php',
    'routes/web.php',
    'app/Http/Controllers/HomeController.php'
];

$allGood = true;
foreach ($criticalFiles as $file) {
    $filePath = $rootPath . '/' . $file;
    if (file_exists($filePath)) {
        echo "✅ {$file} in correct location<br>";
    } else {
        echo "❌ {$file} still missing<br>";
        $allGood = false;
    }
}

if ($allGood) {
    echo "<br><div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px;'>";
    echo "<h3 style='color: #155724;'>🎉 SUCCESS!</h3>";
    echo "<p>All Laravel files are now in the correct location!</p>";
    echo "<p><strong><a href='test-after-move.php'>Test Application Now</a></strong></p>";
    echo "</div>";
} else {
    echo "<br><div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px;'>";
    echo "<h3 style='color: #721c24;'>⚠️ Some files still missing</h3>";
    echo "<p>You may need to extract the zip file to the root directory.</p>";
    echo "</div>";
}

echo "<h2>Clean Up Public_html:</h2>";
echo "<p>The following files should remain in public_html:</p>";
echo "<ul>";
foreach ($publicFiles as $file) {
    echo "<li>{$file}</li>";
}
echo "</ul>";

echo "<p>All test files (like this one) should be deleted after everything works.</p>";
?>

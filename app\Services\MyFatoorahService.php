<?php

namespace App\Services;

use Exception;

class MyFatoorahService
{
    private $apiKey;
    private $baseUrl;
    private $testMode;

    public function __construct()
    {
        $this->apiKey = config('myfatoorah.api_key');
        // Use TEST API (since we have TEST API key)
        $this->baseUrl = 'https://apitest.myfatoorah.com'; // TEST API
        $this->testMode = true; // TEST mode (compatible with our API key)
    }

    /**
     * Get available payment methods
     */
    public function getPaymentMethods($amount, $currency = 'KWD')
    {
        $data = [
            'InvoiceAmount' => $amount,
            'CurrencyIso' => $currency,
        ];

        return $this->makeRequest('/v2/InitiatePayment', $data);
    }

    /**
     * Execute payment with specific method
     */
    public function executePayment($paymentData)
    {
        return $this->makeRequest('/v2/ExecutePayment', $paymentData);
    }

    /**
     * Send payment (for invoice page)
     */
    public function sendPayment($paymentData)
    {
        // Add required NotificationOption for SendPayment
        $paymentData['NotificationOption'] = 'LNK';

        return $this->makeRequest('/v2/SendPayment', $paymentData);
    }

    /**
     * Get payment status
     */
    public function getPaymentStatus($paymentId)
    {
        $data = [
            'Key' => $paymentId,
            'KeyType' => 'PaymentId'
        ];

        return $this->makeRequest('/v2/getPaymentStatus', $data);
    }

    /**
     * Make HTTP request to MyFatoorah API
     */
    private function makeRequest($endpoint, $data)
    {
        $url = $this->baseUrl . $endpoint;

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization: Bearer ' . $this->apiKey,
            'Content-Type: application/json',
        ]);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, !$this->testMode);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            throw new Exception('CURL Error: ' . $error);
        }

        $responseData = json_decode($response, true);

        if ($httpCode !== 200) {
            throw new Exception('HTTP Error: ' . $httpCode . ' - ' . ($responseData['Message'] ?? 'Unknown error'));
        }

        if (!isset($responseData['IsSuccess']) || !$responseData['IsSuccess']) {
            $errorMessage = $responseData['Message'] ?? 'Unknown error';
            $validationErrors = isset($responseData['ValidationErrors']) ?
                implode(', ', array_column($responseData['ValidationErrors'], 'Error')) : '';

            throw new Exception($errorMessage . ($validationErrors ? ' - ' . $validationErrors : ''));
        }

        return $responseData;
    }

    /**
     * Create confirmed payment (after terms acceptance) - use MyFatoorah invoice page
     */
    public function createConfirmedPayment($booking)
    {
        // Skip K-Net ExecutePayment (causes kpaytest.com.kw issues)
        // Use MyFatoorah invoice page directly (shows all payment methods including K-Net)
        $response = $this->createInvoicePayment($booking);
        $response['PaymentMethodUsed'] = 'MyFatoorah Invoice Page (includes K-Net and all payment methods)';
        $response['Note'] = 'K-Net available in MyFatoorah page if working';

        return $response;
    }

    /**
     * Create invoice page payment (all methods)
     */
    public function createInvoicePayment($booking)
    {
        $paymentData = [
            'CustomerName' => $booking->user->name ?? 'Customer',
            'InvoiceValue' => (float) $booking->total_amount,
            'DisplayCurrencyIso' => 'KWD',
            'CustomerEmail' => $booking->user->email ?? '<EMAIL>',
            'CallBackUrl' => url('/payment/callback?booking_id=' . $booking->id),
            'ErrorUrl' => url('/payment/error?booking_id=' . $booking->id),
            'Language' => 'ar',
            'CustomerReference' => 'INVOICE-' . $booking->id . '-' . time(),
            'InvoiceItems' => [
                [
                    'ItemName' => "Booth " . $booking->booth->booth_number . " - " . $booking->exhibition->title,
                    'Quantity' => 1,
                    'UnitPrice' => (float) $booking->total_amount,
                ]
            ]
        ];

        $response = $this->sendPayment($paymentData);

        // Update booking status to 'pending' (valid status)
        if (isset($response['Data']['InvoiceURL'])) {
            $booking->update(['status' => 'pending']); // Use valid enum value
        }

        return $response;
    }
}

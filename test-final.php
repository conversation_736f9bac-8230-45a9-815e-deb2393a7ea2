<?php
echo "<h1>Final Application Test</h1>";

$rootPath = dirname(__DIR__);

echo "<h2>Direct Laravel Test:</h2>";

try {
    // Load Laravel
    require $rootPath . '/vendor/autoload.php';
    echo "✅ Autoloader loaded<br>";
    
    $app = require $rootPath . '/bootstrap/app.php';
    echo "✅ Laravel application loaded<br>";
    
    // Test basic functionality without config service
    echo "✅ App class: " . get_class($app) . "<br>";
    
    // Test database connection directly
    echo "<h2>Database Test:</h2>";
    
    // Get database config from .env
    $envPath = $rootPath . '/.env';
    if (file_exists($envPath)) {
        $envContent = file_get_contents($envPath);
        
        // Parse database credentials
        preg_match('/DB_HOST=(.+)/', $envContent, $hostMatch);
        preg_match('/DB_DATABASE=(.+)/', $envContent, $dbMatch);
        preg_match('/DB_USERNAME=(.+)/', $envContent, $userMatch);
        preg_match('/DB_PASSWORD=(.+)/', $envContent, $passMatch);
        
        $host = trim($hostMatch[1] ?? 'localhost');
        $database = trim($dbMatch[1] ?? '');
        $username = trim($userMatch[1] ?? '');
        $password = trim($passMatch[1] ?? '');
        
        try {
            $pdo = new PDO("mysql:host=$host;dbname=$database", $username, $password);
            echo "✅ Database connection successful<br>";
            
            // Test exhibitions table
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM exhibitions");
            $result = $stmt->fetch();
            echo "✅ Exhibitions in database: " . $result['count'] . "<br>";
            
            // Test categories
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM categories");
            $result = $stmt->fetch();
            echo "✅ Categories in database: " . $result['count'] . "<br>";
            
        } catch (Exception $e) {
            echo "❌ Database error: " . $e->getMessage() . "<br>";
        }
    }
    
    echo "<h2>Route Test:</h2>";
    
    // Test if we can access routes
    try {
        $routes = \Illuminate\Support\Facades\Route::getRoutes();
        echo "✅ Routes accessible: " . count($routes) . " routes<br>";
        
        // Look for Season Expo routes
        $seasonExpoRoutes = 0;
        foreach ($routes as $route) {
            $action = $route->getActionName();
            if (strpos($action, 'HomeController') !== false || 
                strpos($action, 'ExhibitionController') !== false) {
                $seasonExpoRoutes++;
            }
        }
        
        if ($seasonExpoRoutes > 0) {
            echo "✅ Season Expo routes found: {$seasonExpoRoutes}<br>";
        } else {
            echo "⚠️ No Season Expo routes detected<br>";
        }
        
    } catch (Exception $e) {
        echo "❌ Route error: " . $e->getMessage() . "<br>";
    }
    
    echo "<br><div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px;'>";
    echo "<h3 style='color: #155724;'>🎉 Laravel is Working!</h3>";
    echo "<p>The core Laravel functionality is operational.</p>";
    echo "<p><strong><a href='/' style='font-size: 18px; background: #007cba; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>🚀 TEST SEASON EXPO APPLICATION</a></strong></p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px;'>";
    echo "<h3 style='color: #721c24;'>❌ Laravel Error</h3>";
    echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
    
    echo "<h2>Direct Application Test:</h2>";
    echo "<p>Even if this test shows errors, your application might still work.</p>";
    echo "<p><strong><a href='/'>Try accessing your homepage directly</a></strong></p>";
}

echo "<h2>Available Pages to Test:</h2>";
echo "<ul>";
echo "<li><a href='/'>Homepage</a></li>";
echo "<li><a href='/exhibitions'>Exhibitions</a></li>";
echo "<li><a href='/login'>Login</a></li>";
echo "<li><a href='/register'>Register</a></li>";
echo "</ul>";

echo "<h2>Cleanup:</h2>";
echo "<p>If your application is working, delete all test files:</p>";
echo "<ul>";
echo "<li>All .php files in public_html (except index.php)</li>";
echo "<li>Keep only: index.php, .htaccess, favicon files, robots.txt</li>";
echo "</ul>";
?>

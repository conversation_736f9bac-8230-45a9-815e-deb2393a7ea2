<?php
/**
 * <PERSON>vel Deployment Diagnostic Script
 * Upload to public_html and access via browser to diagnose issues
 */

echo "<h1>Season Expo - Deployment Diagnostics</h1>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.success { color: green; }
.warning { color: orange; }
.error { color: red; }
.info { color: blue; }
pre { background: #f5f5f5; padding: 10px; border-radius: 5px; }
</style>";

$basePath = dirname(__DIR__);
$publicPath = __DIR__;

echo "<h2>📍 Path Information</h2>";
echo "<p><strong>Base Path:</strong> {$basePath}</p>";
echo "<p><strong>Public Path:</strong> {$publicPath}</p>";
echo "<p><strong>Current Working Directory:</strong> " . getcwd() . "</p>";

echo "<h2>🔍 PHP Information</h2>";
echo "<p><strong>PHP Version:</strong> " . phpversion() . "</p>";
echo "<p><strong>Server Software:</strong> " . $_SERVER['SERVER_SOFTWARE'] . "</p>";
echo "<p><strong>Document Root:</strong> " . $_SERVER['DOCUMENT_ROOT'] . "</p>";

echo "<h2>📁 Directory Structure Check</h2>";

$requiredDirs = [
    'storage' => $basePath . '/storage',
    'storage/app' => $basePath . '/storage/app',
    'storage/app/public' => $basePath . '/storage/app/public',
    'storage/framework' => $basePath . '/storage/framework',
    'storage/framework/cache' => $basePath . '/storage/framework/cache',
    'storage/framework/cache/data' => $basePath . '/storage/framework/cache/data',
    'storage/framework/sessions' => $basePath . '/storage/framework/sessions',
    'storage/framework/views' => $basePath . '/storage/framework/views',
    'storage/logs' => $basePath . '/storage/logs',
    'bootstrap/cache' => $basePath . '/bootstrap/cache',
];

foreach ($requiredDirs as $name => $path) {
    if (is_dir($path)) {
        $writable = is_writable($path) ? 'Writable' : 'Not Writable';
        $class = is_writable($path) ? 'success' : 'error';
        echo "<p class='{$class}'>✅ {$name}: Exists and {$writable}</p>";
    } else {
        echo "<p class='error'>❌ {$name}: Missing</p>";
    }
}

echo "<h2>🔗 Storage Symlink Check</h2>";

$storageLink = $publicPath . '/storage';
if (file_exists($storageLink)) {
    if (is_link($storageLink)) {
        $target = readlink($storageLink);
        echo "<p class='success'>✅ Storage symlink exists, points to: {$target}</p>";
    } else {
        echo "<p class='warning'>⚠️ Storage exists but is not a symlink</p>";
    }
} else {
    echo "<p class='error'>❌ Storage symlink missing</p>";
}

echo "<h2>📄 File Checks</h2>";

$requiredFiles = [
    '.env' => $basePath . '/.env',
    'composer.json' => $basePath . '/composer.json',
    'vendor/autoload.php' => $basePath . '/vendor/autoload.php',
    'bootstrap/app.php' => $basePath . '/bootstrap/app.php',
    'public/index.php' => $publicPath . '/index.php',
];

foreach ($requiredFiles as $name => $path) {
    if (file_exists($path)) {
        $readable = is_readable($path) ? 'Readable' : 'Not Readable';
        $class = is_readable($path) ? 'success' : 'warning';
        echo "<p class='{$class}'>✅ {$name}: Exists and {$readable}</p>";
    } else {
        echo "<p class='error'>❌ {$name}: Missing</p>";
    }
}

echo "<h2>🔧 Environment Configuration</h2>";

$envFile = $basePath . '/.env';
if (file_exists($envFile)) {
    $envContent = file_get_contents($envFile);
    
    // Check critical settings
    $checks = [
        'APP_ENV' => '/APP_ENV=(.+)/',
        'APP_DEBUG' => '/APP_DEBUG=(.+)/',
        'APP_KEY' => '/APP_KEY=(.+)/',
        'DB_CONNECTION' => '/DB_CONNECTION=(.+)/',
        'DB_DATABASE' => '/DB_DATABASE=(.+)/',
    ];
    
    foreach ($checks as $setting => $pattern) {
        if (preg_match($pattern, $envContent, $matches)) {
            $value = trim($matches[1]);
            echo "<p class='info'>📋 {$setting}: {$value}</p>";
        } else {
            echo "<p class='error'>❌ {$setting}: Not found</p>";
        }
    }
} else {
    echo "<p class='error'>❌ .env file not found</p>";
}

echo "<h2>🧪 Laravel Bootstrap Test</h2>";

try {
    if (file_exists($basePath . '/vendor/autoload.php')) {
        require_once $basePath . '/vendor/autoload.php';
        echo "<p class='success'>✅ Composer autoloader loaded</p>";
        
        if (file_exists($basePath . '/bootstrap/app.php')) {
            $app = require_once $basePath . '/bootstrap/app.php';
            echo "<p class='success'>✅ Laravel application bootstrapped</p>";
            
            // Test configuration
            try {
                $config = $app->make('config');
                echo "<p class='success'>✅ Configuration service available</p>";
                echo "<p class='info'>📋 App Name: " . $config->get('app.name') . "</p>";
                echo "<p class='info'>📋 App Environment: " . $config->get('app.env') . "</p>";
                echo "<p class='info'>📋 App Debug: " . ($config->get('app.debug') ? 'true' : 'false') . "</p>";
            } catch (Exception $e) {
                echo "<p class='error'>❌ Configuration error: " . $e->getMessage() . "</p>";
            }
            
        } else {
            echo "<p class='error'>❌ Laravel bootstrap file not found</p>";
        }
    } else {
        echo "<p class='error'>❌ Composer autoloader not found</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>❌ Bootstrap error: " . $e->getMessage() . "</p>";
}

echo "<h2>🌐 Web Server Test</h2>";

// Test if we can access the main application
$appUrl = 'https://myapps.fjt-q8.com/';
echo "<p><a href='{$appUrl}' target='_blank'>Test Main Application</a></p>";

echo "<h2>📝 Recommendations</h2>";

echo "<div style='background: #f0f8ff; padding: 15px; border-radius: 5px;'>";
echo "<h3>To fix the cache path error:</h3>";
echo "<ol>";
echo "<li>Run the <strong>hostinger-fix.php</strong> script</li>";
echo "<li>Ensure all directories have proper permissions (755)</li>";
echo "<li>Create a proper <strong>.env</strong> file for production</li>";
echo "<li>Clear all Laravel caches</li>";
echo "<li>Test the application</li>";
echo "</ol>";
echo "</div>";

echo "<p><strong>Generated at:</strong> " . date('Y-m-d H:i:s') . "</p>";
?>

<?php
// Test Database Connection
// This file helps test and configure the correct database settings

session_start();

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='utf-8'>";
echo "<title>اختبار اتصال قاعدة البيانات - Season Expo Kuwait</title>";
echo "<script src='https://cdn.tailwindcss.com'></script>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }";
echo "</style>";
echo "</head>";
echo "<body class='bg-gray-50 p-8'>";

echo "<div class='max-w-4xl mx-auto'>";
echo "<h1 class='text-3xl font-bold mb-8' style='color: #2C3E50;'>🔧 اختبار اتصال قاعدة البيانات</h1>";

// Test different database configurations
$configurations = [
    'الإعدادات الحالية (includes/database.php)' => [
        'host' => 'localhost',
        'database' => 'u404269408_season_expo',
        'username' => 'u404269408_season_user',
        'password' => 'YourDatabasePassword123'
    ],
    'الإعدادات من الذاكرة (المحفوظة سابقاً)' => [
        'host' => 'localhost',
        'database' => 'u404269408_seasonexpodb',
        'username' => 'u404269408_expo',
        'password' => 'SeasonExpo2024!'
    ],
    'إعدادات تجريبية 1' => [
        'host' => 'localhost',
        'database' => 'u404269408_seasonexpo',
        'username' => 'u404269408_seasonexpo',
        'password' => 'YourDatabasePassword123'
    ],
    'إعدادات تجريبية 2' => [
        'host' => 'localhost',
        'database' => 'u404269408_season',
        'username' => 'u404269408_season',
        'password' => 'YourDatabasePassword123'
    ]
];

foreach ($configurations as $name => $config) {
    echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>";
    echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>🔍 {$name}</h2>";
    
    echo "<div class='bg-gray-50 p-4 rounded mb-4'>";
    echo "<h3 class='font-semibold mb-2'>الإعدادات:</h3>";
    echo "<div class='grid grid-cols-1 md:grid-cols-2 gap-4 text-sm'>";
    echo "<div><strong>Host:</strong> {$config['host']}</div>";
    echo "<div><strong>Database:</strong> {$config['database']}</div>";
    echo "<div><strong>Username:</strong> {$config['username']}</div>";
    echo "<div><strong>Password:</strong> " . str_repeat('*', strlen($config['password'])) . "</div>";
    echo "</div>";
    echo "</div>";
    
    // Test connection
    try {
        $dsn = "mysql:host={$config['host']};dbname={$config['database']};charset=utf8mb4";
        $options = [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
        ];
        
        $pdo = new PDO($dsn, $config['username'], $config['password'], $options);
        
        // Test query
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM exhibitions");
        $result = $stmt->fetch();
        $exhibitionsCount = $result['count'];
        
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM booths");
        $result = $stmt->fetch();
        $boothsCount = $result['count'];
        
        echo "<div class='bg-green-50 border border-green-200 rounded-lg p-4'>";
        echo "<div class='flex items-center mb-3'>";
        echo "<div class='text-green-600 text-xl mr-3'>✅</div>";
        echo "<div>";
        echo "<h3 class='font-semibold text-green-800'>الاتصال ناجح!</h3>";
        echo "<p class='text-green-700'>تم الاتصال بقاعدة البيانات بنجاح</p>";
        echo "</div>";
        echo "</div>";
        
        echo "<div class='bg-white p-3 rounded border'>";
        echo "<h4 class='font-semibold mb-2'>إحصائيات قاعدة البيانات:</h4>";
        echo "<div class='grid grid-cols-2 gap-4 text-sm'>";
        echo "<div>عدد المعارض: <strong>{$exhibitionsCount}</strong></div>";
        echo "<div>عدد الأجنحة: <strong>{$boothsCount}</strong></div>";
        echo "</div>";
        echo "</div>";
        
        if ($name === 'الإعدادات الحالية (includes/database.php)') {
            echo "<div class='mt-4'>";
            echo "<button onclick='useThisConfig()' class='bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700'>✅ استخدام هذه الإعدادات</button>";
            echo "</div>";
        }
        
        echo "</div>";
        
    } catch (PDOException $e) {
        echo "<div class='bg-red-50 border border-red-200 rounded-lg p-4'>";
        echo "<div class='flex items-center mb-3'>";
        echo "<div class='text-red-600 text-xl mr-3'>❌</div>";
        echo "<div>";
        echo "<h3 class='font-semibold text-red-800'>فشل الاتصال</h3>";
        echo "<p class='text-red-700'>لم يتم الاتصال بقاعدة البيانات</p>";
        echo "</div>";
        echo "</div>";
        
        echo "<div class='bg-white p-3 rounded border'>";
        echo "<h4 class='font-semibold mb-2 text-red-700'>رسالة الخطأ:</h4>";
        echo "<code class='text-sm text-red-600'>" . htmlspecialchars($e->getMessage()) . "</code>";
        echo "</div>";
        echo "</div>";
    }
    
    echo "</div>";
}

// Manual configuration form
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>⚙️ إعدادات يدوية</h2>";

echo "<form method='POST' class='space-y-4'>";
echo "<div class='grid grid-cols-1 md:grid-cols-2 gap-4'>";

echo "<div>";
echo "<label class='block text-sm font-medium text-gray-700 mb-2'>Host:</label>";
echo "<input type='text' name='host' value='localhost' class='w-full px-3 py-2 border border-gray-300 rounded-md'>";
echo "</div>";

echo "<div>";
echo "<label class='block text-sm font-medium text-gray-700 mb-2'>Database Name:</label>";
echo "<input type='text' name='database' value='u404269408_' class='w-full px-3 py-2 border border-gray-300 rounded-md'>";
echo "</div>";

echo "<div>";
echo "<label class='block text-sm font-medium text-gray-700 mb-2'>Username:</label>";
echo "<input type='text' name='username' value='u404269408_' class='w-full px-3 py-2 border border-gray-300 rounded-md'>";
echo "</div>";

echo "<div>";
echo "<label class='block text-sm font-medium text-gray-700 mb-2'>Password:</label>";
echo "<input type='password' name='password' class='w-full px-3 py-2 border border-gray-300 rounded-md'>";
echo "</div>";

echo "</div>";

echo "<div class='text-center'>";
echo "<button type='submit' name='test_manual' class='bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700'>🔍 اختبار الإعدادات</button>";
echo "</div>";

echo "</form>";

// Test manual configuration
if (isset($_POST['test_manual'])) {
    $manualConfig = [
        'host' => $_POST['host'],
        'database' => $_POST['database'],
        'username' => $_POST['username'],
        'password' => $_POST['password']
    ];
    
    echo "<div class='mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4'>";
    echo "<h3 class='font-semibold mb-3 text-blue-700'>نتيجة الاختبار اليدوي:</h3>";
    
    try {
        $dsn = "mysql:host={$manualConfig['host']};dbname={$manualConfig['database']};charset=utf8mb4";
        $pdo = new PDO($dsn, $manualConfig['username'], $manualConfig['password']);
        
        echo "<div class='bg-green-50 border border-green-200 rounded p-3'>";
        echo "<p class='text-green-700'>✅ الاتصال ناجح! يمكنك استخدام هذه الإعدادات.</p>";
        echo "</div>";
        
        echo "<div class='mt-4 bg-white p-3 rounded border'>";
        echo "<h4 class='font-semibold mb-2'>الكود المطلوب لملف includes/database.php:</h4>";
        echo "<pre class='text-sm bg-gray-100 p-3 rounded overflow-x-auto'>";
        echo "\$host = '{$manualConfig['host']}';\n";
        echo "\$database = '{$manualConfig['database']}';\n";
        echo "\$username = '{$manualConfig['username']}';\n";
        echo "\$password = '{$manualConfig['password']}';";
        echo "</pre>";
        echo "</div>";
        
    } catch (PDOException $e) {
        echo "<div class='bg-red-50 border border-red-200 rounded p-3'>";
        echo "<p class='text-red-700'>❌ فشل الاتصال: " . htmlspecialchars($e->getMessage()) . "</p>";
        echo "</div>";
    }
    
    echo "</div>";
}

echo "</div>";

// Instructions
echo "<div class='bg-yellow-50 border border-yellow-200 rounded-lg p-6'>";
echo "<h2 class='text-xl font-bold text-yellow-700 mb-4'>📋 تعليمات الحصول على الإعدادات الصحيحة</h2>";

echo "<div class='space-y-4'>";

echo "<div class='bg-white p-4 rounded border'>";
echo "<h3 class='font-semibold mb-2'>1. من لوحة تحكم Hostinger:</h3>";
echo "<ul class='text-sm space-y-1 list-disc list-inside ml-4'>";
echo "<li>اذهب إلى Databases → MySQL Databases</li>";
echo "<li>ابحث عن قاعدة البيانات التي تحتوي على جداول exhibitions و booths</li>";
echo "<li>انسخ اسم قاعدة البيانات واسم المستخدم</li>";
echo "<li>استخدم كلمة المرور التي حددتها عند إنشاء قاعدة البيانات</li>";
echo "</ul>";
echo "</div>";

echo "<div class='bg-white p-4 rounded border'>";
echo "<h3 class='font-semibold mb-2'>2. من phpMyAdmin:</h3>";
echo "<ul class='text-sm space-y-1 list-disc list-inside ml-4'>";
echo "<li>اذهب إلى phpMyAdmin من لوحة تحكم Hostinger</li>";
echo "<li>ابحث عن قاعدة البيانات التي تحتوي على الجداول المطلوبة</li>";
echo "<li>تأكد من وجود جداول: exhibitions, booths, users</li>";
echo "</ul>";
echo "</div>";

echo "<div class='bg-white p-4 rounded border'>";
echo "<h3 class='font-semibold mb-2'>3. أسماء قواعد البيانات المحتملة:</h3>";
echo "<ul class='text-sm space-y-1 list-disc list-inside ml-4'>";
echo "<li>u404269408_seasonexpodb</li>";
echo "<li>u404269408_season_expo</li>";
echo "<li>u404269408_seasonexpo</li>";
echo "<li>u404269408_season</li>";
echo "</ul>";
echo "</div>";

echo "</div>";
echo "</div>";

echo "</div>";

echo "<script>";
echo "function useThisConfig() {";
echo "  alert('ممتاز! هذه الإعدادات تعمل بشكل صحيح. لا تحتاج لتغيير أي شيء.');";
echo "}";
echo "</script>";

echo "</body>";
echo "</html>";
?>

<?php
// Exhibition 2 - Simple Display
// This file displays exhibition 2 details

session_start();

// Get language
$currentLang = $_SESSION['language'] ?? $_COOKIE['language'] ?? $_GET['lang'] ?? 'ar';

// Mock data for Exhibition 2
$exhibition = (object) [
    'id' => 2,
    'title' => 'معرض الصحة والجمال 2025',
    'description' => 'أحدث منتجات الصحة والجمال والعناية الشخصية',
    'start_date' => '2025-04-10',
    'end_date' => '2025-04-15',
    'venue_name' => 'مركز الكويت الدولي للمعارض',
    'venue_address' => 'مشرف، الكويت',
    'status' => 'upcoming',
    'featured_image' => 'https://picsum.photos/1200/600?random=health1'
];

// Mock booths data
$booths = [
    (object) [
        'id' => 201,
        'name' => 'جناح A-201',
        'area' => 25,
        'location' => 'القاعة الرئيسية - الصف الأول',
        'price' => 1500,
        'status' => 'available',
        'description' => 'جناح مميز في موقع استراتيجي مع إطلالة على المدخل الرئيسي'
    ],
    (object) [
        'id' => 202,
        'name' => 'جناح B-202',
        'area' => 30,
        'location' => 'القاعة الرئيسية - الصف الثاني',
        'price' => 1800,
        'status' => 'available',
        'description' => 'جناح واسع مع مساحة إضافية للعرض والتخزين'
    ],
    (object) [
        'id' => 203,
        'name' => 'جناح C-203',
        'area' => 20,
        'location' => 'القاعة الفرعية - زاوية',
        'price' => 1200,
        'status' => 'available',
        'description' => 'جناح اقتصادي مناسب للشركات الناشئة'
    ]
];

$availableBooths = collect($booths)->where('status', 'available');
?>

<!DOCTYPE html>
<html lang="<?= $currentLang ?>" dir="<?= $currentLang === 'ar' ? 'rtl' : 'ltr' ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $exhibition->title ?> - Season Expo Kuwait</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .hero-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .booth-card {
            transition: all 0.3s ease;
        }
        .booth-card:hover {
            transform: translateY(-5px);
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg fixed w-full top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="/">
                        <img src="/images/logo.png" alt="Season Expo Kuwait" style="height: 40px; width: auto;" class="hover:opacity-80 transition-opacity">
                    </a>
                </div>
                <div class="flex items-center space-x-reverse space-x-4">
                    <a href="/" class="text-gray-600 hover:text-gray-900">الصفحة الرئيسية</a>
                    <a href="/exhibitions-simple.php" class="text-blue-600 font-semibold">المعارض</a>
                    <a href="/dashboard" class="text-gray-600 hover:text-gray-900">حسابي</a>
                    <a href="/signatures" class="text-gray-600 hover:text-gray-900">التوقيعات الرقمية</a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="pt-16">
        <div class="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
            <!-- Exhibition Header -->
            <div class="bg-white rounded-lg shadow-lg overflow-hidden mb-8">
                <div class="h-64 bg-cover bg-center" style="background-image: url('<?= $exhibition->featured_image ?>');">
                    <div class="h-full bg-black bg-opacity-50 flex items-center justify-center">
                        <div class="text-center text-white">
                            <h1 class="text-4xl font-bold mb-4"><?= $exhibition->title ?></h1>
                            <p class="text-xl"><?= $exhibition->description ?></p>
                        </div>
                    </div>
                </div>
                
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div class="text-center">
                            <div class="text-2xl mb-2">📅</div>
                            <h3 class="font-semibold mb-1">تاريخ المعرض</h3>
                            <p class="text-gray-600"><?= date('d/m/Y', strtotime($exhibition->start_date)) ?> - <?= date('d/m/Y', strtotime($exhibition->end_date)) ?></p>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl mb-2">📍</div>
                            <h3 class="font-semibold mb-1">المكان</h3>
                            <p class="text-gray-600"><?= $exhibition->venue_name ?></p>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl mb-2">🎯</div>
                            <h3 class="font-semibold mb-1">الحالة</h3>
                            <span class="inline-block px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                                قريباً
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistics -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                <div class="bg-white p-6 rounded-lg shadow-lg border-t-4" style="border-top-color: #2C3E50;">
                    <div class="text-3xl font-bold mb-2" style="color: #2C3E50;"><?= count($booths) ?></div>
                    <div class="font-semibold" style="color: #34495E;">إجمالي الأجنحة</div>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-lg border-t-4 border-green-500">
                    <div class="text-3xl font-bold text-green-600 mb-2"><?= $availableBooths->count() ?></div>
                    <div class="text-green-700 font-semibold">أجنحة متاحة</div>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-lg border-t-4 border-blue-500">
                    <div class="text-3xl font-bold text-blue-600 mb-2"><?= collect($booths)->min('price') ?></div>
                    <div class="text-blue-700 font-semibold">أقل سعر (KWD)</div>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-lg border-t-4 border-purple-500">
                    <div class="text-3xl font-bold text-purple-600 mb-2"><?= collect($booths)->sum('area') ?></div>
                    <div class="text-purple-700 font-semibold">إجمالي المساحة (م²)</div>
                </div>
            </div>

            <!-- Available Booths -->
            <?php if ($availableBooths->count() > 0): ?>
            <div id="booking" class="bg-white rounded-lg shadow-lg p-6 mb-8">
                <h2 class="text-2xl font-bold text-gray-900 mb-6">🎯 الأجنحة المتاحة</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <?php foreach ($availableBooths as $booth): ?>
                    <div class="booth-card border-2 rounded-lg p-4 hover:shadow-lg transition-all duration-300"
                         style="border-color: #2C3E50; background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);">
                        <div class="flex justify-between items-start mb-3">
                            <h3 class="font-bold text-lg" style="color: #2C3E50;"><?= $booth->name ?></h3>
                            <span class="px-2 py-1 text-xs rounded-full text-white"
                                  style="background: #27AE60;">متاح</span>
                        </div>
                        <p class="mb-3" style="color: #34495E;"><?= $booth->description ?></p>
                        <div class="space-y-2 text-sm">
                            <div style="color: #2C3E50;"><span class="font-semibold">📏 المساحة:</span> <?= $booth->area ?> م²</div>
                            <div style="color: #2C3E50;"><span class="font-semibold">📍 الموقع:</span> <?= $booth->location ?></div>
                            <div class="price font-bold text-lg" style="color: #E74C3C; font-weight: bold;">💰 <?= number_format($booth->price) ?> KWD</div>
                        </div>
                        <a href="/booking-with-signature.php?booth_id=<?= $booth->id ?>&exhibition_id=<?= $exhibition->id ?>"
                           class="block w-full mt-4 text-white py-2 px-4 rounded transition-colors text-center font-semibold"
                           style="background: #2C3E50; color: white;"
                           onmouseover="this.style.background='#1A252F'"
                           onmouseout="this.style.background='#2C3E50'">
                            احجز الآن (مع الإقرار والتعهد)
                        </a>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
            <?php else: ?>
            <div class="bg-white rounded-lg shadow-lg p-8 text-center">
                <div class="text-6xl mb-4">🏢</div>
                <h3 class="text-xl font-bold text-gray-900 mb-2">لا توجد أجنحة متاحة حالياً</h3>
                <p class="text-gray-600">جميع الأجنحة محجوزة أو سيتم الإعلان عن أجنحة جديدة قريباً</p>
            </div>
            <?php endif; ?>

            <!-- Navigation -->
            <div class="text-center space-x-reverse space-x-4">
                <a href="/exhibitions-simple.php" class="bg-gray-600 text-white px-6 py-3 rounded-lg hover:bg-gray-700">
                    ← العودة للمعارض
                </a>
                <a href="/" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700">
                    🏠 الصفحة الرئيسية
                </a>
            </div>
        </div>
    </div>
</body>
</html>

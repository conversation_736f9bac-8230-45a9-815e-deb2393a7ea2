<?php

namespace App\Http\Controllers;

use App\Models\Media;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;

class MediaController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $type = $request->get('type');
        $category = $request->get('category');

        $media = Media::query()
            ->when($type, fn($q) => $q->byType($type))
            ->when($category, fn($q) => $q->byCategory($category))
            ->ordered()
            ->paginate(12);

        return Inertia::render('Media/Index', [
            'media' => $media,
            'types' => Media::getTypes(),
            'filters' => [
                'type' => $type,
                'category' => $category,
            ],
            'locale' => app()->getLocale(),
            'translations' => [
                'app' => __('app'),
            ],
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return Inertia::render('Media/Create', [
            'types' => Media::getTypes(),
            'locale' => app()->getLocale(),
            'translations' => [
                'app' => __('app'),
            ],
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'file' => 'required|image|mimes:jpeg,png,jpg,gif,webp|max:5120', // 5MB max
            'type' => 'required|string|in:' . implode(',', array_keys(Media::getTypes())),
            'category' => 'nullable|string|max:255',
            'title' => 'nullable|string|max:255',
            'description' => 'nullable|string|max:1000',
            'alt_text' => 'nullable|string|max:255',
            'sort_order' => 'nullable|integer|min:0',
        ]);

        $file = $request->file('file');
        $fileName = time() . '_' . $file->getClientOriginalName();
        $filePath = $file->storeAs('media', $fileName, 'public');

        $metadata = [
            'title' => $request->title,
            'description' => $request->description,
            'alt_text' => $request->alt_text,
        ];

        Media::create([
            'name' => $request->name,
            'file_name' => $fileName,
            'file_path' => $filePath,
            'mime_type' => $file->getMimeType(),
            'file_size' => $file->getSize(),
            'type' => $request->type,
            'category' => $request->category,
            'metadata' => $metadata,
            'sort_order' => $request->sort_order ?? 0,
        ]);

        return redirect()->route('media.index')->with('success', 'Media uploaded successfully!');
    }

    /**
     * Display the specified resource.
     */
    public function show(Media $media)
    {
        return Inertia::render('Media/Show', [
            'media' => $media,
            'locale' => app()->getLocale(),
            'translations' => [
                'app' => __('app'),
            ],
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Media $media)
    {
        return Inertia::render('Media/Edit', [
            'media' => $media,
            'types' => Media::getTypes(),
            'locale' => app()->getLocale(),
            'translations' => [
                'app' => __('app'),
            ],
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Media $media)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'file' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:5120',
            'type' => 'required|string|in:' . implode(',', array_keys(Media::getTypes())),
            'category' => 'nullable|string|max:255',
            'title' => 'nullable|string|max:255',
            'description' => 'nullable|string|max:1000',
            'alt_text' => 'nullable|string|max:255',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
        ]);

        $updateData = [
            'name' => $request->name,
            'type' => $request->type,
            'category' => $request->category,
            'metadata' => [
                'title' => $request->title,
                'description' => $request->description,
                'alt_text' => $request->alt_text,
            ],
            'sort_order' => $request->sort_order ?? 0,
            'is_active' => $request->boolean('is_active', true),
        ];

        // Handle file upload if new file is provided
        if ($request->hasFile('file')) {
            // Delete old file
            Storage::disk('public')->delete($media->file_path);

            $file = $request->file('file');
            $fileName = time() . '_' . $file->getClientOriginalName();
            $filePath = $file->storeAs('media', $fileName, 'public');

            $updateData['file_name'] = $fileName;
            $updateData['file_path'] = $filePath;
            $updateData['mime_type'] = $file->getMimeType();
            $updateData['file_size'] = $file->getSize();
        }

        $media->update($updateData);

        return redirect()->route('media.index')->with('success', 'Media updated successfully!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Media $media)
    {
        // Delete file from storage
        Storage::disk('public')->delete($media->file_path);

        // Delete database record
        $media->delete();

        return redirect()->route('media.index')->with('success', 'Media deleted successfully!');
    }

    /**
     * Get media by type for API usage
     */
    public function getByType($type)
    {
        $media = Media::active()
            ->byType($type)
            ->ordered()
            ->get();

        return response()->json($media);
    }
}

<?php
echo "<h1>Routes File Content</h1>";

$webRoutesPath = dirname(__DIR__) . '/routes/web.php';

if (file_exists($webRoutesPath)) {
    echo "<h2>routes/web.php exists</h2>";
    echo "File size: " . filesize($webRoutesPath) . " bytes<br>";
    echo "Last modified: " . date('Y-m-d H:i:s', filemtime($webRoutesPath)) . "<br><br>";
    
    echo "<h2>Complete routes/web.php content:</h2>";
    echo "<pre style='background: #f5f5f5; padding: 15px; border-radius: 5px; overflow-x: auto; max-height: 500px; overflow-y: auto;'>";
    echo htmlspecialchars(file_get_contents($webRoutesPath));
    echo "</pre>";
    
    // Check for key components
    $content = file_get_contents($webRoutesPath);
    
    echo "<h2>Route Analysis:</h2>";
    
    if (strpos($content, "Route::get('/', ") !== false || strpos($content, 'Route::get("/", ') !== false) {
        echo "✅ Home route (/) found<br>";
    } else {
        echo "❌ Home route (/) not found<br>";
    }
    
    if (strpos($content, 'HomeController') !== false) {
        echo "✅ HomeController referenced<br>";
    } else {
        echo "❌ HomeController not referenced<br>";
    }
    
    if (strpos($content, 'exhibitions') !== false) {
        echo "✅ Exhibition routes found<br>";
    } else {
        echo "❌ Exhibition routes not found<br>";
    }
    
} else {
    echo "<h2>❌ routes/web.php does not exist!</h2>";
    echo "<p>This is the problem! The routes file is missing.</p>";
    
    echo "<h2>Expected routes/web.php should contain:</h2>";
    echo "<pre style='background: #ffeeee; padding: 15px; border-radius: 5px;'>";
    echo htmlspecialchars("<?php

use App\Http\Controllers\HomeController;
use App\Http\Controllers\ExhibitionController;
use App\Http\Controllers\BookingController;
use Illuminate\Support\Facades\Route;

// Home route
Route::get('/', [HomeController::class, 'index'])->name('home');

// Exhibition routes
Route::get('/exhibitions', [ExhibitionController::class, 'index'])->name('exhibitions.index');
Route::get('/exhibitions/{exhibition:slug}', [ExhibitionController::class, 'show'])->name('exhibitions.show');

// Other routes...
");
    echo "</pre>";
}

// Check HomeController
echo "<h2>HomeController Check:</h2>";
$homeControllerPath = dirname(__DIR__) . '/app/Http/Controllers/HomeController.php';
if (file_exists($homeControllerPath)) {
    echo "✅ HomeController exists<br>";
    echo "File size: " . filesize($homeControllerPath) . " bytes<br>";
} else {
    echo "❌ HomeController missing!<br>";
}
?>

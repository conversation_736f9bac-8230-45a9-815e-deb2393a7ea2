<script setup>
import { Head, Link, useForm } from '@inertiajs/vue3';
import { ref } from 'vue';
import LanguageSwitcher from '@/components/LanguageSwitcher.vue';

const props = defineProps({
    types: Object,
    locale: String,
    translations: Object,
});

const t = (key) => {
    return props.translations?.app?.[key] || key;
};

const isRTL = props.locale === 'ar';

const form = useForm({
    name: '',
    file: null,
    type: '',
    category: '',
    title: '',
    description: '',
    alt_text: '',
    sort_order: 0,
});

const fileInput = ref(null);
const previewUrl = ref(null);

const handleFileChange = (event) => {
    const file = event.target.files[0];
    if (file) {
        form.file = file;
        
        // Generate preview
        const reader = new FileReader();
        reader.onload = (e) => {
            previewUrl.value = e.target.result;
        };
        reader.readAsDataURL(file);

        // Auto-fill name if empty
        if (!form.name) {
            form.name = file.name.replace(/\.[^/.]+$/, ""); // Remove extension
        }
    }
};

const removeFile = () => {
    form.file = null;
    previewUrl.value = null;
    if (fileInput.value) {
        fileInput.value.value = '';
    }
};

const submit = () => {
    form.post(route('media.store'), {
        forceFormData: true,
    });
};
</script>

<template>
    <Head :title="t('upload_new_media')" />

    <div class="min-h-screen bg-gray-50" :dir="isRTL ? 'rtl' : 'ltr'">
        <!-- Fixed Navigation -->
        <nav class="fixed top-0 left-0 right-0 fixed-nav z-50">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <div class="flex items-center">
                        <Link href="/" class="text-2xl font-bold text-blue-600 hover:text-blue-700 transition-colors">
                            {{ t('site_name') }}
                        </Link>
                    </div>
                    <div class="flex items-center" :class="isRTL ? 'space-x-reverse space-x-4' : 'space-x-4'">
                        <LanguageSwitcher :current-locale="locale" />
                        <Link href="/media" class="nav-link text-gray-600">{{ t('media_management') }}</Link>
                        <Link href="/dashboard" class="nav-link text-gray-600">{{ t('dashboard') }}</Link>
                    </div>
                </div>
            </div>
        </nav>

        <div class="py-12 pt-24">
            <div class="mx-auto max-w-4xl sm:px-6 lg:px-8">
                <!-- Header -->
                <div class="mb-8">
                    <div class="flex items-center gap-4">
                        <Link 
                            :href="route('media.index')"
                            class="text-gray-600 hover:text-gray-900"
                        >
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="isRTL ? 'M9 5l7 7-7 7' : 'M15 19l-7-7 7-7'" />
                            </svg>
                        </Link>
                        <div>
                            <h1 class="text-3xl font-bold text-gray-900">{{ t('upload_new_media') }}</h1>
                            <p class="text-gray-600 mt-2">{{ t('upload_media_description') }}</p>
                        </div>
                    </div>
                </div>

                <!-- Form -->
                <div class="bg-white rounded-lg shadow-sm p-8">
                    <form @submit.prevent="submit" class="space-y-6">
                        <!-- File Upload -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                {{ t('select_file') }} *
                            </label>
                            
                            <!-- File Input -->
                            <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-lg hover:border-gray-400 transition-colors">
                                <div class="space-y-1 text-center">
                                    <div v-if="!previewUrl">
                                        <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                            <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                        </svg>
                                        <div class="flex text-sm text-gray-600">
                                            <label for="file-upload" class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500">
                                                <span>{{ t('upload_file') }}</span>
                                                <input 
                                                    id="file-upload" 
                                                    ref="fileInput"
                                                    name="file-upload" 
                                                    type="file" 
                                                    class="sr-only" 
                                                    accept="image/*"
                                                    @change="handleFileChange"
                                                />
                                            </label>
                                            <p class="pl-1">{{ t('or_drag_and_drop') }}</p>
                                        </div>
                                        <p class="text-xs text-gray-500">PNG, JPG, GIF {{ t('up_to') }} 5MB</p>
                                    </div>
                                    
                                    <!-- Preview -->
                                    <div v-else class="relative">
                                        <img :src="previewUrl" alt="Preview" class="mx-auto h-32 w-auto rounded-lg" />
                                        <button 
                                            type="button"
                                            @click="removeFile"
                                            class="absolute top-0 right-0 -mt-2 -mr-2 bg-red-600 text-white rounded-full p-1 hover:bg-red-700"
                                        >
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div v-if="form.errors.file" class="mt-1 text-sm text-red-600">{{ form.errors.file }}</div>
                        </div>

                        <!-- Basic Information -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Name -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    {{ t('name') }} *
                                </label>
                                <input 
                                    v-model="form.name"
                                    type="text" 
                                    class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                                    :placeholder="t('enter_media_name')"
                                />
                                <div v-if="form.errors.name" class="mt-1 text-sm text-red-600">{{ form.errors.name }}</div>
                            </div>

                            <!-- Type -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    {{ t('type') }} *
                                </label>
                                <select 
                                    v-model="form.type"
                                    class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                                >
                                    <option value="">{{ t('select_type') }}</option>
                                    <option v-for="(label, value) in types" :key="value" :value="value">
                                        {{ label }}
                                    </option>
                                </select>
                                <div v-if="form.errors.type" class="mt-1 text-sm text-red-600">{{ form.errors.type }}</div>
                            </div>

                            <!-- Category -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    {{ t('category') }}
                                </label>
                                <input 
                                    v-model="form.category"
                                    type="text" 
                                    class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                                    :placeholder="t('enter_category')"
                                />
                                <div v-if="form.errors.category" class="mt-1 text-sm text-red-600">{{ form.errors.category }}</div>
                            </div>

                            <!-- Sort Order -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    {{ t('sort_order') }}
                                </label>
                                <input 
                                    v-model="form.sort_order"
                                    type="number" 
                                    min="0"
                                    class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                                    :placeholder="t('enter_sort_order')"
                                />
                                <div v-if="form.errors.sort_order" class="mt-1 text-sm text-red-600">{{ form.errors.sort_order }}</div>
                            </div>
                        </div>

                        <!-- Metadata -->
                        <div class="space-y-4">
                            <h3 class="text-lg font-medium text-gray-900">{{ t('additional_information') }}</h3>
                            
                            <!-- Title -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    {{ t('title') }}
                                </label>
                                <input 
                                    v-model="form.title"
                                    type="text" 
                                    class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                                    :placeholder="t('enter_title')"
                                />
                                <div v-if="form.errors.title" class="mt-1 text-sm text-red-600">{{ form.errors.title }}</div>
                            </div>

                            <!-- Description -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    {{ t('description') }}
                                </label>
                                <textarea 
                                    v-model="form.description"
                                    rows="3"
                                    class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                                    :placeholder="t('enter_description')"
                                ></textarea>
                                <div v-if="form.errors.description" class="mt-1 text-sm text-red-600">{{ form.errors.description }}</div>
                            </div>

                            <!-- Alt Text -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    {{ t('alt_text') }}
                                </label>
                                <input 
                                    v-model="form.alt_text"
                                    type="text" 
                                    class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                                    :placeholder="t('enter_alt_text')"
                                />
                                <p class="mt-1 text-sm text-gray-500">{{ t('alt_text_description') }}</p>
                                <div v-if="form.errors.alt_text" class="mt-1 text-sm text-red-600">{{ form.errors.alt_text }}</div>
                            </div>
                        </div>

                        <!-- Actions -->
                        <div class="flex items-center justify-between pt-6 border-t border-gray-200">
                            <Link 
                                :href="route('media.index')"
                                class="bg-gray-100 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-200 transition-colors"
                            >
                                {{ t('cancel') }}
                            </Link>
                            
                            <button 
                                type="submit"
                                :disabled="form.processing"
                                class="btn-primary bg-blue-600 text-white px-6 py-2 rounded-lg font-semibold hover:bg-blue-700 disabled:opacity-50"
                            >
                                <span v-if="form.processing">{{ t('uploading') }}...</span>
                                <span v-else>{{ t('upload_media') }}</span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</template>

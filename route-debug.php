<?php
require '../vendor/autoload.php';

$app = require_once '../bootstrap/app.php';

echo "<h1>Route Debug - Season Expo</h1>";

try {
    // Get all routes
    $routes = \Illuminate\Support\Facades\Route::getRoutes();
    
    echo "<h2>Total Routes Found: " . count($routes) . "</h2>";
    
    // Check for specific Season Expo routes
    $seasonExpoRoutes = [];
    $homeRoute = null;
    
    foreach ($routes as $route) {
        $uri = $route->uri();
        $action = $route->getActionName();
        
        if ($uri === '/') {
            $homeRoute = $route;
        }
        
        if (strpos($action, 'HomeController') !== false || 
            strpos($action, 'ExhibitionController') !== false ||
            strpos($uri, 'exhibition') !== false ||
            strpos($uri, 'dashboard') !== false) {
            $seasonExpoRoutes[] = [
                'uri' => $uri,
                'action' => $action,
                'methods' => implode('|', $route->methods())
            ];
        }
    }
    
    echo "<h2>Home Route (/):</h2>";
    if ($homeRoute) {
        echo "✅ Found home route<br>";
        echo "Action: " . $homeRoute->getActionName() . "<br>";
        echo "Methods: " . implode(', ', $homeRoute->methods()) . "<br>";
    } else {
        echo "❌ No home route found - this is the problem!<br>";
    }
    
    echo "<h2>Season Expo Routes Found:</h2>";
    if (empty($seasonExpoRoutes)) {
        echo "❌ No Season Expo routes found!<br>";
        echo "<strong>This means your routes/web.php is not loading properly.</strong><br>";
    } else {
        foreach ($seasonExpoRoutes as $route) {
            echo "✅ {$route['methods']} /{$route['uri']} → {$route['action']}<br>";
        }
    }
    
    // Check if routes/web.php exists
    echo "<h2>Routes File Check:</h2>";
    $webRoutesPath = dirname(__DIR__) . '/routes/web.php';
    if (file_exists($webRoutesPath)) {
        echo "✅ routes/web.php exists<br>";
        echo "File size: " . filesize($webRoutesPath) . " bytes<br>";
        
        // Show first few lines
        $content = file_get_contents($webRoutesPath);
        $lines = explode("\n", $content);
        echo "<h3>First 10 lines of routes/web.php:</h3>";
        echo "<pre style='background: #f5f5f5; padding: 10px;'>";
        for ($i = 0; $i < min(10, count($lines)); $i++) {
            echo htmlspecialchars($lines[$i]) . "\n";
        }
        echo "</pre>";
        
        // Check for Season Expo routes
        if (strpos($content, 'HomeController') !== false) {
            echo "✅ HomeController found in routes<br>";
        } else {
            echo "❌ HomeController not found in routes<br>";
        }
        
    } else {
        echo "❌ routes/web.php missing!<br>";
    }
    
    // Check controllers
    echo "<h2>Controller Check:</h2>";
    $homeControllerPath = dirname(__DIR__) . '/app/Http/Controllers/HomeController.php';
    if (file_exists($homeControllerPath)) {
        echo "✅ HomeController exists<br>";
    } else {
        echo "❌ HomeController missing!<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
}

echo "<h2>Quick Fix:</h2>";
echo "<p>If no Season Expo routes are found, we need to:</p>";
echo "<ol>";
echo "<li>Check routes/web.php content</li>";
echo "<li>Ensure HomeController exists</li>";
echo "<li>Clear route cache</li>";
echo "<li>Restart the application</li>";
echo "</ol>";
?>

<?php
// Fix Slider Database Error
// This file creates the missing slider_images table

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='utf-8'>";
echo "<title>إصلاح قاعدة بيانات السلايد - Season Expo Kuwait</title>";
echo "<script src='https://cdn.tailwindcss.com'></script>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }";
echo "</style>";
echo "</head>";
echo "<body class='bg-gray-50 p-8'>";

echo "<div class='max-w-4xl mx-auto'>";
echo "<h1 class='text-3xl font-bold mb-8' style='color: #2C3E50;'>🔧 إصلاح قاعدة بيانات السلايد</h1>";

try {
    // Connect to database
    $pdo = new PDO('sqlite:' . __DIR__ . '/database/database.sqlite');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<div class='bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4'>";
    echo "✅ تم الاتصال بقاعدة البيانات بنجاح";
    echo "</div>";

    // Check if table exists
    $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='slider_images'");
    $tableExists = $stmt->fetch();

    if ($tableExists) {
        echo "<div class='bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-4'>";
        echo "⚠️ جدول slider_images موجود بالفعل";
        echo "</div>";
        
        // Check if table has data
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM slider_images");
        $count = $stmt->fetch()['count'];
        
        echo "<div class='bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded mb-4'>";
        echo "📊 عدد الصور الحالية: {$count}";
        echo "</div>";
        
    } else {
        echo "<div class='bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4'>";
        echo "❌ جدول slider_images غير موجود - سيتم إنشاؤه الآن";
        echo "</div>";

        // Create slider_images table
        $createTableSQL = "
        CREATE TABLE slider_images (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title VARCHAR(255) NOT NULL,
            description TEXT,
            image_path VARCHAR(500) NOT NULL,
            link_url VARCHAR(500),
            display_order INTEGER DEFAULT 0,
            is_active BOOLEAN DEFAULT 1,
            background_color VARCHAR(7) DEFAULT '#2C3E50',
            text_color VARCHAR(7) DEFAULT '#ffffff',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )";

        $pdo->exec($createTableSQL);

        echo "<div class='bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4'>";
        echo "✅ تم إنشاء جدول slider_images بنجاح";
        echo "</div>";

        // Insert sample data
        echo "<div class='bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded mb-4'>";
        echo "📝 إضافة بيانات تجريبية...";
        echo "</div>";

        $sampleData = [
            [
                'title' => 'مرحباً بكم في Season Expo Kuwait',
                'description' => 'منصة المعارض الرائدة في الكويت - احجز جناحك الآن',
                'image_path' => '/images/logo.png',
                'link_url' => '/exhibitions-simple.php',
                'display_order' => 1,
                'background_color' => '#2C3E50',
                'text_color' => '#ffffff'
            ],
            [
                'title' => 'معرض التكنولوجيا 2025',
                'description' => 'أحدث التقنيات والابتكارات التكنولوجية في معرض واحد',
                'image_path' => '/images/logo.png',
                'link_url' => '/exhibitions-1-simple.php',
                'display_order' => 2,
                'background_color' => '#34495E',
                'text_color' => '#ffffff'
            ],
            [
                'title' => 'احجز جناحك الآن',
                'description' => 'أجنحة متميزة بأسعار تنافسية ومواقع استراتيجية',
                'image_path' => '/images/logo.png',
                'link_url' => '/booking-with-signature.php?booth_id=1&exhibition_id=1',
                'display_order' => 3,
                'background_color' => '#1A252F',
                'text_color' => '#ffffff'
            ]
        ];

        $insertSQL = "INSERT INTO slider_images (title, description, image_path, link_url, display_order, background_color, text_color, created_at, updated_at) 
                      VALUES (?, ?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))";
        
        $stmt = $pdo->prepare($insertSQL);
        
        foreach ($sampleData as $data) {
            $stmt->execute([
                $data['title'],
                $data['description'],
                $data['image_path'],
                $data['link_url'],
                $data['display_order'],
                $data['background_color'],
                $data['text_color']
            ]);
        }

        echo "<div class='bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4'>";
        echo "✅ تم إضافة " . count($sampleData) . " صور تجريبية";
        echo "</div>";
    }

    // Display current data
    $stmt = $pdo->query("SELECT * FROM slider_images ORDER BY display_order");
    $slides = $stmt->fetchAll(PDO::FETCH_ASSOC);

    echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
    echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>📊 البيانات الحالية في السلايد</h2>";

    if (count($slides) > 0) {
        echo "<div class='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4'>";
        
        foreach ($slides as $slide) {
            echo "<div class='border border-gray-200 rounded-lg p-4' style='background: {$slide['background_color']};'>";
            echo "<div style='color: {$slide['text_color']};'>";
            echo "<h3 class='font-bold text-lg mb-2'>{$slide['title']}</h3>";
            echo "<p class='text-sm mb-3'>{$slide['description']}</p>";
            echo "<div class='text-xs opacity-75'>";
            echo "<div>الترتيب: {$slide['display_order']}</div>";
            echo "<div>الحالة: " . ($slide['is_active'] ? 'نشط' : 'غير نشط') . "</div>";
            echo "</div>";
            echo "</div>";
            echo "</div>";
        }
        
        echo "</div>";
    } else {
        echo "<div class='text-center text-gray-500 py-8'>";
        echo "لا توجد صور في السلايد";
        echo "</div>";
    }

    echo "</div>";

    // Test admin panel link
    echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
    echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>🧪 اختبار إدارة السلايد</h2>";
    echo "<p class='mb-4'>الآن يمكنك الذهاب إلى صفحة إدارة السلايد بدون أخطاء:</p>";
    echo "<a href='/admin-slider-management.php' class='text-white px-6 py-3 rounded-lg hover:opacity-80 inline-block' style='background: #2C3E50;'>🖼️ اختبار إدارة السلايد</a>";
    echo "</div>";

    // Success message
    echo "<div class='bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4'>";
    echo "<h3 class='font-bold text-lg mb-2'>🎉 تم إصلاح المشكلة بنجاح!</h3>";
    echo "<ul class='text-sm space-y-1'>";
    echo "<li>✅ تم إنشاء جدول slider_images</li>";
    echo "<li>✅ تم إضافة بيانات تجريبية</li>";
    echo "<li>✅ صفحة إدارة السلايد تعمل الآن</li>";
    echo "<li>✅ يمكنك إضافة وتعديل الصور</li>";
    echo "</ul>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div class='bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4'>";
    echo "❌ خطأ: " . $e->getMessage();
    echo "</div>";
    
    echo "<div class='bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-4'>";
    echo "<h3 class='font-bold mb-2'>الحلول المحتملة:</h3>";
    echo "<ul class='text-sm space-y-1'>";
    echo "<li>• تأكد من وجود ملف database/database.sqlite</li>";
    echo "<li>• تأكد من صلاحيات الكتابة على مجلد database/</li>";
    echo "<li>• تأكد من تثبيت SQLite على السيرفر</li>";
    echo "<li>• جرب إنشاء ملف قاعدة البيانات يدوياً</li>";
    echo "</ul>";
    echo "</div>";
}

// Navigation links
echo "<div class='text-center space-x-reverse space-x-4 mt-8'>";
echo "<a href='/admin-slider-management.php' class='text-white px-6 py-3 rounded-lg hover:opacity-80' style='background: #2C3E50;'>🖼️ إدارة السلايد</a>";
echo "<a href='/exhibitions-simple.php' class='bg-gray-600 text-white px-6 py-3 rounded-lg hover:bg-gray-700'>📋 المعارض</a>";
echo "<a href='/' class='bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700'>🏠 الصفحة الرئيسية</a>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>

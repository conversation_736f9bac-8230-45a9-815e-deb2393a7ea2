<?php
// Test to check if login page shows forgot password link

echo "<h1>اختب<PERSON>ر صفحة تسجيل الدخول</h1>";

echo "<h2>الروابط المتاحة:</h2>";
echo "<ul>";
echo "<li><a href='/login-simple' target='_blank'>صفحة تسجيل الدخول (يجب أن تحتوي على رابط نسيت كلمة المرور)</a></li>";
echo "<li><a href='/reset-password-form' target='_blank'>صفحة نسيت كلمة المرور مباشرة</a></li>";
echo "<li><a href='/register-simple' target='_blank'>صفحة التسجيل</a></li>";
echo "</ul>";

echo "<h2>ما يجب أن تراه في صفحة تسجيل الدخول:</h2>";
echo "<ul>";
echo "<li>رابط '🔑 نسيت كلمة المرور؟' بجانب تسمية 'كلمة المرور'</li>";
echo "<li>رابط '🔑 نسيت كلمة المرور؟' في قسم 'تذكرني'</li>";
echo "<li>كلا الرابطين يجب أن يؤديا إلى '/reset-password-form'</li>";
echo "</ul>";

echo "<h2>اختبار الروتات:</h2>";

// Test if routes exist
$routes_to_test = [
    '/login-simple' => 'صفحة تسجيل الدخول',
    '/reset-password-form' => 'صفحة نسيت كلمة المرور',
    '/register-simple' => 'صفحة التسجيل'
];

foreach ($routes_to_test as $route => $description) {
    $url = 'http://127.0.0.1:8001' . $route;
    
    // Use curl to test if route exists
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_NOBODY, true); // HEAD request only
    
    $result = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($httpCode == 200) {
        echo "<p style='color: green;'>✅ $description - يعمل بشكل صحيح</p>";
    } else {
        echo "<p style='color: red;'>❌ $description - لا يعمل (HTTP $httpCode)</p>";
    }
}

echo "<h2>تعليمات الاختبار:</h2>";
echo "<ol>";
echo "<li>انقر على رابط 'صفحة تسجيل الدخول' أعلاه</li>";
echo "<li>تأكد من وجود رابط 'نسيت كلمة المرور؟' في مكانين</li>";
echo "<li>انقر على الرابط للتأكد من أنه يؤدي إلى صفحة استعادة كلمة المرور</li>";
echo "<li>جرب إدخال بريد إلكتروني واختبار الوظيفة</li>";
echo "</ol>";

echo "<h2>ملاحظات مهمة:</h2>";
echo "<ul>";
echo "<li>تأكد من أن الخادم يعمل على المنفذ 8001</li>";
echo "<li>تأكد من وجود جدول password_reset_tokens في قاعدة البيانات</li>";
echo "<li>تأكد من إعدادات البريد الإلكتروني في ملف .env</li>";
echo "</ul>";

?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h1, h2 {
    color: #333;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
}

ul, ol {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    margin: 10px 0;
}

li {
    margin: 5px 0;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}
</style>

<?php
// English Language Route Handler
// This file handles the /en route from Laravel language switcher

session_start();

// Set language to English
$_SESSION['language'] = 'en';
$_SESSION['locale'] = 'en';

// Set persistent cookie
setcookie('language', 'en', time() + (30 * 24 * 60 * 60), '/');
setcookie('locale', 'en', time() + (30 * 24 * 60 * 60), '/');

// Redirect to homepage with language parameter
header('Location: /homepage-fixed.php?lang=en');
exit;
?>

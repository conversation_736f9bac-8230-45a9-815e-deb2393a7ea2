<?php
// Test slider functionality

echo "<h1>اختبار السلايد في الصفحة الرئيسية</h1>";

echo "<h2>التحسينات المطبقة:</h2>";
echo "<ul>";
echo "<li>✅ إضافة صور احتياطية متعددة لكل سلايد</li>";
echo "<li>✅ إضافة ألوان خلفية احتياطية لكل سلايد</li>";
echo "<li>✅ تحسين CSS للسلايد</li>";
echo "<li>✅ إضافة كلاسات مخصصة لكل سلايد</li>";
echo "</ul>";

echo "<h2>الصور المستخدمة:</h2>";
echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0;'>";

$slides = [
    [
        'title' => 'معرض التكنولوجيا',
        'images' => [
            'https://picsum.photos/1200/600?random=tech1',
            'https://images.unsplash.com/photo-1540575467063-178a50c2df87?w=1200&h=600&fit=crop&crop=center'
        ],
        'fallback_color' => '#1e40af',
        'class' => 'slide-tech'
    ],
    [
        'title' => 'معرض الصحة والجمال',
        'images' => [
            'https://picsum.photos/1200/600?random=health2',
            'https://images.unsplash.com/photo-**********-641a0ac8b55e?w=1200&h=600&fit=crop&crop=center'
        ],
        'fallback_color' => '#059669',
        'class' => 'slide-health'
    ],
    [
        'title' => 'معرض الأزياء والموضة',
        'images' => [
            'https://picsum.photos/1200/600?random=fashion3',
            'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=1200&h=600&fit=crop&crop=center'
        ],
        'fallback_color' => '#7c3aed',
        'class' => 'slide-fashion'
    ],
    [
        'title' => 'معرض الطعام والمشروبات',
        'images' => [
            'https://picsum.photos/1200/600?random=food4',
            'https://images.unsplash.com/photo-1414235077428-338989a2e8c0?w=1200&h=600&fit=crop&crop=center'
        ],
        'fallback_color' => '#ea580c',
        'class' => 'slide-food'
    ]
];

foreach ($slides as $slide) {
    echo "<div style='border: 1px solid #ddd; border-radius: 8px; padding: 15px; background: white;'>";
    echo "<h3>{$slide['title']}</h3>";
    echo "<p><strong>الكلاس:</strong> <code>{$slide['class']}</code></p>";
    echo "<p><strong>لون الخلفية الاحتياطي:</strong> <span style='display: inline-block; width: 20px; height: 20px; background: {$slide['fallback_color']}; border-radius: 3px; vertical-align: middle;'></span> {$slide['fallback_color']}</p>";
    echo "<p><strong>الصور:</strong></p>";
    echo "<ol>";
    foreach ($slide['images'] as $image) {
        echo "<li><a href='$image' target='_blank'>$image</a></li>";
    }
    echo "</ol>";
    echo "</div>";
}

echo "</div>";

echo "<h2>اختبار الصور:</h2>";
echo "<p>انقر على الروابط أعلاه لاختبار تحميل الصور. إذا لم تعمل الصورة الأولى، سيتم استخدام الصورة الثانية تلقائياً.</p>";

echo "<h2>الميزات الجديدة:</h2>";
echo "<ul>";
echo "<li><strong>صور متعددة:</strong> كل سلايد له صورتان احتياطيتان</li>";
echo "<li><strong>ألوان احتياطية:</strong> إذا لم تحمل الصور، سيظهر لون مناسب للموضوع</li>";
echo "<li><strong>CSS محسن:</strong> تحسينات في العرض والانتقالات</li>";
echo "<li><strong>استجابة أفضل:</strong> يعمل بشكل أفضل على جميع الأجهزة</li>";
echo "</ul>";

echo "<h2>اختبار الصفحة الرئيسية:</h2>";
echo "<p><a href='/' target='_blank' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>اذهب إلى الصفحة الرئيسية</a></p>";

echo "<h2>كيفية عمل النظام:</h2>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h3>ترتيب التحميل:</h3>";
echo "<ol>";
echo "<li>يحاول تحميل الصورة الأولى من Picsum</li>";
echo "<li>إذا فشل، يحاول تحميل الصورة الثانية من Unsplash</li>";
echo "<li>إذا فشل كلاهما، يظهر اللون الاحتياطي</li>";
echo "<li>النص والأزرار تظهر دائماً فوق الخلفية</li>";
echo "</ol>";
echo "</div>";

echo "<h2>CSS المضاف:</h2>";
echo "<pre style='background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto;'>";
echo htmlspecialchars('
/* Enhanced Slider Styles */
.slider-container {
    position: relative;
    overflow: hidden;
    height: 400px;
}

.slider-slide {
    min-width: 100%;
    height: 100%;
    position: relative;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    transition: all 0.3s ease;
}

.slider-slide::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
    z-index: 1;
}

.slider-content {
    position: relative;
    z-index: 2;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-align: center;
    padding: 20px;
}

/* Fallback colors for each slide */
.slide-tech { background-color: #1e40af; }
.slide-health { background-color: #059669; }
.slide-fashion { background-color: #7c3aed; }
.slide-food { background-color: #ea580c; }
');
echo "</pre>";

?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h1, h2, h3 {
    color: #333;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
}

code {
    background-color: #f8f9fa;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
}

ul, ol {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    margin: 10px 0;
}

pre {
    font-size: 12px;
    line-height: 1.4;
}
</style>

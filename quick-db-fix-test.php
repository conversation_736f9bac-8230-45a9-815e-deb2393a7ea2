<?php
// Quick Database Fix Test
// Test the corrected database connection

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='utf-8'>";
echo "<title>اختبار إصلاح قاعدة البيانات السريع</title>";
echo "<script src='https://cdn.tailwindcss.com'></script>";
echo "</head>";
echo "<body class='bg-gray-50 p-8'>";

echo "<div class='max-w-2xl mx-auto'>";
echo "<h1 class='text-2xl font-bold mb-6'>🔧 اختبار إصلاح قاعدة البيانات السريع</h1>";

// Test includes/database.php
echo "<div class='bg-white rounded-lg shadow p-6 mb-6'>";
echo "<h2 class='text-lg font-bold mb-4'>🔍 اختبار includes/database.php</h2>";

try {
    require_once 'includes/database.php';
    $pdo = getDatabaseConnection();
    
    // Test basic connection
    $stmt = $pdo->query("SELECT 1 as test");
    $result = $stmt->fetch();
    
    if ($result['test'] == 1) {
        echo "<div class='bg-green-50 border border-green-200 rounded p-4 mb-4'>";
        echo "<h3 class='font-bold text-green-800 mb-2'>✅ الاتصال ناجح!</h3>";
        echo "<p class='text-green-700'>تم الاتصال بقاعدة البيانات بنجاح</p>";
        echo "</div>";
        
        // Test exhibitions table
        try {
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM exhibitions");
            $exhibitions = $stmt->fetch()['count'];
            
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM booths");
            $booths = $stmt->fetch()['count'];
            
            echo "<div class='bg-blue-50 border border-blue-200 rounded p-4'>";
            echo "<h3 class='font-bold text-blue-800 mb-2'>📊 إحصائيات قاعدة البيانات:</h3>";
            echo "<div class='text-blue-700'>";
            echo "<p>عدد المعارض: <strong>{$exhibitions}</strong></p>";
            echo "<p>عدد الأجنحة: <strong>{$booths}</strong></p>";
            echo "</div>";
            echo "</div>";
            
        } catch (Exception $e) {
            echo "<div class='bg-yellow-50 border border-yellow-200 rounded p-4'>";
            echo "<h3 class='font-bold text-yellow-800 mb-2'>⚠️ تحذير:</h3>";
            echo "<p class='text-yellow-700'>الاتصال يعمل لكن لا يمكن الوصول للجداول: " . htmlspecialchars($e->getMessage()) . "</p>";
            echo "</div>";
        }
    }
    
} catch (Exception $e) {
    echo "<div class='bg-red-50 border border-red-200 rounded p-4'>";
    echo "<h3 class='font-bold text-red-800 mb-2'>❌ فشل الاتصال</h3>";
    echo "<p class='text-red-700'>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
    
    // Show current settings
    echo "<div class='bg-gray-50 border border-gray-200 rounded p-4 mt-4'>";
    echo "<h3 class='font-bold text-gray-800 mb-2'>الإعدادات الحالية:</h3>";
    echo "<div class='text-sm text-gray-600'>";
    echo "<p>Host: 127.0.0.1</p>";
    echo "<p>Database: u404269408_seasonexpodb</p>";
    echo "<p>Username: u404269408_expo</p>";
    echo "<p>Password: 43674367@Kwi</p>";
    echo "</div>";
    echo "</div>";
}

echo "</div>";

// Test different host configurations
echo "<div class='bg-white rounded-lg shadow p-6 mb-6'>";
echo "<h2 class='text-lg font-bold mb-4'>🧪 اختبار إعدادات مختلفة</h2>";

$testConfigs = [
    'localhost' => [
        'host' => 'localhost',
        'database' => 'u404269408_seasonexpodb',
        'username' => 'u404269408_expo',
        'password' => '43674367@Kwi'
    ],
    '127.0.0.1' => [
        'host' => '127.0.0.1',
        'database' => 'u404269408_seasonexpodb',
        'username' => 'u404269408_expo',
        'password' => '43674367@Kwi'
    ]
];

foreach ($testConfigs as $name => $config) {
    echo "<div class='mb-4'>";
    echo "<h3 class='font-semibold mb-2'>اختبار {$name}:</h3>";
    
    try {
        $dsn = "mysql:host={$config['host']};dbname={$config['database']};charset=utf8mb4";
        $pdo = new PDO($dsn, $config['username'], $config['password'], [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]);
        
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM exhibitions");
        $exhibitions = $stmt->fetch()['count'];
        
        echo "<div class='bg-green-50 border border-green-200 rounded p-3'>";
        echo "<p class='text-green-700'>✅ يعمل! عدد المعارض: <strong>{$exhibitions}</strong></p>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div class='bg-red-50 border border-red-200 rounded p-3'>";
        echo "<p class='text-red-700'>❌ لا يعمل: " . htmlspecialchars($e->getMessage()) . "</p>";
        echo "</div>";
    }
    
    echo "</div>";
}

echo "</div>";

// Instructions
echo "<div class='bg-blue-50 border border-blue-200 rounded-lg p-6'>";
echo "<h2 class='text-lg font-bold text-blue-700 mb-4'>📋 التعليمات</h2>";

echo "<div class='space-y-3'>";

echo "<div class='bg-white p-4 rounded border'>";
echo "<h3 class='font-semibold mb-2'>إذا نجح الاختبار:</h3>";
echo "<ul class='text-sm space-y-1 list-disc list-inside'>";
echo "<li>اذهب إلى exhibition-details.php واختبر الحجز</li>";
echo "<li>يجب أن يعمل بدون أخطاء قاعدة البيانات</li>";
echo "</ul>";
echo "</div>";

echo "<div class='bg-white p-4 rounded border'>";
echo "<h3 class='font-semibold mb-2'>إذا فشل الاختبار:</h3>";
echo "<ul class='text-sm space-y-1 list-disc list-inside'>";
echo "<li>تحقق من إعدادات قاعدة البيانات في Hostinger</li>";
echo "<li>تأكد من صحة اسم المستخدم وكلمة المرور</li>";
echo "<li>جرب localhost بدلاً من 127.0.0.1</li>";
echo "</ul>";
echo "</div>";

echo "</div>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>

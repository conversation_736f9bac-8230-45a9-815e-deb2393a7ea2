<?php
// Check Database Structure
// Check the structure of booths and bookings tables

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='utf-8'>";
echo "<title>فحص بنية قاعدة البيانات</title>";
echo "<script src='https://cdn.tailwindcss.com'></script>";
echo "</head>";
echo "<body class='bg-gray-50 p-8'>";

echo "<div class='max-w-4xl mx-auto'>";
echo "<h1 class='text-2xl font-bold mb-6'>🔍 فحص بنية قاعدة البيانات</h1>";

try {
    require_once 'includes/database.php';
    $pdo = getDatabaseConnection();
    
    // Check booths table structure
    echo "<div class='bg-white rounded-lg shadow p-6 mb-6'>";
    echo "<h2 class='text-lg font-bold mb-4'>📋 بنية جدول booths</h2>";
    
    try {
        $stmt = $pdo->query("DESCRIBE booths");
        $boothsColumns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<div class='overflow-x-auto'>";
        echo "<table class='min-w-full table-auto border-collapse border border-gray-300'>";
        echo "<thead class='bg-gray-100'>";
        echo "<tr>";
        echo "<th class='border border-gray-300 px-4 py-2'>اسم العمود</th>";
        echo "<th class='border border-gray-300 px-4 py-2'>النوع</th>";
        echo "<th class='border border-gray-300 px-4 py-2'>Null</th>";
        echo "<th class='border border-gray-300 px-4 py-2'>Key</th>";
        echo "<th class='border border-gray-300 px-4 py-2'>Default</th>";
        echo "</tr>";
        echo "</thead>";
        echo "<tbody>";
        
        foreach ($boothsColumns as $column) {
            echo "<tr>";
            echo "<td class='border border-gray-300 px-4 py-2 font-mono'>{$column['Field']}</td>";
            echo "<td class='border border-gray-300 px-4 py-2'>{$column['Type']}</td>";
            echo "<td class='border border-gray-300 px-4 py-2'>{$column['Null']}</td>";
            echo "<td class='border border-gray-300 px-4 py-2'>{$column['Key']}</td>";
            echo "<td class='border border-gray-300 px-4 py-2'>{$column['Default']}</td>";
            echo "</tr>";
        }
        
        echo "</tbody>";
        echo "</table>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div class='bg-red-50 border border-red-200 rounded p-4'>";
        echo "<p class='text-red-700'>خطأ في قراءة جدول booths: " . htmlspecialchars($e->getMessage()) . "</p>";
        echo "</div>";
    }
    
    echo "</div>";
    
    // Check bookings table structure
    echo "<div class='bg-white rounded-lg shadow p-6 mb-6'>";
    echo "<h2 class='text-lg font-bold mb-4'>📋 بنية جدول bookings</h2>";
    
    try {
        $stmt = $pdo->query("DESCRIBE bookings");
        $bookingsColumns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<div class='overflow-x-auto'>";
        echo "<table class='min-w-full table-auto border-collapse border border-gray-300'>";
        echo "<thead class='bg-gray-100'>";
        echo "<tr>";
        echo "<th class='border border-gray-300 px-4 py-2'>اسم العمود</th>";
        echo "<th class='border border-gray-300 px-4 py-2'>النوع</th>";
        echo "<th class='border border-gray-300 px-4 py-2'>Null</th>";
        echo "<th class='border border-gray-300 px-4 py-2'>Key</th>";
        echo "<th class='border border-gray-300 px-4 py-2'>Default</th>";
        echo "</tr>";
        echo "</thead>";
        echo "<tbody>";
        
        foreach ($bookingsColumns as $column) {
            echo "<tr>";
            echo "<td class='border border-gray-300 px-4 py-2 font-mono'>{$column['Field']}</td>";
            echo "<td class='border border-gray-300 px-4 py-2'>{$column['Type']}</td>";
            echo "<td class='border border-gray-300 px-4 py-2'>{$column['Null']}</td>";
            echo "<td class='border border-gray-300 px-4 py-2'>{$column['Key']}</td>";
            echo "<td class='border border-gray-300 px-4 py-2'>{$column['Default']}</td>";
            echo "</tr>";
        }
        
        echo "</tbody>";
        echo "</table>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div class='bg-yellow-50 border border-yellow-200 rounded p-4'>";
        echo "<p class='text-yellow-700'>جدول bookings غير موجود أو لا يمكن الوصول إليه: " . htmlspecialchars($e->getMessage()) . "</p>";
        echo "</div>";
    }
    
    echo "</div>";
    
    // Check sample data
    echo "<div class='bg-white rounded-lg shadow p-6 mb-6'>";
    echo "<h2 class='text-lg font-bold mb-4'>📊 بيانات تجريبية</h2>";
    
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM booths");
        $boothsCount = $stmt->fetch()['count'];
        
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM booths WHERE status = 'available'");
        $availableBooths = $stmt->fetch()['count'];
        
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM booths WHERE status = 'booked'");
        $bookedBooths = $stmt->fetch()['count'];
        
        echo "<div class='grid grid-cols-1 md:grid-cols-3 gap-4'>";
        
        echo "<div class='bg-blue-50 border border-blue-200 rounded p-4 text-center'>";
        echo "<h3 class='font-semibold text-blue-700'>إجمالي الأجنحة</h3>";
        echo "<p class='text-2xl font-bold text-blue-900'>{$boothsCount}</p>";
        echo "</div>";
        
        echo "<div class='bg-green-50 border border-green-200 rounded p-4 text-center'>";
        echo "<h3 class='font-semibold text-green-700'>أجنحة متاحة</h3>";
        echo "<p class='text-2xl font-bold text-green-900'>{$availableBooths}</p>";
        echo "</div>";
        
        echo "<div class='bg-red-50 border border-red-200 rounded p-4 text-center'>";
        echo "<h3 class='font-semibold text-red-700'>أجنحة محجوزة</h3>";
        echo "<p class='text-2xl font-bold text-red-900'>{$bookedBooths}</p>";
        echo "</div>";
        
        echo "</div>";
        
        // Try to count bookings
        try {
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM bookings");
            $bookingsCount = $stmt->fetch()['count'];
            
            echo "<div class='mt-4 bg-purple-50 border border-purple-200 rounded p-4 text-center'>";
            echo "<h3 class='font-semibold text-purple-700'>سجلات الحجز</h3>";
            echo "<p class='text-2xl font-bold text-purple-900'>{$bookingsCount}</p>";
            echo "</div>";
            
        } catch (Exception $e) {
            echo "<div class='mt-4 bg-yellow-50 border border-yellow-200 rounded p-4 text-center'>";
            echo "<h3 class='font-semibold text-yellow-700'>سجلات الحجز</h3>";
            echo "<p class='text-yellow-600'>جدول bookings غير متاح</p>";
            echo "</div>";
        }
        
    } catch (Exception $e) {
        echo "<div class='bg-red-50 border border-red-200 rounded p-4'>";
        echo "<p class='text-red-700'>خطأ في قراءة البيانات: " . htmlspecialchars($e->getMessage()) . "</p>";
        echo "</div>";
    }
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='bg-red-50 border border-red-200 rounded-lg p-6'>";
    echo "<h2 class='text-lg font-bold text-red-700 mb-4'>❌ خطأ في الاتصال</h2>";
    echo "<p class='text-red-700'>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

// Recommendations
echo "<div class='bg-blue-50 border border-blue-200 rounded-lg p-6'>";
echo "<h2 class='text-lg font-bold text-blue-700 mb-4'>💡 التوصيات</h2>";

echo "<div class='space-y-4'>";

echo "<div class='bg-white p-4 rounded border'>";
echo "<h3 class='font-semibold mb-2'>إذا كان جدول bookings موجود:</h3>";
echo "<ul class='text-sm space-y-1 list-disc list-inside'>";
echo "<li>سيتم حفظ الحجوزات في جدول bookings</li>";
echo "<li>سيتم عرض الحجوزات من جدول bookings</li>";
echo "<li>نظام حجز متكامل</li>";
echo "</ul>";
echo "</div>";

echo "<div class='bg-white p-4 rounded border'>";
echo "<h3 class='font-semibold mb-2'>إذا كان جدول bookings غير موجود:</h3>";
echo "<ul class='text-sm space-y-1 list-disc list-inside'>";
echo "<li>سيتم تغيير حالة الجناح فقط</li>";
echo "<li>لن يتم حفظ تفاصيل الحجز</li>";
echo "<li>نظام حجز بسيط</li>";
echo "</ul>";
echo "</div>";

echo "</div>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>

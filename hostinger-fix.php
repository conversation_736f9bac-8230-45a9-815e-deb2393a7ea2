<?php
/**
 * <PERSON>inger Deployment Fix Script
 * Upload this file to your public_html directory and run it once
 * URL: https://myapps.fjt-q8.com/hostinger-fix.php
 */

echo "<h1>Season Expo - Hostinger Deployment Fix</h1>";
echo "<p>Fixing Laravel cache and storage issues...</p>";

// Get the base path (one level up from public_html)
$basePath = dirname(__DIR__);
$publicPath = __DIR__;

echo "<h2>🔍 Checking Paths</h2>";
echo "<p>Base Path: {$basePath}</p>";
echo "<p>Public Path: {$publicPath}</p>";

// Required directories
$directories = [
    $basePath . '/storage',
    $basePath . '/storage/app',
    $basePath . '/storage/app/public',
    $basePath . '/storage/framework',
    $basePath . '/storage/framework/cache',
    $basePath . '/storage/framework/cache/data',
    $basePath . '/storage/framework/sessions',
    $basePath . '/storage/framework/testing',
    $basePath . '/storage/framework/views',
    $basePath . '/storage/logs',
    $basePath . '/bootstrap',
    $basePath . '/bootstrap/cache',
];

echo "<h2>📁 Creating Required Directories</h2>";

foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        if (mkdir($dir, 0755, true)) {
            echo "<p>✅ Created: {$dir}</p>";
        } else {
            echo "<p>❌ Failed to create: {$dir}</p>";
        }
    } else {
        echo "<p>✅ Exists: {$dir}</p>";
    }
}

echo "<h2>🔗 Creating Storage Symlink</h2>";

$storageLink = $publicPath . '/storage';
$storageTarget = $basePath . '/storage/app/public';

if (!file_exists($storageLink)) {
    if (symlink($storageTarget, $storageLink)) {
        echo "<p>✅ Storage symlink created successfully</p>";
    } else {
        echo "<p>❌ Failed to create storage symlink</p>";
        echo "<p>Manual fix: Create symlink from {$storageLink} to {$storageTarget}</p>";
    }
} else {
    echo "<p>✅ Storage symlink already exists</p>";
}

echo "<h2>📝 Creating .gitkeep Files</h2>";

$gitkeepFiles = [
    $basePath . '/storage/app/.gitkeep',
    $basePath . '/storage/app/public/.gitkeep',
    $basePath . '/storage/framework/cache/.gitkeep',
    $basePath . '/storage/framework/cache/data/.gitkeep',
    $basePath . '/storage/framework/sessions/.gitkeep',
    $basePath . '/storage/framework/testing/.gitkeep',
    $basePath . '/storage/framework/views/.gitkeep',
    $basePath . '/storage/logs/.gitkeep',
    $basePath . '/bootstrap/cache/.gitkeep',
];

foreach ($gitkeepFiles as $file) {
    if (!file_exists($file)) {
        if (file_put_contents($file, '# Keep this directory') !== false) {
            echo "<p>✅ Created: {$file}</p>";
        } else {
            echo "<p>❌ Failed to create: {$file}</p>";
        }
    }
}

echo "<h2>🔧 Setting Permissions</h2>";

foreach ($directories as $dir) {
    if (is_dir($dir)) {
        if (chmod($dir, 0755)) {
            echo "<p>✅ Set permissions for: {$dir}</p>";
        } else {
            echo "<p>⚠️ Could not set permissions for: {$dir}</p>";
        }
    }
}

echo "<h2>🧹 Clearing Caches (if possible)</h2>";

// Try to clear Laravel caches if possible
if (file_exists($basePath . '/vendor/autoload.php')) {
    try {
        require_once $basePath . '/vendor/autoload.php';
        
        if (file_exists($basePath . '/bootstrap/app.php')) {
            $app = require_once $basePath . '/bootstrap/app.php';
            $kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
            
            // Clear caches
            $kernel->call('config:clear');
            echo "<p>✅ Config cache cleared</p>";
            
            $kernel->call('cache:clear');
            echo "<p>✅ Application cache cleared</p>";
            
            $kernel->call('view:clear');
            echo "<p>✅ View cache cleared</p>";
            
            $kernel->call('route:clear');
            echo "<p>✅ Route cache cleared</p>";
            
        } else {
            echo "<p>⚠️ Laravel bootstrap file not found</p>";
        }
    } catch (Exception $e) {
        echo "<p>⚠️ Could not clear Laravel caches: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p>⚠️ Composer autoloader not found</p>";
}

echo "<h2>📋 Environment Check</h2>";

// Check if .env exists
$envFile = $basePath . '/.env';
if (file_exists($envFile)) {
    echo "<p>✅ .env file exists</p>";
    
    // Check critical settings
    $envContent = file_get_contents($envFile);
    
    if (strpos($envContent, 'APP_ENV=production') !== false) {
        echo "<p>✅ APP_ENV set to production</p>";
    } else {
        echo "<p>⚠️ APP_ENV should be set to 'production'</p>";
    }
    
    if (strpos($envContent, 'APP_DEBUG=false') !== false) {
        echo "<p>✅ APP_DEBUG set to false</p>";
    } else {
        echo "<p>⚠️ APP_DEBUG should be set to 'false' for production</p>";
    }
    
} else {
    echo "<p>❌ .env file not found! You need to create it.</p>";
}

echo "<h2>✅ Fix Complete!</h2>";
echo "<p>The deployment fix has been completed. Try accessing your application now.</p>";
echo "<p><strong>Important:</strong> Delete this file after running it for security reasons.</p>";
echo "<p><a href='/'>Go to Application</a></p>";

// Self-destruct option
if (isset($_GET['delete']) && $_GET['delete'] === 'yes') {
    if (unlink(__FILE__)) {
        echo "<p>✅ Fix script deleted successfully!</p>";
    } else {
        echo "<p>❌ Could not delete fix script. Please delete manually.</p>";
    }
}

echo "<p><a href='?delete=yes'>Delete this fix script</a></p>";
?>

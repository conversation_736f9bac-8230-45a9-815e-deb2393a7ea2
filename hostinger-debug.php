<?php
echo "<h1>🚨 EMERGENCY HOSTINGER DIAGNOSIS</h1>";

echo "<h2>1. Basic Server Information:</h2>";
echo "PHP Version: " . phpversion() . "<br>";
echo "Server Software: " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "<br>";
echo "Document Root: " . ($_SERVER['DOCUMENT_ROOT'] ?? 'Unknown') . "<br>";
echo "Current User: " . get_current_user() . "<br>";
echo "Memory Limit: " . ini_get('memory_limit') . "<br>";
echo "Max Execution Time: " . ini_get('max_execution_time') . "<br>";

echo "<h2>2. PHP Extensions Check:</h2>";
$requiredExtensions = ['pdo', 'pdo_mysql', 'mbstring', 'openssl', 'tokenizer', 'xml', 'ctype', 'json', 'bcmath'];
foreach ($requiredExtensions as $ext) {
    if (extension_loaded($ext)) {
        echo "✅ {$ext}<br>";
    } else {
        echo "❌ {$ext} MISSING<br>";
    }
}

echo "<h2>3. File Permissions Check:</h2>";
$paths = [
    dirname(__DIR__) . '/storage',
    dirname(__DIR__) . '/storage/logs',
    dirname(__DIR__) . '/storage/framework',
    dirname(__DIR__) . '/bootstrap/cache',
    __DIR__
];

foreach ($paths as $path) {
    if (file_exists($path)) {
        $perms = substr(sprintf('%o', fileperms($path)), -4);
        $writable = is_writable($path) ? 'WRITABLE' : 'NOT WRITABLE';
        echo "✅ {$path}: {$perms} ({$writable})<br>";
    } else {
        echo "❌ {$path}: MISSING<br>";
    }
}

echo "<h2>4. Error Log Check:</h2>";
$errorLog = ini_get('error_log');
echo "Error log location: " . ($errorLog ?: 'Default') . "<br>";

// Check for error log files
$possibleLogs = [
    __DIR__ . '/error_log',
    dirname(__DIR__) . '/error_log',
    dirname(__DIR__) . '/storage/logs/laravel.log'
];

foreach ($possibleLogs as $log) {
    if (file_exists($log)) {
        echo "📄 Found log: {$log} (" . filesize($log) . " bytes)<br>";
        
        // Show last few lines
        $lines = file($log);
        if ($lines && count($lines) > 0) {
            $lastLines = array_slice($lines, -5);
            echo "<details><summary>Last 5 lines</summary><pre>";
            foreach ($lastLines as $line) {
                echo htmlspecialchars($line);
            }
            echo "</pre></details>";
        }
    }
}

echo "<h2>5. Composer/Vendor Check:</h2>";
$vendorPath = dirname(__DIR__) . '/vendor';
if (is_dir($vendorPath)) {
    echo "✅ Vendor directory exists<br>";
    
    $autoloadFile = $vendorPath . '/autoload.php';
    if (file_exists($autoloadFile)) {
        echo "✅ Autoload file exists (" . filesize($autoloadFile) . " bytes)<br>";
        
        // Try to load it
        try {
            require_once $autoloadFile;
            echo "✅ Autoloader loads successfully<br>";
        } catch (Throwable $e) {
            echo "❌ Autoloader error: " . $e->getMessage() . "<br>";
        }
    } else {
        echo "❌ Autoload file missing<br>";
    }
} else {
    echo "❌ Vendor directory missing<br>";
}

echo "<h2>6. Laravel Version Check:</h2>";
$composerFile = dirname(__DIR__) . '/composer.json';
if (file_exists($composerFile)) {
    $composer = json_decode(file_get_contents($composerFile), true);
    if (isset($composer['require']['laravel/framework'])) {
        echo "Laravel version: " . $composer['require']['laravel/framework'] . "<br>";
    }
}

echo "<h2>7. Environment File Check:</h2>";
$envFile = dirname(__DIR__) . '/.env';
if (file_exists($envFile)) {
    echo "✅ .env file exists (" . filesize($envFile) . " bytes)<br>";
    
    $envContent = file_get_contents($envFile);
    if (strpos($envContent, 'APP_KEY=') !== false) {
        echo "✅ APP_KEY found<br>";
    } else {
        echo "❌ APP_KEY missing<br>";
    }
} else {
    echo "❌ .env file missing<br>";
}

echo "<h2>8. CRITICAL RECOMMENDATION:</h2>";
echo "<div style='background: #f8d7da; padding: 20px; border: 2px solid #dc3545; border-radius: 10px;'>";
echo "<h3>🚨 CONTACT HOSTINGER SUPPORT IMMEDIATELY</h3>";
echo "<p><strong>This level of persistent 500 errors suggests a server-level issue.</strong></p>";
echo "<p>Share this diagnostic information with Hostinger support:</p>";
echo "<ul>";
echo "<li>Domain: myapps.fjt-q8.com</li>";
echo "<li>Issue: Persistent 500 errors even with basic Laravel</li>";
echo "<li>PHP Version: " . phpversion() . "</li>";
echo "<li>Framework: Laravel</li>";
echo "<li>All basic tests pass but web requests fail</li>";
echo "</ul>";
echo "</div>";

echo "<h2>9. Alternative: Try Different PHP Version</h2>";
echo "<p>In Hostinger control panel, try switching to:</p>";
echo "<ul>";
echo "<li>PHP 8.1</li>";
echo "<li>PHP 8.0</li>";
echo "<li>PHP 7.4</li>";
echo "</ul>";

echo "<h2>10. Last Resort Test:</h2>";
echo "<p><a href='minimal-laravel-test.php' style='background: #dc3545; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>Try Absolute Minimal Laravel Test</a></p>";
?>

<?php

namespace App\Http\Controllers;

use App\Models\Booking;
use App\Models\Booth;
use App\Models\Exhibition;
use App\Models\Payment;
use App\Models\Category;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class DashboardController extends Controller
{
    /**
     * Display the dashboard.
     */
    public function index(): Response
    {
        try {
            $user = auth()->user();

            if (!$user) {
                // Fallback for unauthenticated users
                return Inertia::render('Dashboard', [
                    'user' => null,
                    'bookings' => [],
                    'exhibitions' => [],
                    'stats' => [
                        'total_bookings' => 0,
                        'active_bookings' => 0,
                        'total_spent' => 0,
                        'upcoming_exhibitions' => 0,
                    ],
                    'locale' => app()->getLocale(),
                    'translations' => [
                        'app' => __('app'),
                    ],
                ]);
            }

            // Get user's bookings with related data
            $bookings = Booking::with(['exhibition', 'booth', 'payments'])
                ->where('user_id', $user->id)
                ->latest()
                ->take(5)
                ->get();

            // Get user's exhibitions if they're an organizer
            $exhibitions = [];
            if (isset($user->role) && $user->role === 'organizer') {
                $exhibitions = Exhibition::where('organizer_id', $user->id)
                    ->withCount(['booths', 'bookings'])
                    ->latest()
                    ->take(5)
                    ->get();
            }

            // Calculate stats
            $stats = [
                'total_bookings' => Booking::where('user_id', $user->id)->count(),
                'active_bookings' => Booking::where('user_id', $user->id)
                    ->where('status', 'confirmed')
                    ->count(),
                'total_spent' => Payment::whereHas('booking', function ($query) use ($user) {
                    $query->where('user_id', $user->id);
                })->where('status', 'completed')->sum('amount'),
                'upcoming_exhibitions' => Booking::where('user_id', $user->id)
                    ->whereHas('exhibition', function ($query) {
                        $query->where('start_date', '>', now());
                    })
                    ->count(),
            ];

            return Inertia::render('Dashboard', [
                'user' => $user,
                'bookings' => $bookings,
                'exhibitions' => $exhibitions,
                'stats' => $stats,
                'locale' => app()->getLocale(),
                'translations' => [
                    'app' => __('app'),
                ],
            ]);

        } catch (\Exception $e) {
            // Error fallback
            return Inertia::render('Dashboard', [
                'user' => auth()->user(),
                'bookings' => [],
                'exhibitions' => [],
                'stats' => [
                    'total_bookings' => 0,
                    'active_bookings' => 0,
                    'total_spent' => 0,
                    'upcoming_exhibitions' => 0,
                ],
                'locale' => app()->getLocale(),
                'translations' => [
                    'app' => __('app'),
                ],
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Search for available booths
     */
    public function searchBooths(Request $request): Response
    {
        $query = Booth::with(['exhibition.category'])
            ->where('status', 'available');

        // Filter by exhibition
        if ($request->filled('exhibition_id')) {
            $query->where('exhibition_id', $request->exhibition_id);
        }

        // Filter by category
        if ($request->filled('category_id')) {
            $query->whereHas('exhibition', function ($q) use ($request) {
                $q->where('category_id', $request->category_id);
            });
        }

        // Filter by price range
        if ($request->filled('min_price')) {
            $query->where('price', '>=', $request->min_price);
        }
        if ($request->filled('max_price')) {
            $query->where('price', '<=', $request->max_price);
        }

        // Filter by size
        if ($request->filled('min_size')) {
            $query->where('size', '>=', $request->min_size);
        }

        // Filter by location/city
        if ($request->filled('city')) {
            $query->whereHas('exhibition', function ($q) use ($request) {
                $q->where('city', 'like', '%' . $request->city . '%');
            });
        }

        // Filter by date range
        if ($request->filled('start_date')) {
            $query->whereHas('exhibition', function ($q) use ($request) {
                $q->where('start_date', '>=', $request->start_date);
            });
        }
        if ($request->filled('end_date')) {
            $query->whereHas('exhibition', function ($q) use ($request) {
                $q->where('end_date', '<=', $request->end_date);
            });
        }

        // Sort options
        $sortBy = $request->get('sort', 'price');
        $sortOrder = $request->get('order', 'asc');

        if (in_array($sortBy, ['price', 'size', 'booth_number'])) {
            $query->orderBy($sortBy, $sortOrder);
        } elseif ($sortBy === 'exhibition_date') {
            $query->join('exhibitions', 'booths.exhibition_id', '=', 'exhibitions.id')
                  ->orderBy('exhibitions.start_date', $sortOrder)
                  ->select('booths.*');
        }

        $booths = $query->paginate(12)->withQueryString();

        // Get filter options
        $exhibitions = Exhibition::where('status', 'published')
            ->where('start_date', '>', now())
            ->orderBy('start_date')
            ->get(['id', 'title', 'start_date', 'city']);

        $categories = Category::where('is_active', true)
            ->orderBy('name')
            ->get(['id', 'name']);

        return Inertia::render('Dashboard/BoothSearch', [
            'booths' => $booths,
            'exhibitions' => $exhibitions,
            'categories' => $categories,
            'filters' => $request->only([
                'exhibition_id', 'category_id', 'min_price', 'max_price',
                'min_size', 'city', 'start_date', 'end_date', 'sort', 'order'
            ]),
        ]);
    }

    /**
     * Manage user's reservations
     */
    public function manageReservations(Request $request): Response
    {
        $user = auth()->user();

        $query = Booking::with(['exhibition', 'booth', 'payments'])
            ->where('user_id', $user->id);

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by exhibition
        if ($request->filled('exhibition_id')) {
            $query->where('exhibition_id', $request->exhibition_id);
        }

        // Filter by date range
        if ($request->filled('from_date')) {
            $query->whereDate('created_at', '>=', $request->from_date);
        }
        if ($request->filled('to_date')) {
            $query->whereDate('created_at', '<=', $request->to_date);
        }

        // Sort
        $sortBy = $request->get('sort', 'created_at');
        $sortOrder = $request->get('order', 'desc');

        if (in_array($sortBy, ['created_at', 'total_amount', 'status'])) {
            $query->orderBy($sortBy, $sortOrder);
        }

        $reservations = $query->paginate(10)->withQueryString();

        // Get user's exhibitions for filter
        $userExhibitions = Exhibition::whereHas('bookings', function ($q) use ($user) {
            $q->where('user_id', $user->id);
        })->get(['id', 'title']);

        // Calculate summary stats
        $summaryStats = [
            'total_reservations' => Booking::where('user_id', $user->id)->count(),
            'confirmed_reservations' => Booking::where('user_id', $user->id)->where('status', 'confirmed')->count(),
            'pending_reservations' => Booking::where('user_id', $user->id)->where('status', 'pending')->count(),
            'cancelled_reservations' => Booking::where('user_id', $user->id)->where('status', 'cancelled')->count(),
            'total_spent' => Payment::whereHas('booking', function ($query) use ($user) {
                $query->where('user_id', $user->id);
            })->where('status', 'completed')->sum('amount'),
        ];

        return Inertia::render('Dashboard/ReservationManagement', [
            'reservations' => $reservations,
            'userExhibitions' => $userExhibitions,
            'summaryStats' => $summaryStats,
            'filters' => $request->only(['status', 'exhibition_id', 'from_date', 'to_date', 'sort', 'order']),
        ]);
    }
}

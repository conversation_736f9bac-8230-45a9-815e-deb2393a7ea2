<?php

namespace App\Http\Controllers;

use App\Models\Exhibition;
use App\Models\Booth;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;
use Illuminate\Http\RedirectResponse;

class BoothController extends Controller
{
    /**
     * Display a listing of booths for an exhibition.
     */
    public function index(Request $request, Exhibition $exhibition)
    {
        $query = $exhibition->booths();

        // Filter by size
        if ($request->filled('size')) {
            $query->where('size', $request->get('size'));
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->get('status'));
        } else {
            // Default to available booths
            $query->available();
        }

        // Filter by features
        if ($request->filled('features')) {
            $features = $request->get('features');
            if (is_array($features)) {
                foreach ($features as $feature) {
                    $query->whereJsonContains('features', $feature);
                }
            }
        }

        // Filter by price range
        if ($request->filled('min_price')) {
            $query->where('price', '>=', $request->get('min_price'));
        }

        if ($request->filled('max_price')) {
            $query->where('price', '<=', $request->get('max_price'));
        }

        // Filter by corner booths
        if ($request->filled('corner_only') && $request->get('corner_only')) {
            $query->where('is_corner', true);
        }

        // Sort
        $sortBy = $request->get('sort', 'booth_number');
        $sortOrder = $request->get('order', 'asc');

        if (in_array($sortBy, ['booth_number', 'price', 'area', 'size'])) {
            $query->orderBy($sortBy, $sortOrder);
        }

        $booths = $query->paginate(12)->withQueryString();

        // Get booth statistics
        $stats = [
            'total' => $exhibition->booths()->count(),
            'available' => $exhibition->booths()->available()->count(),
            'booked' => $exhibition->booths()->booked()->count(),
            'by_size' => $exhibition->booths()
                ->selectRaw('size, count(*) as count, sum(case when status = "available" then 1 else 0 end) as available_count')
                ->groupBy('size')
                ->get()
                ->keyBy('size'),
        ];

        // Get available features for filtering
        $allFeatures = $exhibition->booths()
            ->whereNotNull('features')
            ->pluck('features')
            ->flatten()
            ->unique()
            ->values();

        // Use simple HTML view instead of Inertia to avoid Vite issues
        return view('booths.index-simple', [
            'exhibition' => $exhibition->load('category', 'organizer'),
            'booths' => $booths,
            'stats' => $stats,
            'filters' => $request->only(['size', 'status', 'features', 'min_price', 'max_price', 'corner_only', 'sort', 'order']),
        ]);
    }

    /**
     * Display the specified booth.
     */
    public function show(Exhibition $exhibition, Booth $booth)
    {
        // Ensure the booth belongs to the exhibition
        if ($booth->exhibition_id !== $exhibition->id) {
            abort(404);
        }

        $booth->load('exhibition.category', 'exhibition.organizer');

        // Get similar booths (same size, available)
        $similarBooths = $exhibition->booths()
            ->where('id', '!=', $booth->id)
            ->where('size', $booth->size)
            ->available()
            ->take(4)
            ->get();

        // Use simple HTML view instead of Inertia to avoid Vite issues
        return view('booths.show-simple', [
            'exhibition' => $exhibition,
            'booth' => $booth,
            'similarBooths' => $similarBooths,
        ]);
    }

    /**
     * Show the form for creating a new booth.
     */
    public function create(Exhibition $exhibition): Response
    {
        // Only organizers can create booths for their exhibitions
        if (!auth()->user() || auth()->user()->id !== $exhibition->organizer_id) {
            abort(403);
        }

        return Inertia::render('Booths/Create', [
            'exhibition' => $exhibition,
        ]);
    }

    /**
     * Store a newly created booth.
     */
    public function store(Request $request, Exhibition $exhibition): RedirectResponse
    {
        // Only organizers can create booths for their exhibitions
        if (!auth()->user() || auth()->user()->id !== $exhibition->organizer_id) {
            abort(403);
        }

        $validated = $request->validate([
            'booth_number' => 'required|string|max:255',
            'name' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'size' => 'required|in:small,medium,large,premium',
            'width' => 'required|numeric|min:1',
            'height' => 'required|numeric|min:1',
            'price' => 'required|numeric|min:0',
            'location' => 'nullable|string|max:255',
            'features' => 'nullable|array',
            'features.*' => 'string',
            'is_featured' => 'boolean',
            'is_corner' => 'boolean',
        ]);

        // Calculate area
        $validated['area'] = $validated['width'] * $validated['height'];
        $validated['exhibition_id'] = $exhibition->id;

        // Check if booth number is unique for this exhibition
        $existingBooth = $exhibition->booths()->where('booth_number', $validated['booth_number'])->first();
        if ($existingBooth) {
            return back()->withErrors(['booth_number' => 'Booth number already exists for this exhibition.']);
        }

        $booth = Booth::create($validated);

        return redirect()->route('booths.show', [$exhibition, $booth])
            ->with('success', 'Booth created successfully!');
    }

    /**
     * Show the form for editing the specified booth.
     */
    public function edit(Exhibition $exhibition, Booth $booth): Response
    {
        // Only organizers can edit booths for their exhibitions
        if (!auth()->user() || auth()->user()->id !== $exhibition->organizer_id) {
            abort(403);
        }

        // Ensure the booth belongs to the exhibition
        if ($booth->exhibition_id !== $exhibition->id) {
            abort(404);
        }

        return Inertia::render('Booths/Edit', [
            'exhibition' => $exhibition,
            'booth' => $booth,
        ]);
    }

    /**
     * Update the specified booth.
     */
    public function update(Request $request, Exhibition $exhibition, Booth $booth): RedirectResponse
    {
        // Only organizers can update booths for their exhibitions
        if (!auth()->user() || auth()->user()->id !== $exhibition->organizer_id) {
            abort(403);
        }

        // Ensure the booth belongs to the exhibition
        if ($booth->exhibition_id !== $exhibition->id) {
            abort(404);
        }

        $validated = $request->validate([
            'booth_number' => 'required|string|max:255',
            'name' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'size' => 'required|in:small,medium,large,premium',
            'width' => 'required|numeric|min:1',
            'height' => 'required|numeric|min:1',
            'price' => 'required|numeric|min:0',
            'location' => 'nullable|string|max:255',
            'features' => 'nullable|array',
            'features.*' => 'string',
            'status' => 'required|in:available,booked,reserved,maintenance',
            'is_featured' => 'boolean',
            'is_corner' => 'boolean',
        ]);

        // Calculate area
        $validated['area'] = $validated['width'] * $validated['height'];

        // Check if booth number is unique for this exhibition (excluding current booth)
        $existingBooth = $exhibition->booths()
            ->where('booth_number', $validated['booth_number'])
            ->where('id', '!=', $booth->id)
            ->first();

        if ($existingBooth) {
            return back()->withErrors(['booth_number' => 'Booth number already exists for this exhibition.']);
        }

        $booth->update($validated);

        return redirect()->route('booths.show', [$exhibition, $booth])
            ->with('success', 'Booth updated successfully!');
    }

    /**
     * Remove the specified booth.
     */
    public function destroy(Exhibition $exhibition, Booth $booth): RedirectResponse
    {
        // Only organizers can delete booths for their exhibitions
        if (!auth()->user() || auth()->user()->id !== $exhibition->organizer_id) {
            abort(403);
        }

        // Ensure the booth belongs to the exhibition
        if ($booth->exhibition_id !== $exhibition->id) {
            abort(404);
        }

        // Don't allow deletion if booth is booked
        if ($booth->status === 'booked') {
            return back()->withErrors(['booth' => 'Cannot delete a booked booth.']);
        }

        $booth->delete();

        return redirect()->route('booths.index', $exhibition)
            ->with('success', 'Booth deleted successfully!');
    }
}

<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;
use App\Http\Controllers\ExhibitionController;
use App\Http\Controllers\BoothController;
use App\Http\Controllers\BookingController;
use App\Http\Controllers\DashboardController;
use Inertia\Inertia;

/*
|--------------------------------------------------------------------------
| Web Routes - MINIMAL SAFE VERSION
|--------------------------------------------------------------------------
*/

// Homepage
Route::get('/', function () {
    return view('welcome');
});

// Cache clearing routes for Hostinger
Route::get('/route-clear', function () {
    try {
        Artisan::call('route:clear');
        return response()->json(['status' => 'success', 'message' => 'Routes cleared successfully']);
    } catch (Exception $e) {
        return response()->json(['status' => 'error', 'message' => $e->getMessage()]);
    }
});

Route::get('/config-clear', function () {
    try {
        Artisan::call('config:clear');
        return response()->json(['status' => 'success', 'message' => 'Config cleared successfully']);
    } catch (Exception $e) {
        return response()->json(['status' => 'error', 'message' => $e->getMessage()]);
    }
});

Route::get('/view-clear', function () {
    try {
        Artisan::call('view:clear');
        return response()->json(['status' => 'success', 'message' => 'Views cleared successfully']);
    } catch (Exception $e) {
        return response()->json(['status' => 'error', 'message' => $e->getMessage()]);
    }
});

Route::get('/cache-clear', function () {
    try {
        Artisan::call('cache:clear');
        return response()->json(['status' => 'success', 'message' => 'Cache cleared successfully']);
    } catch (Exception $e) {
        return response()->json(['status' => 'error', 'message' => $e->getMessage()]);
    }
});

// Simple Arabic login page (separate route)
Route::get('/login-simple', function () {
    return view('auth.login-simple');
})->name('login.arabic')->middleware('guest');

// Override login routes AFTER auth.php to take precedence
Route::get('/login', function () {
    return view('auth.login-simple');
})->middleware('guest');

Route::post('/login', function (Request $request) {
    $credentials = $request->validate([
        'email' => ['required', 'email'],
        'password' => ['required'],
    ]);

    if (Auth::attempt($credentials, $request->boolean('remember'))) {
        $request->session()->regenerate();
        return redirect()->intended('/dashboard');
    }

    return back()->withErrors([
        'email' => 'البيانات المدخلة غير صحيحة.',
    ])->onlyInput('email');
})->middleware('guest');

// Override logout route
Route::post('/logout', function (Request $request) {
    Auth::logout();
    $request->session()->invalidate();
    $request->session()->regenerateToken();
    return redirect('/');
})->middleware('auth');

require __DIR__.'/auth.php';

// Exhibitions
Route::get('/exhibitions', [ExhibitionController::class, 'index'])->name('exhibitions.index');
Route::get('/exhibitions/{exhibition}', [ExhibitionController::class, 'show'])->name('exhibitions.show');

// Booths
Route::get('/exhibitions/{exhibition}/booths', [BoothController::class, 'index'])->name('booths.index');
Route::get('/exhibitions/{exhibition}/booths/{booth}', [BoothController::class, 'show'])->name('booths.show');

// Protected routes
Route::middleware(['auth', 'verified'])->group(function () {
    // Simple HTML dashboard (no Vite needed)
    Route::get('/dashboard', function () {
        return view('dashboard-simple');
    })->name('dashboard');

    // Booking management
    Route::resource('bookings', BookingController::class);

    // Simple booking route (no payment for now)
    Route::post('/exhibitions/{exhibition}/booths/{booth}/book', function ($exhibitionSlug, $boothId) {
        try {
            $exhibition = \App\Models\Exhibition::where('slug', $exhibitionSlug)->firstOrFail();
            $booth = \App\Models\Booth::findOrFail($boothId);

            if ($booth->status !== 'available') {
                return back()->withErrors(['booth' => 'هذا الجناح غير متاح للحجز']);
            }

            $booking = \App\Models\Booking::create([
                'user_id' => auth()->id(),
                'exhibition_id' => $exhibition->id,
                'booth_id' => $booth->id,
                'status' => 'pending',
                'total_amount' => $booth->price,
                'currency' => $exhibition->currency ?? 'KWD',
                'booking_date' => now(),
                'exhibitor_details' => [
                    'company' => auth()->user()->company ?? '',
                    'contact_person' => auth()->user()->name,
                    'email' => auth()->user()->email,
                    'phone' => auth()->user()->phone ?? '',
                ],
            ]);

            $booth->update(['status' => 'reserved']);

            return redirect("/bookings/{$booking->id}")
                ->with('success', 'تم حجز الجناح بنجاح!');

        } catch (Exception $e) {
            return back()->withErrors(['booking' => 'فشل في إنشاء الحجز']);
        }
    })->name('booths.book');
});

// Create demo users for testing
Route::get('/create-demo-users', function () {
    try {
        $users = [];

        $demoUsers = [
            [
                'name' => 'Demo Exhibitor',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'role' => 'exhibitor',
            ],
            [
                'name' => 'Demo Organizer',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'role' => 'organizer',
            ],
            [
                'name' => 'Demo Admin',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'role' => 'admin',
            ]
        ];

        foreach ($demoUsers as $userData) {
            $user = \App\Models\User::updateOrCreate(
                ['email' => $userData['email']],
                $userData
            );
            $users[] = [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'role' => $user->role ?? 'user'
            ];
        }

        return response()->json([
            'status' => 'success',
            'message' => 'Demo users created/updated successfully',
            'users' => $users
        ]);

    } catch (Exception $e) {
        return response()->json([
            'status' => 'error',
            'message' => $e->getMessage()
        ]);
    }
});

// Create sample booths
Route::get('/create-sample-booths', function () {
    try {
        $exhibition = \App\Models\Exhibition::first();

        if (!$exhibition) {
            return response()->json([
                'status' => 'error',
                'message' => 'No exhibitions found. Create exhibitions first.'
            ]);
        }

        $boothCount = 10;
        $booths = [];

        for ($i = 1; $i <= $boothCount; $i++) {
            $booth = \App\Models\Booth::create([
                'exhibition_id' => $exhibition->id,
                'booth_number' => 'B' . str_pad($i, 3, '0', STR_PAD_LEFT),
                'name' => 'Booth ' . $i,
                'description' => 'Premium booth space with excellent visibility',
                'size' => ['small', 'medium', 'large'][rand(0, 2)],
                'width' => rand(3, 6),
                'height' => rand(3, 6),
                'area' => rand(9, 36),
                'price' => rand(1000, 5000),
                'location' => 'Hall A, Section ' . ceil($i / 5),
                'features' => ['electricity', 'wifi', 'storage'],
                'status' => 'available',
                'is_featured' => rand(0, 1),
                'is_corner' => rand(0, 1),
            ]);

            $booths[] = $booth->booth_number;
        }

        return response()->json([
            'status' => 'success',
            'message' => 'Sample booths created successfully',
            'booths' => $booths,
            'exhibition' => $exhibition->title
        ]);

    } catch (Exception $e) {
        return response()->json([
            'status' => 'error',
            'message' => $e->getMessage()
        ]);
    }
});

// Test route
Route::get('/test', function () {
    return response()->json([
        'status' => 'success',
        'message' => 'Application is working!',
        'timestamp' => now()->toDateTimeString()
    ]);
});

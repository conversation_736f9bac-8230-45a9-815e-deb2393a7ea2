# دليل رفع التحديثات إلى Hostinger

## الملفات المطلوب رفعها:

### 1. الملفات الأساسية (ضرورية):
- `resources/views/auth/register-simple.blade.php`
- `resources/views/auth/forgot-password.blade.php`
- `routes/web.php`

### 2. ملفات الاختبار (اختيارية):
- `test-forgot-password.php`
- `test-login-page.php`
- `test-email-functionality.php`

## طرق الرفع:

### الطريقة الأولى: File Manager في Hostinger
1. ادخل إلى لوحة تحكم Hostinger
2. اذهب إلى File Manager
3. انتقل إلى مجلد موقعك (عادة public_html)
4. ارفع الملفات في مواقعها الصحيحة:
   - `resources/views/auth/` للملفات blade
   - `routes/` لملف web.php
   - الجذر الرئيسي لملفات الاختبار

### الطريقة الثانية: FTP Client
1. استخدم FileZilla أو WinSCP
2. اتصل بالسيرفر باستخدام بيانات FTP
3. ارفع الملفات في مواقعها الصحيحة

### الطريقة الثالثة: Git (إذا كان متاح)
```bash
git add .
git commit -m "Add forgot password functionality"
git push origin main
```

## بعد الرفع:

### 1. تنظيف الكاش:
- اذهب إلى `/clear-all-caches.php` في المتصفح
- أو استخدم SSH: `php artisan cache:clear`

### 2. اختبار الوظيفة:
- اذهب إلى صفحة تسجيل الدخول
- تأكد من وجود رابط "نسيت كلمة المرور"
- اختبر وظيفة إرسال البريد

### 3. إعداد البريد الإلكتروني:
- تحديث ملف `.env` بإعدادات SMTP الصحيحة
- أو استخدام خدمة البريد المتاحة في Hostinger

## إعدادات البريد الإلكتروني لـ Hostinger:

```env
MAIL_MAILER=smtp
MAIL_HOST=smtp.hostinger.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-email-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="Season Expo"
```

## التحقق من النجاح:

1. ✅ رابط "نسيت كلمة المرور" يظهر في صفحة تسجيل الدخول
2. ✅ صفحة استعادة كلمة المرور تعمل
3. ✅ إرسال البريد الإلكتروني يعمل (أو يظهر في السجلات)
4. ✅ صفحة إعادة تعيين كلمة المرور تعمل

## ملاحظات مهمة:

- تأكد من صلاحيات الكتابة على المجلدات
- تأكد من وجود جدول `password_reset_tokens` في قاعدة البيانات
- اختبر الوظيفة بعد الرفع مباشرة
- احتفظ بنسخة احتياطية قبل الرفع

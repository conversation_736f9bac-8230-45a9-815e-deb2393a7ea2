<?php
echo "<h1>Check index.php File</h1>";

$indexPath = __DIR__ . '/index.php';

echo "<h2>Current index.php Analysis:</h2>";

if (file_exists($indexPath)) {
    echo "✅ index.php exists<br>";
    echo "File size: " . filesize($indexPath) . " bytes<br>";
    echo "Last modified: " . date('Y-m-d H:i:s', filemtime($indexPath)) . "<br><br>";
    
    $content = file_get_contents($indexPath);
    
    echo "<h3>Current Content:</h3>";
    echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 5px;'>";
    echo htmlspecialchars($content);
    echo "</pre>";
    
    echo "<h3>Content Analysis:</h3>";
    
    // Check for Laravel components
    if (strpos($content, 'LARAVEL_START') !== false) {
        echo "✅ Contains LARAVEL_START<br>";
    } else {
        echo "❌ Missing LARAVEL_START<br>";
    }
    
    if (strpos($content, 'vendor/autoload.php') !== false) {
        echo "✅ Includes autoloader<br>";
    } else {
        echo "❌ Missing autoloader<br>";
    }
    
    if (strpos($content, 'bootstrap/app.php') !== false) {
        echo "✅ Includes bootstrap<br>";
    } else {
        echo "❌ Missing bootstrap<br>";
    }
    
    // Check paths
    if (strpos($content, '__DIR__.\'/../') !== false) {
        echo "✅ Uses correct relative paths<br>";
    } else {
        echo "⚠️ Path format might be incorrect<br>";
    }
    
    // Check for Laravel 11 vs older versions
    if (strpos($content, 'handleRequest') !== false) {
        echo "⚠️ Uses Laravel 11 syntax (might be incompatible)<br>";
        echo "<div style='background: #fff3cd; padding: 10px; border: 1px solid #ffeaa7; border-radius: 5px; margin: 10px 0;'>";
        echo "<strong>Issue Found:</strong> Your index.php uses Laravel 11 syntax but you might have Laravel 10.<br>";
        echo "This could cause the 500 error.";
        echo "</div>";
    } else {
        echo "✅ Uses standard Laravel syntax<br>";
    }
    
} else {
    echo "❌ index.php not found!<br>";
}

echo "<h2>Correct index.php for Laravel 10:</h2>";
echo "<pre style='background: #e8f5e8; padding: 10px; border-radius: 5px;'>";
echo htmlspecialchars('<?php

use Illuminate\Contracts\Http\Kernel;
use Illuminate\Http\Request;

define(\'LARAVEL_START\', microtime(true));

/*
|--------------------------------------------------------------------------
| Check If The Application Is Under Maintenance
|--------------------------------------------------------------------------
*/

if (file_exists($maintenance = __DIR__.\'/../storage/framework/maintenance.php\')) {
    require $maintenance;
}

/*
|--------------------------------------------------------------------------
| Register The Auto Loader
|--------------------------------------------------------------------------
*/

require __DIR__.\'/../vendor/autoload.php\';

/*
|--------------------------------------------------------------------------
| Run The Application
|--------------------------------------------------------------------------
*/

$app = require_once __DIR__.\'/../bootstrap/app.php\';

$kernel = $app->make(Kernel::class);

$response = $kernel->handle(
    $request = Request::capture()
)->send();

$kernel->terminate($request, $response);');
echo "</pre>";

echo "<h2>Actions:</h2>";
echo "<p><a href='fix-index.php' style='background: #007cba; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>Fix index.php File</a></p>";
?>

<?php
// Exhibition Details Page
// This page displays detailed information about a specific exhibition

session_start();

// Get exhibition ID from URL
$exhibitionId = $_GET['id'] ?? null;

if (!$exhibitionId) {
    header('Location: /exhibitions');
    exit;
}

// Get language
$currentLang = $_SESSION['language'] ?? $_COOKIE['language'] ?? $_GET['lang'] ?? 'ar';

// Include database configuration
require_once '../config/database.php';

try {
    $pdo = getDatabaseConnection();
    
    // Get exhibition details
    $stmt = $pdo->prepare("SELECT * FROM exhibitions WHERE id = ? AND status = 'published'");
    $stmt->execute([$exhibitionId]);
    $exhibition = $stmt->fetch(PDO::FETCH_OBJ);
    
    if (!$exhibition) {
        header('Location: /exhibitions');
        exit;
    }
    
    // Get exhibition booths
    $stmt = $pdo->prepare("
        SELECT *
        FROM booths
        WHERE exhibition_id = ?
        ORDER BY name
    ");
    $stmt->execute([$exhibitionId]);
    $booths = $stmt->fetchAll(PDO::FETCH_OBJ);
    
} catch (Exception $e) {
    $dbError = "خطأ في قاعدة البيانات: " . $e->getMessage();
}

// Format dates
if (isset($exhibition)) {
    $startDate = new DateTime($exhibition->start_date);
    $endDate = new DateTime($exhibition->end_date);
}
?>

<!DOCTYPE html>
<html lang="<?= $currentLang ?>" dir="<?= $currentLang === 'ar' ? 'rtl' : 'ltr' ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= isset($exhibition) ? htmlspecialchars($exhibition->title) : 'تفاصيل المعرض' ?> - Season Expo Kuwait</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg fixed w-full top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="/">
                        <img src="/images/logo.png" alt="Season Expo Kuwait" style="height: 40px; width: auto;" class="hover:opacity-80 transition-opacity">
                    </a>
                </div>
                <div class="flex items-center space-x-reverse space-x-4">
                    <a href="/" class="text-gray-600 hover:text-gray-900">الصفحة الرئيسية</a>
                    <a href="/exhibitions" class="text-gray-600 hover:text-gray-900">المعارض</a>
                    <a href="/admin/dashboard.php" class="text-gray-600 hover:text-gray-900">حسابي</a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="pt-16">
        <div class="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
            
            <?php if (isset($dbError)): ?>
            <div class="bg-red-50 border border-red-200 rounded-lg p-6">
                <div class="flex items-center">
                    <div class="text-red-600 text-xl mr-3">❌</div>
                    <div>
                        <h3 class="font-semibold text-red-800">خطأ في قاعدة البيانات</h3>
                        <p class="text-red-700"><?= $dbError ?></p>
                    </div>
                </div>
            </div>
            <?php elseif (isset($exhibition)): ?>
            
            <!-- Exhibition Header -->
            <div class="bg-white rounded-lg shadow-lg overflow-hidden mb-8">
                <div class="h-64 bg-gradient-to-r from-blue-600 to-purple-600 relative">
                    <?php if ($exhibition->featured_image): ?>
                    <img src="/storage/<?= htmlspecialchars($exhibition->featured_image) ?>" 
                         alt="<?= htmlspecialchars($exhibition->title) ?>"
                         class="w-full h-full object-cover">
                    <?php else: ?>
                    <div class="w-full h-full flex items-center justify-center text-white">
                        <div class="text-center">
                            <div class="text-6xl mb-4">🏢</div>
                            <h1 class="text-3xl font-bold"><?= htmlspecialchars($exhibition->title) ?></h1>
                        </div>
                    </div>
                    <?php endif; ?>
                    
                    <div class="absolute inset-0 bg-black bg-opacity-40 flex items-end">
                        <div class="p-6 text-white">
                            <h1 class="text-4xl font-bold mb-2"><?= htmlspecialchars($exhibition->title) ?></h1>
                            <p class="text-xl"><?= htmlspecialchars($exhibition->description) ?></p>
                        </div>
                    </div>
                </div>
                
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                        <div class="text-center">
                            <div class="text-2xl mb-2">📅</div>
                            <h3 class="font-semibold mb-1">تاريخ المعرض</h3>
                            <p class="text-gray-600"><?= $startDate->format('d/m/Y') ?> - <?= $endDate->format('d/m/Y') ?></p>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl mb-2">📍</div>
                            <h3 class="font-semibold mb-1">المكان</h3>
                            <p class="text-gray-600"><?= htmlspecialchars($exhibition->venue_name) ?></p>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl mb-2">🏙️</div>
                            <h3 class="font-semibold mb-1">المدينة</h3>
                            <p class="text-gray-600"><?= htmlspecialchars($exhibition->city) ?></p>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl mb-2">💰</div>
                            <h3 class="font-semibold mb-1">العملة</h3>
                            <p class="text-gray-600"><?= htmlspecialchars($exhibition->currency ?? 'KWD') ?></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Available Booths -->
            <div class="bg-white rounded-lg shadow-lg p-6" id="booking">
                <h2 class="text-2xl font-bold text-gray-900 mb-6">🏢 الأجنحة المتاحة</h2>
                
                <?php if (isset($booths) && count($booths) > 0): ?>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <?php foreach ($booths as $booth): ?>
                    <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                        <div class="flex items-center justify-between mb-3">
                            <h3 class="font-semibold text-lg"><?= htmlspecialchars($booth->name ?? $booth->booth_number) ?></h3>
                            <span class="px-2 py-1 rounded-full text-xs font-medium <?= $booth->status === 'available' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' ?>">
                                <?= $booth->status === 'available' ? 'متاح' : 'محجوز' ?>
                            </span>
                        </div>
                        
                        <div class="space-y-2 mb-4">
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600">المساحة:</span>
                                <span class="font-medium"><?= $booth->area ?> م²</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600">الموقع:</span>
                                <span class="font-medium"><?= htmlspecialchars($booth->location) ?></span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600">السعر:</span>
                                <span class="font-bold text-green-600"><?= number_format($booth->price) ?> <?= $exhibition->currency ?? 'KWD' ?></span>
                            </div>
                        </div>
                        
                        <?php if ($booth->description): ?>
                        <p class="text-gray-600 text-sm mb-4"><?= htmlspecialchars($booth->description) ?></p>
                        <?php endif; ?>
                        
                        <?php if ($booth->status === 'available'): ?>
                        <a href="/booking/create.php?booth_id=<?= $booth->id ?>&exhibition_id=<?= $exhibition->id ?>"
                           class="block w-full bg-blue-600 text-white text-center py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors">
                            احجز الآن
                        </a>
                        <?php else: ?>
                        <button class="block w-full bg-gray-400 text-white text-center py-2 px-4 rounded-lg cursor-not-allowed">
                            غير متاح
                        </button>
                        <?php endif; ?>
                    </div>
                    <?php endforeach; ?>
                </div>
                <?php else: ?>
                <div class="text-center py-12">
                    <div class="text-6xl mb-4">🏢</div>
                    <h3 class="text-xl font-bold text-gray-900 mb-2">لا توجد أجنحة متاحة</h3>
                    <p class="text-gray-600">جميع الأجنحة محجوزة أو لا توجد أجنحة لهذا المعرض</p>
                </div>
                <?php endif; ?>
            </div>
            
            <?php endif; ?>
        </div>
    </div>
</body>
</html>

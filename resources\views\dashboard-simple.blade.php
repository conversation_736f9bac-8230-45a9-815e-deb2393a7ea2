<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>لوحة التحكم - Season Expo</title>

    <!-- Custom Favicon -->
    <link rel="icon" type="image/svg+xml" href="/season-expo-favicon.svg">
    <link rel="icon" type="image/x-icon" href="/season-expo-favicon.ico">
    <link rel="shortcut icon" href="/season-expo-favicon.ico">

    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
    </style>
</head>
<body class="min-h-screen bg-gray-50">
    <!-- Fixed Navigation -->
    <nav class="fixed top-0 left-0 right-0 bg-white shadow-sm z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="/" class="text-2xl font-bold text-blue-600 hover:text-blue-700 transition-colors">
                        Season Expo
                    </a>
                </div>
                <div class="flex items-center space-x-reverse space-x-4">
                    @if(auth()->user()->role === 'admin')
                        <a href="/admin/exhibitions" class="text-red-600 hover:text-red-700 font-semibold">إدارة المعارض</a>
                    @endif
                    <a href="/exhibitions" class="text-gray-600 hover:text-gray-900">المعارض</a>

                    <a href="/signatures" class="text-emerald-600 hover:text-emerald-700 font-semibold">التوقيعات الرقمية</a>

                    <a href="/profile" class="text-blue-600 hover:text-blue-700 font-semibold">الملف الشخصي</a>
                    <a href="/" class="text-gray-600 hover:text-gray-900">الصفحة الرئيسية</a>
                    <form method="POST" action="/logout" class="inline">
                        @csrf
                        <button type="submit" class="text-gray-600 hover:text-gray-900 bg-transparent border-none cursor-pointer">
                            تسجيل الخروج
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </nav>

    <div class="pt-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">



            <!-- Welcome Section -->
            <div class="overflow-hidden bg-white shadow-sm sm:rounded-lg mb-8">
                <div class="p-6 text-gray-900">
                    <h2 class="text-2xl font-bold mb-4">مرحباً بك في Season Expo</h2>
                    <p class="text-gray-600 mb-4">
                        منصة حجز المعارض والأجنحة الخاصة بك جاهزة. ابدأ في استكشاف المعارض واحجز مساحة الجناح المثالية.
                    </p>
                </div>
            </div>

            <!-- Quick Stats -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="text-2xl">🏢</div>
                            </div>
                            <div class="mr-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">المعارض النشطة</dt>
                                    <dd class="text-lg font-medium text-gray-900">{{ \App\Models\Exhibition::where('status', 'active')->count() }}</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="text-2xl">📋</div>
                            </div>
                            <div class="mr-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">حجوزاتي</dt>
                                    <dd class="text-lg font-medium text-gray-900">{{ auth()->user()->bookings()->count() }}</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="text-2xl">✍️</div>
                            </div>
                            <div class="mr-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">توقيعاتي الرقمية</dt>
                                    <dd class="text-lg font-medium text-gray-900">{{ \App\Models\DigitalSignature::where('user_id', auth()->id())->count() }}</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="text-2xl">📊</div>
                            </div>
                            <div class="mr-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">النشاط الشهري</dt>
                                    <dd class="text-lg font-medium text-gray-900">{{ auth()->user()->bookings()->whereMonth('created_at', now()->month)->count() }}</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-lg font-medium text-gray-900 mb-4">الإجراءات السريعة</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">

                    <!-- Digital Signatures -->
                    <a href="/signatures" class="flex items-center p-4 border border-emerald-300 rounded-lg hover:border-emerald-400 hover:bg-emerald-50 transition-colors bg-emerald-25">
                        <div class="text-2xl mr-3">✍️</div>
                        <div>
                            <div class="font-medium text-gray-900">التوقيعات الرقمية</div>
                            <div class="text-sm text-gray-500">إنشاء وإدارة التوقيعات والمستندات الرقمية</div>
                        </div>
                    </a>

                    <a href="/exhibitions" class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors">
                        <svg class="h-6 w-6 text-blue-600 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                        <div>
                            <div class="font-medium text-gray-900">تصفح المعارض</div>
                            <div class="text-sm text-gray-500">اعثر على الحدث التالي</div>
                        </div>
                    </a>

                    <a href="/dashboard/booth-search" class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-green-300 hover:bg-green-50 transition-colors">
                        <svg class="h-6 w-6 text-green-600 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        <div>
                            <div class="font-medium text-gray-900">البحث عن الأجنحة</div>
                            <div class="text-sm text-gray-500">ابحث عن المساحة المثالية</div>
                        </div>
                    </a>

                    <a href="/dashboard/reservations" class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-purple-300 hover:bg-purple-50 transition-colors">
                        <svg class="h-6 w-6 text-purple-600 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                        </svg>
                        <div>
                            <div class="font-medium text-gray-900">حجوزاتي</div>
                            <div class="text-sm text-gray-500">إدارة الحجوزات</div>
                        </div>
                    </a>

                    <a href="/profile" class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-indigo-300 hover:bg-indigo-50 transition-colors">
                        <svg class="h-6 w-6 text-indigo-600 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                        <div>
                            <div class="font-medium text-gray-900">الملف الشخصي</div>
                            <div class="text-sm text-gray-500">إدارة معلوماتك</div>
                        </div>
                    </a>

                    <a href="#" class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-orange-300 hover:bg-orange-50 transition-colors">
                        <svg class="h-6 w-6 text-orange-600 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M12 2.25a9.75 9.75 0 109.75 9.75A9.75 9.75 0 0012 2.25z"></path>
                        </svg>
                        <div>
                            <div class="font-medium text-gray-900">الحصول على الدعم</div>
                            <div class="text-sm text-gray-500">تحتاج مساعدة؟</div>
                        </div>
                    </a>
                </div>
            </div>

            <!-- Footer -->
            <div class="mt-12 text-center text-gray-500">
                <p>&copy; {{ date('Y') }} Season Expo. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </div>
</body>
</html>

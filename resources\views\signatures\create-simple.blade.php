<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>إنشاء توقيع رقمي - Season Expo</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        #signature-canvas {
            border: 2px dashed #d1d5db;
            cursor: crosshair;
        }
        #signature-canvas:hover {
            border-color: #3b82f6;
        }
    </style>
</head>
<body class="min-h-screen bg-gray-50">
    <!-- Fixed Navigation -->
    <nav class="fixed top-0 left-0 right-0 bg-white shadow-sm z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="/" class="text-2xl font-bold text-blue-600 hover:text-blue-700">Season Expo</a>
                </div>
                <div class="flex items-center space-x-reverse space-x-4">
                    <a href="/signatures" class="text-gray-600 hover:text-gray-900">التوقيعات الرقمية</a>
                    <a href="/dashboard" class="text-gray-600 hover:text-gray-900">لوحة التحكم</a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="pt-16 min-h-screen">
        <div class="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
            <!-- Header -->
            <div class="mb-8">
                <h1 class="text-3xl font-bold text-gray-900">إنشاء توقيع رقمي جديد</h1>
                <p class="text-gray-600 mt-2">قم بإنشاء توقيع رقمي آمن ومعتمد</p>
            </div>

            <!-- Form -->
            <form method="POST" action="/signatures/store-simple" class="space-y-8">
                @csrf

                <!-- Document Information -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h2 class="text-xl font-semibold text-gray-900 mb-4">معلومات المستند</h2>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Document Type -->
                        <div>
                            <label for="document_type" class="block text-sm font-medium text-gray-700 mb-2">نوع المستند</label>
                            <select id="document_type" name="document_type" required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <option value="">اختر نوع المستند</option>
                                <option value="booking_contract">عقد حجز جناح</option>
                                <option value="exhibition_agreement">اتفاقية معرض</option>
                                <option value="payment_receipt">إيصال دفع</option>
                                <option value="terms_conditions">الشروط والأحكام</option>
                                <option value="other">أخرى</option>
                            </select>
                        </div>

                        <!-- Document ID -->
                        <div>
                            <label for="document_id" class="block text-sm font-medium text-gray-700 mb-2">رقم المستند</label>
                            <input type="text" id="document_id" name="document_id" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                   placeholder="مثال: BOOKING-001">
                        </div>
                    </div>

                    <!-- Document Title -->
                    <div class="mt-6">
                        <label for="document_title" class="block text-sm font-medium text-gray-700 mb-2">عنوان المستند</label>
                        <input type="text" id="document_title" name="document_title" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="أدخل عنوان المستند">
                    </div>
                </div>

                <!-- Signer Information -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h2 class="text-xl font-semibold text-gray-900 mb-4">معلومات الموقع</h2>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Signer Name -->
                        <div>
                            <label for="signer_name" class="block text-sm font-medium text-gray-700 mb-2">اسم الموقع</label>
                            <input type="text" id="signer_name" name="signer_name" value="{{ auth()->user()->name }}" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>

                        <!-- Signer Position -->
                        <div>
                            <label for="signer_position" class="block text-sm font-medium text-gray-700 mb-2">المنصب</label>
                            <input type="text" id="signer_position" name="signer_position"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                   placeholder="مثال: مدير المعارض">
                        </div>
                    </div>
                </div>

                <!-- Digital Signature -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h2 class="text-xl font-semibold text-gray-900 mb-4">التوقيع الرقمي</h2>
                    
                    <!-- Signature Canvas -->
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">ارسم توقيعك هنا</label>
                        <div class="border-2 border-dashed border-gray-300 rounded-lg p-4 bg-gray-50">
                            <canvas id="signature-canvas" width="600" height="200" class="w-full bg-white rounded"></canvas>
                        </div>
                        <div class="mt-2 flex gap-2">
                            <button type="button" id="clear-signature" 
                                    class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600">
                                مسح التوقيع
                            </button>
                            <button type="button" id="undo-signature" 
                                    class="px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600">
                                تراجع
                            </button>
                        </div>
                    </div>

                    <!-- Hidden field for signature data -->
                    <input type="hidden" id="signature_data" name="signature_data" required>
                </div>

                <!-- Terms and Conditions -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex items-start">
                        <input type="checkbox" id="terms" name="terms" required
                               class="mt-1 h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                        <label for="terms" class="mr-2 text-sm text-gray-700">
                            أوافق على أن هذا التوقيع الرقمي له نفس القوة القانونية للتوقيع اليدوي وأتحمل المسؤولية الكاملة عن استخدامه
                        </label>
                    </div>
                </div>

                <!-- Submit Button -->
                <div class="flex justify-end gap-4">
                    <a href="/signatures" 
                       class="px-6 py-3 bg-gray-500 text-white rounded-lg font-semibold hover:bg-gray-600 transition-colors">
                        إلغاء
                    </a>
                    <button type="submit" id="submit-signature"
                            class="px-6 py-3 bg-blue-600 text-white rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                        إنشاء التوقيع الرقمي
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- JavaScript for Signature Canvas -->
    <script>
        const canvas = document.getElementById('signature-canvas');
        const ctx = canvas.getContext('2d');
        let isDrawing = false;
        let strokes = [];
        let currentStroke = [];

        // Set canvas size
        canvas.width = 600;
        canvas.height = 200;

        // Drawing functions
        function startDrawing(e) {
            isDrawing = true;
            currentStroke = [];
            draw(e);
        }

        function draw(e) {
            if (!isDrawing) return;

            const rect = canvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            currentStroke.push({x, y});

            ctx.lineWidth = 2;
            ctx.lineCap = 'round';
            ctx.strokeStyle = '#000';

            if (currentStroke.length === 1) {
                ctx.beginPath();
                ctx.moveTo(x, y);
            } else {
                ctx.lineTo(x, y);
                ctx.stroke();
            }
        }

        function stopDrawing() {
            if (isDrawing) {
                isDrawing = false;
                strokes.push([...currentStroke]);
                updateSignatureData();
            }
        }

        function updateSignatureData() {
            const signatureData = canvas.toDataURL();
            document.getElementById('signature_data').value = signatureData;
        }

        function clearSignature() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            strokes = [];
            currentStroke = [];
            document.getElementById('signature_data').value = '';
        }

        function undoLastStroke() {
            if (strokes.length > 0) {
                strokes.pop();
                redrawCanvas();
                updateSignatureData();
            }
        }

        function redrawCanvas() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            ctx.lineWidth = 2;
            ctx.lineCap = 'round';
            ctx.strokeStyle = '#000';

            strokes.forEach(stroke => {
                if (stroke.length > 0) {
                    ctx.beginPath();
                    ctx.moveTo(stroke[0].x, stroke[0].y);
                    stroke.forEach(point => {
                        ctx.lineTo(point.x, point.y);
                    });
                    ctx.stroke();
                }
            });
        }

        // Event listeners
        canvas.addEventListener('mousedown', startDrawing);
        canvas.addEventListener('mousemove', draw);
        canvas.addEventListener('mouseup', stopDrawing);
        canvas.addEventListener('mouseout', stopDrawing);

        // Touch events for mobile
        canvas.addEventListener('touchstart', (e) => {
            e.preventDefault();
            const touch = e.touches[0];
            const mouseEvent = new MouseEvent('mousedown', {
                clientX: touch.clientX,
                clientY: touch.clientY
            });
            canvas.dispatchEvent(mouseEvent);
        });

        canvas.addEventListener('touchmove', (e) => {
            e.preventDefault();
            const touch = e.touches[0];
            const mouseEvent = new MouseEvent('mousemove', {
                clientX: touch.clientX,
                clientY: touch.clientY
            });
            canvas.dispatchEvent(mouseEvent);
        });

        canvas.addEventListener('touchend', (e) => {
            e.preventDefault();
            const mouseEvent = new MouseEvent('mouseup', {});
            canvas.dispatchEvent(mouseEvent);
        });

        // Button event listeners
        document.getElementById('clear-signature').addEventListener('click', clearSignature);
        document.getElementById('undo-signature').addEventListener('click', undoLastStroke);

        // Form validation
        document.querySelector('form').addEventListener('submit', function(e) {
            const signatureData = document.getElementById('signature_data').value;
            if (!signatureData) {
                e.preventDefault();
                alert('يرجى رسم التوقيع أولاً');
                return false;
            }
        });
    </script>
</body>
</html>

<?php
// Enable all error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h1>Web Request Debug</h1>";

try {
    echo "<h2>Step 1: Basic PHP Test</h2>";
    echo "✅ PHP is working<br>";
    echo "PHP Version: " . phpversion() . "<br>";
    
    echo "<h2>Step 2: File Path Test</h2>";
    $rootPath = dirname(__DIR__);
    echo "Root path: {$rootPath}<br>";
    
    // Check critical files
    $files = [
        'vendor/autoload.php',
        'bootstrap/app.php',
        '.env'
    ];
    
    foreach ($files as $file) {
        $fullPath = $rootPath . '/' . $file;
        if (file_exists($fullPath)) {
            echo "✅ {$file} exists<br>";
        } else {
            echo "❌ {$file} missing<br>";
        }
    }
    
    echo "<h2>Step 3: Laravel Bootstrap Test</h2>";
    
    // Load <PERSON>vel step by step
    require $rootPath . '/vendor/autoload.php';
    echo "✅ Autoloader loaded<br>";
    
    $app = require $rootPath . '/bootstrap/app.php';
    echo "✅ Laravel app created<br>";
    
    echo "<h2>Step 4: HTTP Kernel Test</h2>";
    
    // Try to create HTTP kernel
    $kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);
    echo "✅ HTTP Kernel created<br>";
    
    echo "<h2>Step 5: Request Simulation</h2>";
    
    // Simulate a web request
    $request = Illuminate\Http\Request::create('/', 'GET');
    echo "✅ Request created<br>";
    
    // Try to handle the request
    $response = $kernel->handle($request);
    echo "✅ Request handled successfully<br>";
    echo "Response status: " . $response->getStatusCode() . "<br>";
    echo "Response content length: " . strlen($response->getContent()) . " bytes<br>";
    
    // Show first 500 characters of response
    $content = $response->getContent();
    if (strlen($content) > 0) {
        echo "<h3>Response Preview:</h3>";
        echo "<pre style='background: #f5f5f5; padding: 10px; max-height: 200px; overflow-y: auto;'>";
        echo htmlspecialchars(substr($content, 0, 500));
        if (strlen($content) > 500) {
            echo "\n... (truncated)";
        }
        echo "</pre>";
    }
    
    $kernel->terminate($request, $response);
    echo "✅ Request terminated<br>";
    
    echo "<br><div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px;'>";
    echo "<h3 style='color: #155724;'>✅ Web Request Successful!</h3>";
    echo "<p>Laravel can handle web requests properly. The issue might be with your index.php file.</p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<br><div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px;'>";
    echo "<h3 style='color: #721c24;'>❌ Error Found!</h3>";
    echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>File:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    echo "<p><strong>Trace:</strong></p>";
    echo "<pre style='background: #f8f8f8; padding: 10px; max-height: 300px; overflow-y: auto;'>";
    echo htmlspecialchars($e->getTraceAsString());
    echo "</pre>";
    echo "</div>";
}

echo "<h2>Next Steps:</h2>";
echo "<ol>";
echo "<li><a href='check-index-file.php'>Check index.php file</a></li>";
echo "<li><a href='fix-index.php'>Fix index.php if needed</a></li>";
echo "<li><a href='/'>Test main application</a></li>";
echo "</ol>";
?>

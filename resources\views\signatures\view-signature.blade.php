<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>عرض التوقيع الرقمي - Season Expo</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            line-height: 1.6;
        }
        .signature-container {
            background: white;
            border: 2px solid #0066cc;
            border-radius: 10px;
            padding: 30px;
            margin: 20px 0;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .signature-header {
            text-align: center;
            border-bottom: 2px solid #0066cc;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .signature-title {
            font-size: 24px;
            font-weight: bold;
            color: #0066cc;
            margin-bottom: 10px;
        }
        .signature-image {
            border: 2px solid #0066cc;
            border-radius: 8px;
            padding: 20px;
            background: #f8f9fa;
            text-align: center;
            margin: 20px 0;
        }
        .signature-image img {
            max-width: 100%;
            max-height: 200px;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            background: white;
            padding: 10px;
        }
        .signature-details {
            background: #e8f4fd;
            border: 1px solid #0066cc;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .detail-row {
            display: flex;
            justify-content: space-between;
            margin: 10px 0;
            padding: 8px 0;
            border-bottom: 1px solid #ddd;
        }
        .detail-label {
            font-weight: bold;
            color: #333;
        }
        .detail-value {
            color: #666;
        }
        .verification-section {
            background: #d4edda;
            border: 2px solid #28a745;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        .verification-failed {
            background: #f8d7da;
            border-color: #dc3545;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
            transition: all 0.3s;
        }
        .btn-primary {
            background: #0066cc;
            color: white;
        }
        .btn-primary:hover {
            background: #004499;
        }
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        .btn-secondary:hover {
            background: #545b62;
        }
        .btn-success {
            background: #28a745;
            color: white;
        }
        .btn-success:hover {
            background: #1e7e34;
        }
        .metadata-section {
            background: #fff3cd;
            border: 1px solid #ffc107;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        .metadata-content {
            background: white;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body class="min-h-screen bg-gray-50">
    <!-- Fixed Navigation -->
    <nav class="fixed top-0 left-0 right-0 bg-white shadow-sm z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="/" class="text-2xl font-bold text-blue-600 hover:text-blue-700">Season Expo</a>
                </div>
                <div class="flex items-center space-x-reverse space-x-4">
                    <a href="/signatures" class="text-gray-600 hover:text-gray-900">التوقيعات الرقمية</a>
                    <a href="/dashboard" class="text-gray-600 hover:text-gray-900">لوحة التحكم</a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="pt-16 min-h-screen">
        <div class="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
            
            <!-- Signature Display Container -->
            <div class="signature-container">
                <div class="signature-header">
                    <div class="signature-title">عرض التوقيع الرقمي</div>
                    <p class="text-gray-600">تفاصيل التوقيع الرقمي والتحقق من صحته</p>
                </div>

                <!-- Signature Image Display -->
                <div class="signature-image">
                    <h3 style="color: #0066cc; margin-bottom: 15px;">التوقيع الرقمي</h3>
                    <div id="signature-display">
                        <!-- Signature image will be loaded here -->
                        <div class="text-gray-500">جاري تحميل التوقيع...</div>
                    </div>
                </div>

                <!-- Signature Details -->
                <div class="signature-details">
                    <h3 style="color: #0066cc; margin-bottom: 15px;">تفاصيل التوقيع</h3>
                    <div id="signature-details">
                        <!-- Details will be loaded here -->
                    </div>
                </div>

                <!-- Verification Status -->
                <div id="verification-status" class="verification-section">
                    <h3 style="color: #28a745; margin-bottom: 15px;">حالة التحقق</h3>
                    <div id="verification-content">
                        <!-- Verification status will be loaded here -->
                    </div>
                </div>

                <!-- Metadata Section -->
                <div class="metadata-section">
                    <h3 style="color: #856404; margin-bottom: 15px;">البيانات الوصفية</h3>
                    <div id="metadata-content" class="metadata-content">
                        <!-- Metadata will be loaded here -->
                    </div>
                </div>

                <!-- Action Buttons -->
                <div style="text-align: center; margin-top: 30px;">
                    <a href="/signatures" class="btn btn-secondary">العودة للتوقيعات</a>
                    <button onclick="downloadSignature()" class="btn btn-success">تحميل التوقيع</button>
                    <button onclick="verifySignature()" class="btn btn-primary">التحقق من التوقيع</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Get signature ID from URL
        const urlParams = new URLSearchParams(window.location.search);
        const signatureId = urlParams.get('id') || window.location.pathname.split('/').pop();

        // Load signature data
        async function loadSignatureData() {
            try {
                const response = await fetch(`/api/signatures/${signatureId}`);
                if (!response.ok) {
                    throw new Error('فشل في تحميل بيانات التوقيع');
                }
                
                const signature = await response.json();
                displaySignature(signature);
            } catch (error) {
                console.error('Error loading signature:', error);
                document.getElementById('signature-display').innerHTML = 
                    '<div class="text-red-500">خطأ في تحميل التوقيع: ' + error.message + '</div>';
            }
        }

        function displaySignature(signature) {
            // Display signature image
            const signatureDisplay = document.getElementById('signature-display');
            if (signature.signature_data) {
                signatureDisplay.innerHTML = `
                    <img src="${signature.signature_data}" 
                         alt="التوقيع الرقمي" 
                         style="max-width: 100%; max-height: 200px; border: 1px solid #ddd; border-radius: 5px; background: white; padding: 10px;">
                `;
            } else {
                signatureDisplay.innerHTML = '<div class="text-gray-500">لا توجد صورة توقيع متاحة</div>';
            }

            // Display signature details
            const detailsContainer = document.getElementById('signature-details');
            detailsContainer.innerHTML = `
                <div class="detail-row">
                    <span class="detail-label">رقم التوقيع:</span>
                    <span class="detail-value">${signature.id}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">نوع المستند:</span>
                    <span class="detail-value">${signature.document_type}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">رقم المستند:</span>
                    <span class="detail-value">${signature.document_id}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">اسم الموقع:</span>
                    <span class="detail-value">${signature.signer_name}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">البريد الإلكتروني:</span>
                    <span class="detail-value">${signature.signer_email}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">تاريخ التوقيع:</span>
                    <span class="detail-value">${new Date(signature.signed_at).toLocaleString('ar-EG')}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">عنوان IP:</span>
                    <span class="detail-value">${signature.signer_ip}</span>
                </div>
            `;

            // Display verification status
            const verificationContainer = document.getElementById('verification-status');
            const verificationContent = document.getElementById('verification-content');
            
            if (signature.is_verified) {
                verificationContainer.className = 'verification-section';
                verificationContent.innerHTML = `
                    <div style="color: #28a745;">
                        <div style="font-size: 48px; margin-bottom: 10px;">✅</div>
                        <h4 style="font-weight: bold;">التوقيع محقق ومعتمد</h4>
                        <p>تم التحقق من صحة هذا التوقيع في: ${new Date(signature.verified_at).toLocaleString('ar-EG')}</p>
                    </div>
                `;
            } else {
                verificationContainer.className = 'verification-section verification-failed';
                verificationContent.innerHTML = `
                    <div style="color: #dc3545;">
                        <div style="font-size: 48px; margin-bottom: 10px;">❌</div>
                        <h4 style="font-weight: bold;">التوقيع غير محقق</h4>
                        <p>لم يتم التحقق من صحة هذا التوقيع بعد</p>
                    </div>
                `;
            }

            // Display metadata
            const metadataContainer = document.getElementById('metadata-content');
            if (signature.metadata) {
                const metadata = typeof signature.metadata === 'string' 
                    ? JSON.parse(signature.metadata) 
                    : signature.metadata;
                metadataContainer.textContent = JSON.stringify(metadata, null, 2);
            } else {
                metadataContainer.textContent = 'لا توجد بيانات وصفية متاحة';
            }
        }

        function downloadSignature() {
            window.open(`/signatures/${signatureId}/download`, '_blank');
        }

        function verifySignature() {
            // This would typically use the verification token
            window.open(`/signatures/verify/${signatureId}`, '_blank');
        }

        // Load signature data when page loads
        document.addEventListener('DOMContentLoaded', loadSignatureData);
    </script>
</body>
</html>

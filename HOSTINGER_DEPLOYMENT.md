# Season Expo - Hostinger Deployment Guide

## 🚨 Current Error Fix

The error "Please provide a valid cache path" occurs because <PERSON><PERSON> can't write to cache directories on shared hosting. Here's how to fix it:

### 1. Create Required Directories

Create these directories in your hosting file manager or via FTP:

```
/home/<USER>/domains/myapps.fjt-q8.com/public_html/
├── storage/
│   ├── app/
│   │   └── public/
│   ├── framework/
│   │   ├── cache/
│   │   │   └── data/
│   │   ├── sessions/
│   │   ├── testing/
│   │   └── views/
│   └── logs/
└── bootstrap/
    └── cache/
```

### 2. Set Directory Permissions

Set these permissions via your hosting control panel or FTP:

```bash
chmod 755 storage/
chmod 755 storage/app/
chmod 755 storage/app/public/
chmod 755 storage/framework/
chmod 755 storage/framework/cache/
chmod 755 storage/framework/cache/data/
chmod 755 storage/framework/sessions/
chmod 755 storage/framework/testing/
chmod 755 storage/framework/views/
chmod 755 storage/logs/
chmod 755 bootstrap/
chmod 755 bootstrap/cache/
```

### 3. Update .env File for Production

Create/update your `.env` file on the server:

```env
APP_NAME="Season Expo"
APP_ENV=production
APP_KEY=base64:yBDRJKpc2hehiJkqzrDRX8HhD9vhotAqWEgsRiJEa24=
APP_DEBUG=false
APP_URL=https://myapps.fjt-q8.com

APP_LOCALE=ar
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

# Database Configuration (Update with your Hostinger MySQL details)
DB_CONNECTION=mysql
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=u460422474_seasonexpodb
DB_USERNAME=u460422474_seasonexpo
DB_PASSWORD=your_database_password

# Cache Configuration
CACHE_STORE=file
SESSION_DRIVER=file
QUEUE_CONNECTION=sync

# File Storage
FILESYSTEM_DISK=public

# Mail Configuration (Update with your hosting email settings)
MAIL_MAILER=smtp
MAIL_HOST=smtp.hostinger.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_email_password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="Season Expo"
```

### 4. Create Storage Link

Add this to your `public/index.php` after the autoloader:

```php
// Create storage link if it doesn't exist
if (!file_exists(__DIR__.'/storage')) {
    symlink(__DIR__.'/../storage/app/public', __DIR__.'/storage');
}
```

### 5. Clear All Caches

Run these commands via SSH or create a clear-cache.php file:

```php
<?php
// clear-cache.php - Upload this file and run it once
require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);

// Clear all caches
$kernel->call('config:clear');
$kernel->call('cache:clear');
$kernel->call('view:clear');
$kernel->call('route:clear');

echo "All caches cleared successfully!";
?>
```

## 📁 File Structure for Hostinger

Your hosting should have this structure:

```
/home/<USER>/domains/myapps.fjt-q8.com/
├── public_html/ (This is your Laravel public folder)
│   ├── index.php
│   ├── .htaccess
│   ├── css/
│   ├── js/
│   └── storage/ (symlink)
├── app/
├── bootstrap/
├── config/
├── database/
├── resources/
├── routes/
├── storage/
├── vendor/
├── .env
└── composer.json
```

## 🗄️ Database Setup on Hostinger

1. **Create MySQL Database:**
   - Go to Hostinger Control Panel
   - Navigate to MySQL Databases
   - Create database: `u460422474_seasonexpodb`
   - Create user: `u460422474_seasonexpo`
   - Assign user to database with all privileges

2. **Import Database:**
   - Use phpMyAdmin or MySQL import
   - Import your local database backup
   - Or run migrations via SSH

3. **Update Database Credentials:**
   - Update `.env` with Hostinger database details
   - Database names usually have your user ID prefix

## 🔧 Additional Fixes

### Fix .htaccess (in public_html/)

```apache
<IfModule mod_rewrite.c>
    <IfModule mod_negotiation.c>
        Options -MultiViews -Indexes
    </IfModule>

    RewriteEngine On

    # Handle Authorization Header
    RewriteCond %{HTTP:Authorization} .
    RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]

    # Redirect Trailing Slashes If Not A Folder...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_URI} (.+)/$
    RewriteRule ^ %1 [L,R=301]

    # Send Requests To Front Controller...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^ index.php [L]
</IfModule>
```

### Create bootstrap/cache/.gitkeep

```
# This file ensures the cache directory exists
```

## 🚀 Deployment Checklist

- [ ] Upload all files except `node_modules` and local `.env`
- [ ] Create production `.env` file
- [ ] Create required directories with proper permissions
- [ ] Set up database and import data
- [ ] Create storage symlink
- [ ] Clear all caches
- [ ] Test the application

## 🔍 Troubleshooting

If you still get errors:

1. **Check file permissions** - All directories should be 755
2. **Verify .env file** - Ensure all paths and credentials are correct
3. **Check error logs** - Look in `storage/logs/laravel.log`
4. **Contact Hostinger support** - They can help with server-specific issues

## 📞 Quick Fix Commands

If you have SSH access, run these:

```bash
# Create directories
mkdir -p storage/framework/{cache/data,sessions,testing,views}
mkdir -p storage/{app/public,logs}
mkdir -p bootstrap/cache

# Set permissions
chmod -R 755 storage bootstrap/cache

# Clear caches
php artisan config:clear
php artisan cache:clear
php artisan view:clear
```

This should resolve the cache path error and get your application running on Hostinger.

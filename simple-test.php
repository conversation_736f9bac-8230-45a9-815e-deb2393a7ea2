<?php
// Simple Laravel test without complex features
ini_set('display_errors', 1);
error_reporting(E_ALL);

echo "<h1>Simple Laravel Test</h1>";

try {
    echo "Step 1: Loading autoloader...<br>";
    require '../vendor/autoload.php';
    echo "✅ Autoloader loaded<br>";
    
    echo "Step 2: Loading Laravel app...<br>";
    $app = require '../bootstrap/app.php';
    echo "✅ Laravel app loaded<br>";
    
    echo "Step 3: Testing basic functionality...<br>";
    
    // Test without complex routing
    echo "App class: " . get_class($app) . "<br>";
    
    // Test configuration
    $config = $app->make('config');
    echo "✅ Config service working<br>";
    
    // Test database connection
    $dbConfig = $config->get('database.connections.mysql');
    echo "Database config loaded<br>";
    
    try {
        $pdo = new PDO(
            "mysql:host={$dbConfig['host']};dbname={$dbConfig['database']}",
            $dbConfig['username'],
            $dbConfig['password']
        );
        echo "✅ Database connection successful<br>";
        
        // Test a simple query
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM exhibitions");
        $result = $stmt->fetch();
        echo "✅ Database query successful: {$result['count']} exhibitions<br>";
        
    } catch (Exception $e) {
        echo "❌ Database error: " . $e->getMessage() . "<br>";
    }
    
    echo "<br><h2>✅ Basic Laravel functionality is working!</h2>";
    echo "<p>The 500 error might be caused by:</p>";
    echo "<ul>";
    echo "<li>Route configuration issues</li>";
    echo "<li>Controller errors</li>";
    echo "<li>View compilation problems</li>";
    echo "<li>Middleware issues</li>";
    echo "</ul>";
    
    echo "<h2>Next Steps:</h2>";
    echo "<ol>";
    echo "<li><a href='check-errors.php'>Check detailed error logs</a></li>";
    echo "<li><a href='clear-all-caches.php'>Clear all caches</a></li>";
    echo "<li>Check Hostinger error logs in control panel</li>";
    echo "</ol>";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
    echo "File: " . $e->getFile() . "<br>";
    echo "Line: " . $e->getLine() . "<br>";
}
?>

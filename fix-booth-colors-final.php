<?php
// Final Fix for Booth Colors
// This file provides a complete solution for the booth color issue

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='utf-8'>";
echo "<title>الحل النهائي لألوان الأجنحة - Season Expo Kuwait</title>";
echo "<script src='https://cdn.tailwindcss.com'></script>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }";
echo "</style>";
echo "</head>";
echo "<body class='bg-gray-50 p-8'>";

echo "<div class='max-w-6xl mx-auto'>";
echo "<h1 class='text-3xl font-bold mb-8' style='color: #2C3E50;'>🔧 الحل النهائي لألوان الأجنحة</h1>";

// Step 1: Check current file
$currentFile = __DIR__ . '/exhibitions-1-simple.php';
$needsUpdate = false;

echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>📋 الخطوة 1: فحص الملف الحالي</h2>";

if (file_exists($currentFile)) {
    $fileContent = file_get_contents($currentFile);
    
    // Check for problematic patterns
    $problems = [];
    
    if (strpos($fileContent, 'style="background: #2C3E50;"') !== false) {
        $problems[] = "وجود خلفية داكنة مباشرة في الأجنحة";
        $needsUpdate = true;
    }
    
    if (strpos($fileContent, 'bg-gray-800') !== false || strpos($fileContent, 'bg-blue-800') !== false) {
        $problems[] = "استخدام كلاسات Tailwind داكنة";
        $needsUpdate = true;
    }
    
    if (count($problems) > 0) {
        echo "<div class='bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4'>";
        echo "<h3 class='font-bold mb-2'>❌ مشاكل موجودة:</h3>";
        echo "<ul class='list-disc list-inside'>";
        foreach ($problems as $problem) {
            echo "<li>{$problem}</li>";
        }
        echo "</ul>";
        echo "</div>";
    } else {
        echo "<div class='bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4'>";
        echo "✅ الملف يبدو محدث، لكن قد تحتاج لرفعه للسيرفر";
        echo "</div>";
    }
} else {
    echo "<div class='bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4'>";
    echo "❌ ملف exhibitions-1-simple.php غير موجود";
    echo "</div>";
    $needsUpdate = true;
}

echo "</div>";

// Step 2: Provide the complete fixed code
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>📝 الخطوة 2: الكود المحدث الكامل</h2>";

echo "<div class='bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4'>";
echo "<h3 class='font-semibold mb-2'>استبدل قسم الأجنحة المتاحة بهذا الكود:</h3>";
echo "<div class='bg-white p-3 rounded border overflow-x-auto'>";
echo "<pre class='text-xs'>";
echo htmlspecialchars('<!-- Available Booths -->
<?php if ($availableBooths->count() > 0): ?>
<div class="bg-white rounded-lg shadow-lg p-6 mb-8">
    <h2 class="text-2xl font-bold text-gray-900 mb-6">🎯 الأجنحة المتاحة</h2>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <?php foreach ($availableBooths as $booth): ?>
        <div class="booth-available booth-card border-2 rounded-lg p-4 hover:shadow-lg transition-all duration-300" 
             style="border-color: #2C3E50; background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);">
            <div class="flex justify-between items-start mb-3">
                <h3 class="font-bold text-lg" style="color: #2C3E50;"><?= $booth->booth_number ?></h3>
                <span class="status-badge text-white px-3 py-1 rounded-full text-sm font-semibold" 
                      style="background: #27AE60;">متاح</span>
            </div>
            <p class="mb-3" style="color: #34495E;"><?= $booth->description ?></p>
            <div class="space-y-2 text-sm">
                <div style="color: #2C3E50;"><span class="font-semibold">📏 المساحة:</span> <?= $booth->area ?> م²</div>
                <div style="color: #2C3E50;"><span class="font-semibold">📍 الموقع:</span> <?= $booth->location ?></div>
                <div class="price font-bold text-lg" style="color: #E74C3C;">💰 <?= number_format($booth->price) ?> KWD</div>
            </div>
            <a href="/booking-with-signature.php?booth_id=<?= $booth->id ?>&exhibition_id=<?= $exhibition->id ?>"
               class="block w-full mt-4 text-white py-2 px-4 rounded transition-colors text-center font-semibold"
               style="background: #2C3E50;"
               onmouseover="this.style.background=\'#1A252F\'"
               onmouseout="this.style.background=\'#2C3E50\'">
                احجز الآن (مع الإقرار والتعهد)
            </a>
        </div>
        <?php endforeach; ?>
    </div>
</div>
<?php endif; ?>');
echo "</pre>";
echo "</div>";
echo "</div>";

echo "<div class='bg-green-50 border border-green-200 rounded-lg p-4 mb-4'>";
echo "<h3 class='font-semibold mb-2'>استبدل قسم الأجنحة المحجوزة بهذا الكود:</h3>";
echo "<div class='bg-white p-3 rounded border overflow-x-auto'>";
echo "<pre class='text-xs'>";
echo htmlspecialchars('<!-- Booked Booths -->
<?php if ($bookedBooths->count() > 0): ?>
<div class="bg-white rounded-lg shadow-lg p-6">
    <h2 class="text-2xl font-bold text-gray-900 mb-6">📋 الأجنحة المحجوزة</h2>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <?php foreach ($bookedBooths as $booth): ?>
        <div class="booth-booked booth-card border-2 rounded-lg p-4" 
             style="border-color: #E74C3C; background: linear-gradient(135deg, #fdf2f2 0%, #f8f8f8 100%);">
            <div class="flex justify-between items-start mb-2">
                <h3 class="font-semibold" style="color: #2C3E50;"><?= $booth->booth_number ?></h3>
                <span class="status-badge text-white px-2 py-1 rounded-full text-xs font-semibold" 
                      style="background: #E74C3C;">محجوز</span>
            </div>
            <div class="text-sm">
                <div style="color: #2C3E50;"><span class="font-semibold">📏</span> <?= $booth->area ?> م²</div>
                <div style="color: #2C3E50;"><span class="font-semibold">📍</span> <?= $booth->location ?></div>
            </div>
        </div>
        <?php endforeach; ?>
    </div>
</div>
<?php endif; ?>');
echo "</pre>";
echo "</div>";
echo "</div>";

echo "<div class='bg-yellow-50 border border-yellow-200 rounded-lg p-4'>";
echo "<h3 class='font-semibold mb-2'>أضف هذا CSS في نهاية الملف قبل </body>:</h3>";
echo "<div class='bg-white p-3 rounded border overflow-x-auto'>";
echo "<pre class='text-xs'>";
echo htmlspecialchars('<style>
/* إصلاح نهائي لألوان الأجنحة */
.booth-card {
    background: white !important;
}

.booth-available {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%) !important;
    border: 2px solid #2C3E50 !important;
}

.booth-available h3,
.booth-available p,
.booth-available div {
    color: #2C3E50 !important;
}

.booth-available .price {
    color: #E74C3C !important;
    font-weight: bold !important;
}

.booth-available .status-badge {
    background: #27AE60 !important;
    color: white !important;
}

.booth-booked {
    background: linear-gradient(135deg, #fdf2f2 0%, #f8f8f8 100%) !important;
    border: 2px solid #E74C3C !important;
}

.booth-booked h3,
.booth-booked p,
.booth-booked div {
    color: #2C3E50 !important;
}

.booth-booked .status-badge {
    background: #E74C3C !important;
    color: white !important;
}

/* إزالة أي خلفيات داكنة */
div[style*="background: #2C3E50"] {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%) !important;
}

div[style*="background: #2C3E50"] h3,
div[style*="background: #2C3E50"] p,
div[style*="background: #2C3E50"] div {
    color: #2C3E50 !important;
}
</style>');
echo "</pre>";
echo "</div>";
echo "</div>";

echo "</div>";

// Step 3: Quick fix option
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>⚡ الخطوة 3: حل سريع (إضافة CSS فقط)</h2>";
echo "<p class='mb-4'>إذا كنت تريد حل سريع بدون تعديل الكود الأساسي، أضف هذا CSS في نهاية الملف:</p>";

echo "<div class='bg-gray-100 p-4 rounded-lg'>";
echo "<pre class='text-sm'>";
echo htmlspecialchars('<script>
// إصلاح فوري لألوان الأجنحة
document.addEventListener("DOMContentLoaded", function() {
    // البحث عن جميع الأجنحة ذات الخلفية الداكنة
    const darkBooths = document.querySelectorAll(\'div[style*="background: #2C3E50"]\');
    
    darkBooths.forEach(function(booth) {
        // تغيير الخلفية إلى فاتحة
        booth.style.background = "linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)";
        booth.style.border = "2px solid #2C3E50";
        
        // تغيير لون النصوص
        const texts = booth.querySelectorAll("h3, p, div");
        texts.forEach(function(text) {
            text.style.color = "#2C3E50";
        });
        
        // تغيير لون السعر
        const prices = booth.querySelectorAll("div:contains(\'KWD\')");
        prices.forEach(function(price) {
            price.style.color = "#E74C3C";
            price.style.fontWeight = "bold";
        });
    });
});
</script>');
echo "</pre>";
echo "</div>";

echo "</div>";

// Step 4: Instructions
echo "<div class='bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>📋 الخطوة 4: تعليمات التطبيق</h2>";

echo "<div class='grid grid-cols-1 md:grid-cols-2 gap-6'>";

echo "<div>";
echo "<h3 class='font-semibold mb-3' style='color: #2C3E50;'>الطريقة الأولى (الأفضل):</h3>";
echo "<ol class='list-decimal list-inside space-y-2 text-sm'>";
echo "<li>افتح ملف exhibitions-1-simple.php</li>";
echo "<li>استبدل قسم الأجنحة بالكود المحدث أعلاه</li>";
echo "<li>أضف CSS الإضافي في النهاية</li>";
echo "<li>ارفع الملف للسيرفر</li>";
echo "<li>امسح كاش المتصفح (Ctrl+F5)</li>";
echo "</ol>";
echo "</div>";

echo "<div>";
echo "<h3 class='font-semibold mb-3' style='color: #2C3E50;'>الطريقة الثانية (سريعة):</h3>";
echo "<ol class='list-decimal list-inside space-y-2 text-sm'>";
echo "<li>أضف JavaScript الإصلاح السريع فقط</li>";
echo "<li>ارفع الملف للسيرفر</li>";
echo "<li>امسح كاش المتصفح</li>";
echo "<li>اختبر النتيجة</li>";
echo "</ol>";
echo "</div>";

echo "</div>";
echo "</div>";

// Test section
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>🧪 اختبار النتيجة</h2>";
echo "<p class='mb-4'>بعد تطبيق الإصلاح، يجب أن ترى:</p>";

echo "<div class='grid grid-cols-1 md:grid-cols-2 gap-4'>";

// Example correct booth
echo "<div class='border-2 rounded-lg p-4' style='border-color: #2C3E50; background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);'>";
echo "<div class='flex justify-between items-start mb-3'>";
echo "<h3 class='font-bold text-lg' style='color: #2C3E50;'>A001</h3>";
echo "<span class='text-white px-3 py-1 rounded-full text-sm font-semibold' style='background: #27AE60;'>متاح</span>";
echo "</div>";
echo "<p class='mb-3' style='color: #34495E;'>جناح ممتاز في موقع استراتيجي</p>";
echo "<div class='space-y-2 text-sm'>";
echo "<div style='color: #2C3E50;'><span class='font-semibold'>📏 المساحة:</span> 25 م²</div>";
echo "<div style='color: #2C3E50;'><span class='font-semibold'>📍 الموقع:</span> Hall A, Section 1</div>";
echo "<div class='font-bold text-lg' style='color: #E74C3C;'>💰 2,500 KWD</div>";
echo "</div>";
echo "<div class='mt-2 text-center text-green-600 font-bold'>✅ هكذا يجب أن تبدو جميع الأجنحة</div>";
echo "</div>";

echo "</div>";
echo "</div>";

// Final links
echo "<div class='text-center space-x-reverse space-x-4'>";
echo "<a href='/exhibitions-1-simple.php' class='text-white px-6 py-3 rounded-lg hover:opacity-80' style='background: #2C3E50;'>🧪 اختبار الصفحة</a>";
echo "<a href='/check-booth-colors.php' class='bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700'>🔍 فحص الألوان</a>";
echo "<a href='/exhibitions-simple.php' class='bg-gray-600 text-white px-6 py-3 rounded-lg hover:bg-gray-700'>📋 جميع المعارض</a>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>

<?php
// Dynamic Exhibition Details Page
// This file displays any exhibition based on ID parameter

session_start();

// Get language
$currentLang = $_SESSION['language'] ?? $_COOKIE['language'] ?? $_GET['lang'] ?? 'ar';

// Get exhibition ID from URL
$exhibitionId = $_GET['id'] ?? $_GET['exhibition_id'] ?? null;

if (!$exhibitionId) {
    header('Location: /exhibitions-simple.php');
    exit;
}

// Include database configuration
require_once 'config-database.php';

// Database connection
try {
    $pdo = getDatabaseConnection();

    // Fetch exhibition details
    $stmt = $pdo->prepare("SELECT * FROM exhibitions WHERE id = ? AND status = 'published'");
    $stmt->execute([$exhibitionId]);
    $exhibition = $stmt->fetch(PDO::FETCH_OBJ);

    if (!$exhibition) {
        header('Location: /exhibitions-simple.php');
        exit;
    }

    // Fetch available booths
    $stmt = $pdo->prepare("SELECT * FROM booths WHERE exhibition_id = ? AND status = 'available' ORDER BY price ASC");
    $stmt->execute([$exhibitionId]);
    $availableBooths = $stmt->fetchAll(PDO::FETCH_OBJ);

    // Get booth statistics
    $stmt = $pdo->prepare("SELECT
        COUNT(*) as total_booths,
        COUNT(CASE WHEN status = 'available' THEN 1 END) as available_booths,
        COUNT(CASE WHEN status = 'booked' THEN 1 END) as booked_booths,
        MIN(price) as min_price,
        MAX(price) as max_price,
        SUM(area) as total_area
        FROM booths WHERE exhibition_id = ?");
    $stmt->execute([$exhibitionId]);
    $stats = $stmt->fetch(PDO::FETCH_OBJ);

} catch(PDOException $e) {
    echo "خطأ في قاعدة البيانات: " . $e->getMessage();
    exit;
}

// Format dates
$startDate = new DateTime($exhibition->start_date);
$endDate = new DateTime($exhibition->end_date);
?>

<!DOCTYPE html>
<html lang="<?= $currentLang ?>" dir="<?= $currentLang === 'ar' ? 'rtl' : 'ltr' ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($exhibition->title) ?> - Season Expo Kuwait</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">

    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .booth-card {
            transition: all 0.3s ease;
        }
        .booth-card:hover {
            transform: translateY(-5px);
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg fixed w-full top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="/">
                        <img src="/images/logo.png" alt="Season Expo Kuwait" style="height: 40px; width: auto;" class="hover:opacity-80 transition-opacity">
                    </a>
                </div>
                <div class="flex items-center space-x-reverse space-x-4">
                    <a href="/" class="text-gray-600 hover:text-gray-900">الصفحة الرئيسية</a>
                    <a href="/exhibitions-simple.php" class="text-blue-600 font-semibold">المعارض</a>
                    <a href="/dashboard" class="text-gray-600 hover:text-gray-900">حسابي</a>
                    <a href="/signatures" class="text-gray-600 hover:text-gray-900">التوقيعات الرقمية</a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="pt-16">
        <div class="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
            <!-- Exhibition Header -->
            <div class="bg-white rounded-lg shadow-lg overflow-hidden mb-8">
                <?php if ($exhibition->featured_image): ?>
                <div class="h-64 bg-cover bg-center" style="background-image: url('/storage/<?= htmlspecialchars($exhibition->featured_image) ?>');">
                    <div class="h-full bg-black bg-opacity-50 flex items-center justify-center">
                        <div class="text-center text-white">
                            <h1 class="text-4xl font-bold mb-4"><?= htmlspecialchars($exhibition->title) ?></h1>
                            <p class="text-xl"><?= htmlspecialchars($exhibition->short_description ?? $exhibition->description) ?></p>
                        </div>
                    </div>
                </div>
                <?php else: ?>
                <div class="h-64 bg-gradient-to-r from-blue-600 to-purple-600 flex items-center justify-center">
                    <div class="text-center text-white">
                        <h1 class="text-4xl font-bold mb-4"><?= htmlspecialchars($exhibition->title) ?></h1>
                        <p class="text-xl"><?= htmlspecialchars($exhibition->short_description ?? $exhibition->description) ?></p>
                    </div>
                </div>
                <?php endif; ?>

                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                        <div class="text-center">
                            <div class="text-2xl mb-2">📅</div>
                            <h3 class="font-semibold mb-1">تاريخ المعرض</h3>
                            <p class="text-gray-600"><?= $startDate->format('d/m/Y') ?> - <?= $endDate->format('d/m/Y') ?></p>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl mb-2">📍</div>
                            <h3 class="font-semibold mb-1">المكان</h3>
                            <p class="text-gray-600"><?= htmlspecialchars($exhibition->venue_name) ?></p>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl mb-2">🏙️</div>
                            <h3 class="font-semibold mb-1">المدينة</h3>
                            <p class="text-gray-600"><?= htmlspecialchars($exhibition->city) ?></p>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl mb-2">🎯</div>
                            <h3 class="font-semibold mb-1">الحالة</h3>
                            <?php
                            $statusColors = [
                                'published' => 'bg-green-100 text-green-800',
                                'draft' => 'bg-yellow-100 text-yellow-800',
                                'cancelled' => 'bg-red-100 text-red-800',
                                'completed' => 'bg-gray-100 text-gray-800'
                            ];
                            $statusTexts = [
                                'published' => 'متاح',
                                'draft' => 'قيد التحضير',
                                'cancelled' => 'ملغي',
                                'completed' => 'انتهى'
                            ];
                            ?>
                            <span class="inline-block px-3 py-1 rounded-full text-sm font-medium <?= $statusColors[$exhibition->status] ?? 'bg-gray-100 text-gray-800' ?>">
                                <?= $statusTexts[$exhibition->status] ?? $exhibition->status ?>
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Description -->
            <?php if ($exhibition->description): ?>
            <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
                <h2 class="text-2xl font-bold text-gray-900 mb-4">📋 وصف المعرض</h2>
                <div class="text-gray-700 leading-relaxed">
                    <?= nl2br(htmlspecialchars($exhibition->description)) ?>
                </div>
            </div>
            <?php endif; ?>

            <!-- Statistics -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                <div class="bg-white p-6 rounded-lg shadow-lg border-t-4" style="border-top-color: #2C3E50;">
                    <div class="text-3xl font-bold mb-2" style="color: #2C3E50;"><?= $stats->total_booths ?></div>
                    <div class="font-semibold" style="color: #34495E;">إجمالي الأجنحة</div>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-lg border-t-4 border-green-500">
                    <div class="text-3xl font-bold text-green-600 mb-2"><?= $stats->available_booths ?></div>
                    <div class="text-green-700 font-semibold">أجنحة متاحة</div>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-lg border-t-4 border-blue-500">
                    <div class="text-3xl font-bold text-blue-600 mb-2"><?= number_format($stats->min_price ?? 0) ?></div>
                    <div class="text-blue-700 font-semibold">أقل سعر (<?= $exhibition->currency ?>)</div>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-lg border-t-4 border-purple-500">
                    <div class="text-3xl font-bold text-purple-600 mb-2"><?= number_format($stats->total_area ?? 0) ?></div>
                    <div class="text-purple-700 font-semibold">إجمالي المساحة (م²)</div>
                </div>
            </div>

            <!-- Available Booths -->
            <?php if (count($availableBooths) > 0): ?>
            <div id="booking" class="bg-white rounded-lg shadow-lg p-6 mb-8">
                <h2 class="text-2xl font-bold text-gray-900 mb-6">🎯 الأجنحة المتاحة</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <?php foreach ($availableBooths as $booth): ?>
                    <div class="booth-card border-2 rounded-lg p-4 hover:shadow-lg transition-all duration-300"
                         style="border-color: #2C3E50; background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);">
                        <div class="flex justify-between items-start mb-3">
                            <h3 class="font-bold text-lg" style="color: #2C3E50;"><?= htmlspecialchars($booth->name ?? $booth->booth_number) ?></h3>
                            <span class="px-2 py-1 text-xs rounded-full text-white"
                                  style="background: #27AE60;">متاح</span>
                        </div>
                        <?php if ($booth->description): ?>
                        <p class="mb-3" style="color: #34495E;"><?= htmlspecialchars($booth->description) ?></p>
                        <?php endif; ?>
                        <div class="space-y-2 text-sm">
                            <?php if ($booth->area): ?>
                            <div style="color: #2C3E50;"><span class="font-semibold">📏 المساحة:</span> <?= $booth->area ?> م²</div>
                            <?php endif; ?>
                            <?php if ($booth->location): ?>
                            <div style="color: #2C3E50;"><span class="font-semibold">📍 الموقع:</span> <?= htmlspecialchars($booth->location) ?></div>
                            <?php endif; ?>
                            <div class="price font-bold text-lg" style="color: #E74C3C; font-weight: bold;">💰 <?= number_format($booth->price) ?> <?= $exhibition->currency ?></div>
                        </div>
                        <a href="/booking-alternative.php?booth_id=<?= $booth->id ?>&exhibition_id=<?= $exhibition->id ?>"
                           class="block w-full mt-4 text-white py-2 px-4 rounded transition-colors text-center font-semibold"
                           style="background: #2C3E50; color: white;"
                           onmouseover="this.style.background='#1A252F'"
                           onmouseout="this.style.background='#2C3E50'">
                            احجز الآن (مع الإقرار والتعهد)
                        </a>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
            <?php else: ?>
            <div class="bg-white rounded-lg shadow-lg p-8 text-center">
                <div class="text-6xl mb-4">🏢</div>
                <h3 class="text-xl font-bold text-gray-900 mb-2">لا توجد أجنحة متاحة حالياً</h3>
                <p class="text-gray-600">جميع الأجنحة محجوزة أو سيتم الإعلان عن أجنحة جديدة قريباً</p>
            </div>
            <?php endif; ?>

            <!-- Navigation -->
            <div class="text-center space-x-reverse space-x-4">
                <a href="/exhibitions-simple.php" class="bg-gray-600 text-white px-6 py-3 rounded-lg hover:bg-gray-700">
                    ← العودة للمعارض
                </a>
                <a href="/" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700">
                    🏠 الصفحة الرئيسية
                </a>
            </div>
        </div>
    </div>
</body>
</html>

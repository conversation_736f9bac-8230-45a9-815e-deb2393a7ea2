<?php
// Emergency backup index.php
echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='utf-8'>";
echo "<title>Season Expo - Emergency Mode</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }";
echo ".container { max-width: 800px; margin: 0 auto; background: white; padding: 40px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }";
echo ".error { background: #fee; border: 1px solid #fcc; padding: 20px; border-radius: 5px; margin: 20px 0; }";
echo ".success { background: #efe; border: 1px solid #cfc; padding: 20px; border-radius: 5px; margin: 20px 0; }";
echo ".btn { background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px 5px; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<h1>Season Expo - وضع الطوارئ</h1>";

echo "<div class='error'>";
echo "<h3>⚠️ تطبيق Laravel معطل مؤقتاً</h3>";
echo "<p>يبدو أن هناك مشكلة في تطبيق Laravel. يرجى المحاولة لاحقاً.</p>";
echo "</div>";

// Check what's missing
echo "<h3>🔍 فحص النظام:</h3>";
echo "<ul>";

if (file_exists('vendor/autoload.php')) {
    echo "<li style='color: green;'>✅ ملفات Vendor موجودة</li>";
} else {
    echo "<li style='color: red;'>❌ ملفات Vendor مفقودة</li>";
}

if (file_exists('.env')) {
    echo "<li style='color: green;'>✅ ملف .env موجود</li>";
} else {
    echo "<li style='color: red;'>❌ ملف .env مفقود</li>";
}

if (file_exists('app/Http/Kernel.php')) {
    echo "<li style='color: green;'>✅ ملفات Laravel موجودة</li>";
} else {
    echo "<li style='color: red;'>❌ ملفات Laravel مفقودة</li>";
}

if (is_writable('storage')) {
    echo "<li style='color: green;'>✅ مجلد Storage قابل للكتابة</li>";
} else {
    echo "<li style='color: red;'>❌ مجلد Storage غير قابل للكتابة</li>";
}

echo "</ul>";

echo "<h3>📞 خطوات الإصلاح:</h3>";
echo "<ol>";
echo "<li>تحقق من رفع جميع ملفات Laravel</li>";
echo "<li>تأكد من وجود مجلد vendor/</li>";
echo "<li>تحقق من ملف .env</li>";
echo "<li>تحقق من صلاحيات الملفات</li>";
echo "<li>راجع سجلات الأخطاء في لوحة التحكم</li>";
echo "</ol>";

echo "<div style='margin-top: 30px;'>";
echo "<a href='test.php' class='btn'>🧪 اختبار PHP</a>";
echo "<a href='/' class='btn'>🔄 إعادة المحاولة</a>";
echo "</div>";

echo "<div style='margin-top: 30px; padding: 20px; background: #f9f9f9; border-radius: 5px;'>";
echo "<small>";
echo "<strong>معلومات تقنية:</strong><br>";
echo "PHP Version: " . phpversion() . "<br>";
echo "Server: " . $_SERVER['SERVER_SOFTWARE'] . "<br>";
echo "Document Root: " . $_SERVER['DOCUMENT_ROOT'] . "<br>";
echo "Current Time: " . date('Y-m-d H:i:s') . "<br>";
echo "</small>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>

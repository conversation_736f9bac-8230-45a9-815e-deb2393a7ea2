<?php
// English Language Handler for Hostinger Server
// This file handles switching to English language

session_start();

// Set language to English
$_SESSION['language'] = 'en';
$_SESSION['locale'] = 'en';

// Set cookie for persistence (30 days)
setcookie('language', 'en', time() + (30 * 24 * 60 * 60), '/');
setcookie('locale', 'en', time() + (30 * 24 * 60 * 60), '/');

// Get referrer URL to redirect back
$referrer = $_SERVER['HTTP_REFERER'] ?? '/';

// Clean referrer URL from language parameters
$referrer = preg_replace('/[?&]lang=[^&]*/', '', $referrer);
$referrer = preg_replace('/[?&]locale=[^&]*/', '', $referrer);

// Add language parameter
$separator = strpos($referrer, '?') !== false ? '&' : '?';
$redirectUrl = $referrer . $separator . 'lang=en';

// If no referrer or referrer is this file, go to homepage
if (empty($referrer) || strpos($referrer, '/lang/') !== false) {
    $redirectUrl = '/homepage-fixed.php?lang=en';
}

// Redirect back with language parameter
header('Location: ' . $redirectUrl);
exit;
?>

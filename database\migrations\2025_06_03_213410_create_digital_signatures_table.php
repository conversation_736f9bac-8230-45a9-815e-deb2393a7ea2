<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('digital_signatures', function (Blueprint $table) {
            $table->id();
            $table->string('document_type'); // contract, agreement, booking, etc.
            $table->string('document_id'); // Reference to the document being signed
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('signer_name');
            $table->string('signer_email');
            $table->string('signer_ip');
            $table->text('signature_data'); // Base64 encoded signature image
            $table->string('signature_hash'); // SHA256 hash for verification
            $table->json('metadata')->nullable(); // Additional data (position, timestamp, etc.)
            $table->timestamp('signed_at');
            $table->boolean('is_verified')->default(false);
            $table->timestamp('verified_at')->nullable();
            $table->string('verification_token')->unique();
            $table->string('certificate_path')->nullable(); // Path to generated certificate
            $table->timestamps();

            $table->index(['document_type', 'document_id']);
            $table->index(['user_id', 'signed_at']);
            $table->index('verification_token');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('digital_signatures');
    }
};

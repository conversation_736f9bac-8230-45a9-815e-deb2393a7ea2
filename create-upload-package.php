<?php
// <PERSON>ript to create upload package for Hostinger

echo "<h1>إنشاء حزمة الرفع لـ Hostinger</h1>";

$filesToCopy = [
    'resources/views/auth/register-simple.blade.php' => 'upload-package/resources/views/auth/register-simple.blade.php',
    'resources/views/auth/forgot-password.blade.php' => 'upload-package/resources/views/auth/forgot-password.blade.php',
    'routes/web.php' => 'upload-package/routes/web.php',
    'test-forgot-password.php' => 'upload-package/test-forgot-password.php',
    'test-login-page.php' => 'upload-package/test-login-page.php',
    'test-email-functionality.php' => 'upload-package/test-email-functionality.php',
];

// Create directories
$dirs = [
    'upload-package',
    'upload-package/resources',
    'upload-package/resources/views',
    'upload-package/resources/views/auth',
    'upload-package/routes'
];

foreach ($dirs as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
        echo "<p>✅ تم إنشاء مجلد: $dir</p>";
    }
}

// Copy files
foreach ($filesToCopy as $source => $destination) {
    if (file_exists($source)) {
        copy($source, $destination);
        echo "<p>✅ تم نسخ: $source → $destination</p>";
    } else {
        echo "<p>❌ الملف غير موجود: $source</p>";
    }
}

// Create instructions file
$instructions = '# تعليمات الرفع إلى Hostinger

## الملفات في هذه الحزمة:

### 1. ملفات العرض (Views):
- `resources/views/auth/register-simple.blade.php` - صفحة التسجيل مع رابط نسيت كلمة المرور
- `resources/views/auth/forgot-password.blade.php` - صفحة استعادة كلمة المرور

### 2. ملف الروتات:
- `routes/web.php` - يحتوي على روتات نسيت كلمة المرور وصفحة تسجيل الدخول المحدثة

### 3. ملفات الاختبار:
- `test-forgot-password.php` - اختبار وظيفة نسيت كلمة المرور
- `test-login-page.php` - اختبار صفحة تسجيل الدخول
- `test-email-functionality.php` - اختبار وظيفة البريد الإلكتروني

## خطوات الرفع:

1. **ادخل إلى File Manager في Hostinger**
2. **انتقل إلى مجلد موقعك** (عادة public_html أو اسم النطاق)
3. **ارفع الملفات في مواقعها الصحيحة:**
   - ارفع ملفات `resources/views/auth/` إلى `resources/views/auth/`
   - ارفع `routes/web.php` إلى `routes/`
   - ارفع ملفات الاختبار إلى الجذر الرئيسي

4. **بعد الرفع:**
   - اذهب إلى `yourdomain.com/clear-all-caches.php`
   - اختبر الوظيفة في `yourdomain.com/login-simple`

## إعداد البريد الإلكتروني:

أضف هذه الإعدادات إلى ملف `.env`:

```
MAIL_MAILER=smtp
MAIL_HOST=smtp.hostinger.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-email-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="Season Expo"
```

## التحقق من النجاح:

- ✅ رابط "نسيت كلمة المرور" يظهر في صفحة تسجيل الدخول
- ✅ صفحة استعادة كلمة المرور تعمل
- ✅ إرسال البريد الإلكتروني يعمل
';

file_put_contents('upload-package/README.md', $instructions);
echo "<p>✅ تم إنشاء ملف التعليمات: upload-package/README.md</p>";

echo "<h2>✅ تم إنشاء حزمة الرفع بنجاح!</h2>";
echo "<p>يمكنك الآن:</p>";
echo "<ul>";
echo "<li>ضغط مجلد <code>upload-package</code> إلى ملف ZIP</li>";
echo "<li>رفع الملفات واحداً تلو الآخر إلى Hostinger</li>";
echo "<li>اتباع التعليمات في ملف README.md</li>";
echo "</ul>";

echo "<h2>الملفات الجاهزة للرفع:</h2>";
echo "<ul>";
foreach ($filesToCopy as $source => $destination) {
    if (file_exists($destination)) {
        $size = filesize($destination);
        echo "<li><strong>$destination</strong> ($size bytes)</li>";
    }
}
echo "</ul>";

?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h1, h2 {
    color: #333;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
}

code {
    background-color: #f8f9fa;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
}

ul {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    margin: 10px 0;
}
</style>

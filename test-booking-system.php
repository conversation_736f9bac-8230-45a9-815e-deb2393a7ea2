<?php
// Test Booking System
// This file tests the booking functionality

session_start();

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='utf-8'>";
echo "<title>اختبار نظام الحجز - Season Expo Kuwait</title>";
echo "<script src='https://cdn.tailwindcss.com'></script>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }";
echo "</style>";
echo "</head>";
echo "<body class='bg-gray-50 p-8'>";

echo "<div class='max-w-4xl mx-auto'>";
echo "<h1 class='text-3xl font-bold mb-8' style='color: #2C3E50;'>🎯 اختبار نظام الحجز</h1>";

// Login status check
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>👤 حالة تسجيل الدخول</h2>";

$isLoggedIn = isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);

if ($isLoggedIn) {
    echo "<div class='bg-green-50 border border-green-200 rounded-lg p-4'>";
    echo "<h3 class='font-semibold text-green-700 mb-2'>✅ مسجل الدخول</h3>";
    echo "<p class='text-green-600'>معرف المستخدم: " . $_SESSION['user_id'] . "</p>";
    echo "<div class='mt-3'>";
    echo "<a href='/logout.php' class='text-red-600 hover:underline text-sm'>تسجيل الخروج</a>";
    echo "</div>";
    echo "</div>";
} else {
    echo "<div class='bg-yellow-50 border border-yellow-200 rounded-lg p-4'>";
    echo "<h3 class='font-semibold text-yellow-700 mb-2'>⚠️ غير مسجل الدخول</h3>";
    echo "<p class='text-yellow-600'>يجب تسجيل الدخول أولاً لإتمام عملية الحجز</p>";
    echo "<div class='mt-3 space-x-reverse space-x-2'>";
    echo "<a href='/login-simple.php' class='bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 text-sm'>تسجيل الدخول</a>";
    echo "<button onclick='simulateLogin()' class='bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 text-sm'>محاكاة تسجيل الدخول</button>";
    echo "</div>";
    echo "</div>";
}

echo "</div>";

// Test booking links
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>🔗 اختبار روابط الحجز</h2>";

// Include database configuration
require_once 'config-database.php';

try {
    $pdo = getDatabaseConnection();
    
    // Get some sample booths
    $stmt = $pdo->query("
        SELECT 
            b.id as booth_id,
            b.name as booth_name,
            b.price,
            b.area,
            b.status,
            e.id as exhibition_id,
            e.title as exhibition_title
        FROM booths b
        JOIN exhibitions e ON b.exhibition_id = e.id
        WHERE b.status = 'available' AND e.status = 'published'
        ORDER BY e.start_date, b.price
        LIMIT 5
    ");
    
    $booths = $stmt->fetchAll(PDO::FETCH_OBJ);
    
    if (count($booths) > 0) {
        echo "<div class='space-y-4'>";
        
        foreach ($booths as $booth) {
            echo "<div class='border border-gray-200 rounded-lg p-4'>";
            echo "<div class='flex items-center justify-between'>";
            echo "<div>";
            echo "<h3 class='font-semibold'>{$booth->booth_name}</h3>";
            echo "<p class='text-sm text-gray-600'>{$booth->exhibition_title}</p>";
            echo "<p class='text-sm text-blue-600'>{$booth->area} م² - " . number_format($booth->price) . " KWD</p>";
            echo "</div>";
            echo "<div class='space-x-reverse space-x-2'>";
            
            // Test different booking URLs
            echo "<a href='/booking-simple.php?booth_id={$booth->booth_id}&exhibition_id={$booth->exhibition_id}' target='_blank' class='inline-block px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 text-sm'>🎯 حجز مباشر</a>";
            echo "<a href='/booking/{$booth->booth_id}/{$booth->exhibition_id}' target='_blank' class='inline-block px-4 py-2 border border-green-600 text-green-600 rounded hover:bg-green-50 text-sm'>🔗 رابط نظيف</a>";
            
            echo "</div>";
            echo "</div>";
            echo "</div>";
        }
        
        echo "</div>";
    } else {
        echo "<div class='text-center text-gray-600'>لا توجد أجنحة متاحة للاختبار</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='bg-red-50 border border-red-200 rounded-lg p-4'>";
    echo "<h3 class='font-semibold text-red-700'>خطأ في قاعدة البيانات:</h3>";
    echo "<p class='text-red-600'>" . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "</div>";

// Test login redirect
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>🔄 اختبار إعادة التوجيه</h2>";

echo "<div class='space-y-4'>";

echo "<div class='bg-blue-50 border border-blue-200 rounded-lg p-4'>";
echo "<h3 class='font-semibold text-blue-700 mb-2'>📋 كيف يعمل النظام:</h3>";
echo "<ol class='text-sm space-y-1 list-decimal list-inside ml-4'>";
echo "<li>المستخدم يضغط على \"احجز الآن\"</li>";
echo "<li>إذا لم يكن مسجل الدخول، يتم توجيهه لصفحة تسجيل الدخول</li>";
echo "<li>بعد تسجيل الدخول، يعود تلقائياً لصفحة الحجز</li>";
echo "<li>يملأ النموذج ويوافق على الشروط</li>";
echo "<li>يتم تأكيد الحجز وتحديث حالة الجناح</li>";
echo "</ol>";
echo "</div>";

echo "<div class='bg-green-50 border border-green-200 rounded-lg p-4'>";
echo "<h3 class='font-semibold text-green-700 mb-2'>🧪 اختبار الروابط:</h3>";
echo "<div class='space-y-2'>";

// Test URLs
$testUrls = [
    '/booking-simple.php?booth_id=23&exhibition_id=1' => 'رابط الحجز المباشر',
    '/booking/23/1' => 'رابط الحجز النظيف',
    '/login-simple.php' => 'صفحة تسجيل الدخول',
    '/login-simple' => 'تسجيل الدخول (بدون .php)'
];

foreach ($testUrls as $url => $description) {
    echo "<a href='{$url}' target='_blank' class='block w-full text-center px-3 py-2 border border-gray-300 rounded hover:bg-gray-50 text-sm'>";
    echo "🔗 {$description}";
    echo "<div class='text-xs text-gray-500'>{$url}</div>";
    echo "</a>";
}

echo "</div>";
echo "</div>";

echo "</div>";
echo "</div>";

// File status
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>📁 حالة ملفات الحجز</h2>";

$files = [
    'booking-simple.php' => 'صفحة الحجز المبسطة (جديد)',
    'booking-with-signature.php' => 'صفحة الحجز مع التوقيع (Laravel)',
    'login-simple.php' => 'صفحة تسجيل الدخول',
    '.htaccess' => 'ملف إعادة التوجيه (محدث)'
];

echo "<div class='space-y-3'>";

foreach ($files as $file => $description) {
    $fullPath = __DIR__ . '/' . $file;
    echo "<div class='flex items-center justify-between p-3 border border-gray-200 rounded-lg'>";
    echo "<div>";
    echo "<h3 class='font-semibold text-sm'>{$description}</h3>";
    echo "<p class='text-xs text-gray-600'>/{$file}</p>";
    echo "</div>";
    echo "<div>";
    
    if (file_exists($fullPath)) {
        $size = round(filesize($fullPath)/1024, 2);
        echo "<div class='text-green-600 text-sm'>✅ موجود ({$size} KB)</div>";
    } else {
        echo "<div class='text-red-600 text-sm'>❌ مفقود</div>";
    }
    
    echo "</div>";
    echo "</div>";
}

echo "</div>";
echo "</div>";

// Instructions
echo "<div class='bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>📋 تعليمات الاختبار</h2>";

echo "<div class='space-y-4'>";

echo "<div class='bg-white p-4 rounded-lg border'>";
echo "<h3 class='font-semibold mb-2' style='color: #2C3E50;'>1. اختبار بدون تسجيل دخول:</h3>";
echo "<ul class='text-sm space-y-1 list-disc list-inside ml-4'>";
echo "<li>اضغط على أي رابط حجز أعلاه</li>";
echo "<li>يجب أن يوجهك لصفحة تسجيل الدخول</li>";
echo "<li>الرابط الأصلي محفوظ في معامل redirect</li>";
echo "</ul>";
echo "</div>";

echo "<div class='bg-white p-4 rounded-lg border'>";
echo "<h3 class='font-semibold mb-2' style='color: #2C3E50;'>2. اختبار مع تسجيل الدخول:</h3>";
echo "<ul class='text-sm space-y-1 list-disc list-inside ml-4'>";
echo "<li>اضغط على \"محاكاة تسجيل الدخول\" أعلاه</li>";
echo "<li>ثم اضغط على أي رابط حجز</li>";
echo "<li>يجب أن يفتح صفحة الحجز مباشرة</li>";
echo "</ul>";
echo "</div>";

echo "</div>";
echo "</div>";

// Quick actions
echo "<div class='text-center space-x-reverse space-x-4'>";
echo "<a href='/exhibition-details.php?id=1' class='text-white px-6 py-3 rounded-lg hover:opacity-80' style='background: #2C3E50;'>🏢 معرض تجريبي</a>";
echo "<a href='/login-simple.php' class='bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700'>🔐 تسجيل الدخول</a>";
echo "<a href='/homepage-fixed.php?lang=ar' class='bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700'>🏠 الصفحة الرئيسية</a>";
echo "</div>";

echo "</div>";

// JavaScript for simulation
echo "<script>";
echo "function simulateLogin() {";
echo "  fetch('/simulate-login.php', { method: 'POST' })";
echo "    .then(() => {";
echo "      alert('تم محاكاة تسجيل الدخول بنجاح!');";
echo "      location.reload();";
echo "    })";
echo "    .catch(() => {";
echo "      alert('فشل في محاكاة تسجيل الدخول');";
echo "    });";
echo "}";
echo "</script>";

echo "</body>";
echo "</html>";
?>

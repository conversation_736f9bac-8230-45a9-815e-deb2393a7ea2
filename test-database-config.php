<?php
// Test Database Configuration
// This file helps you configure and test database connection

session_start();

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='utf-8'>";
echo "<title>إعداد واختبار قاعدة البيانات - Season Expo Kuwait</title>";
echo "<script src='https://cdn.tailwindcss.com'></script>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }";
echo "</style>";
echo "</head>";
echo "<body class='bg-gray-50 p-8'>";

echo "<div class='max-w-4xl mx-auto'>";
echo "<h1 class='text-3xl font-bold mb-8' style='color: #2C3E50;'>🔧 إعداد واختبار قاعدة البيانات</h1>";

// Current configuration display
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>⚙️ الإعدادات الحالية</h2>";

// Include config file
if (file_exists('config-database.php')) {
    require_once 'config-database.php';
    
    echo "<div class='bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4'>";
    echo "<h3 class='font-semibold text-blue-700 mb-2'>📋 إعدادات قاعدة البيانات:</h3>";
    echo "<div class='space-y-2 text-sm'>";
    echo "<div><strong>Host:</strong> <code class='bg-gray-100 px-2 py-1 rounded'>" . ($DB_CONFIG['host'] ?? 'غير محدد') . "</code></div>";
    echo "<div><strong>Database:</strong> <code class='bg-gray-100 px-2 py-1 rounded'>" . ($DB_CONFIG['database'] ?? 'غير محدد') . "</code></div>";
    echo "<div><strong>Username:</strong> <code class='bg-gray-100 px-2 py-1 rounded'>" . ($DB_CONFIG['username'] ?? 'غير محدد') . "</code></div>";
    echo "<div><strong>Password:</strong> <code class='bg-gray-100 px-2 py-1 rounded'>" . (isset($DB_CONFIG['password']) && !empty($DB_CONFIG['password']) ? '***محدد***' : 'غير محدد') . "</code></div>";
    echo "<div><strong>Port:</strong> <code class='bg-gray-100 px-2 py-1 rounded'>" . ($DB_CONFIG['port'] ?? 'غير محدد') . "</code></div>";
    echo "</div>";
    echo "</div>";
    
} else {
    echo "<div class='bg-red-50 border border-red-200 rounded-lg p-4'>";
    echo "<h3 class='font-semibold text-red-700 mb-2'>❌ ملف الإعدادات غير موجود</h3>";
    echo "<p class='text-red-600'>يرجى رفع ملف config-database.php</p>";
    echo "</div>";
}

echo "</div>";

// Test connection
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>🧪 اختبار الاتصال</h2>";

if (function_exists('testDatabaseConnection')) {
    $testResult = testDatabaseConnection();
    
    if ($testResult['success']) {
        echo "<div class='bg-green-50 border border-green-200 rounded-lg p-4 mb-4'>";
        echo "<h3 class='font-semibold text-green-700 mb-2'>✅ نجح الاتصال!</h3>";
        echo "<p class='text-green-600'>" . $testResult['message'] . "</p>";
        echo "</div>";
        
        // Get database info
        $dbInfo = getDatabaseInfo();
        
        if (!isset($dbInfo['error'])) {
            echo "<div class='bg-blue-50 border border-blue-200 rounded-lg p-4'>";
            echo "<h3 class='font-semibold text-blue-700 mb-2'>📊 معلومات قاعدة البيانات:</h3>";
            echo "<div class='space-y-2 text-sm'>";
            echo "<div><strong>اسم قاعدة البيانات:</strong> " . ($dbInfo['database'] ?? 'غير محدد') . "</div>";
            echo "<div><strong>إصدار MySQL:</strong> " . ($dbInfo['version'] ?? 'غير محدد') . "</div>";
            
            if (isset($dbInfo['exhibitions_table_exists'])) {
                if ($dbInfo['exhibitions_table_exists']) {
                    echo "<div><strong>جدول المعارض:</strong> <span class='text-green-600'>✅ موجود</span></div>";
                    echo "<div><strong>عدد المعارض:</strong> " . ($dbInfo['exhibitions_count'] ?? 0) . "</div>";
                    echo "<div><strong>المعارض المنشورة:</strong> " . ($dbInfo['published_exhibitions_count'] ?? 0) . "</div>";
                } else {
                    echo "<div><strong>جدول المعارض:</strong> <span class='text-red-600'>❌ غير موجود</span></div>";
                }
            }
            
            if (isset($dbInfo['booths_table_exists'])) {
                if ($dbInfo['booths_table_exists']) {
                    echo "<div><strong>جدول الأجنحة:</strong> <span class='text-green-600'>✅ موجود</span></div>";
                    echo "<div><strong>عدد الأجنحة:</strong> " . ($dbInfo['booths_count'] ?? 0) . "</div>";
                } else {
                    echo "<div><strong>جدول الأجنحة:</strong> <span class='text-red-600'>❌ غير موجود</span></div>";
                }
            }
            
            echo "</div>";
            echo "</div>";
        }
        
    } else {
        echo "<div class='bg-red-50 border border-red-200 rounded-lg p-4'>";
        echo "<h3 class='font-semibold text-red-700 mb-2'>❌ فشل الاتصال</h3>";
        echo "<p class='text-red-600'>" . $testResult['message'] . "</p>";
        echo "</div>";
    }
} else {
    echo "<div class='bg-yellow-50 border border-yellow-200 rounded-lg p-4'>";
    echo "<h3 class='font-semibold text-yellow-700 mb-2'>⚠️ لا يمكن اختبار الاتصال</h3>";
    echo "<p class='text-yellow-600'>ملف الإعدادات غير محمل بشكل صحيح</p>";
    echo "</div>";
}

echo "</div>";

// Hostinger specific instructions
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>🏢 إعدادات Hostinger</h2>";

echo "<div class='space-y-4'>";

echo "<div class='bg-blue-50 border border-blue-200 rounded-lg p-4'>";
echo "<h3 class='font-semibold text-blue-700 mb-2'>📋 كيفية الحصول على بيانات قاعدة البيانات:</h3>";
echo "<ol class='text-sm space-y-1 list-decimal list-inside ml-4'>";
echo "<li>ادخل إلى لوحة تحكم Hostinger (hPanel)</li>";
echo "<li>اذهب إلى قسم \"Databases\" أو \"قواعد البيانات\"</li>";
echo "<li>ابحث عن قاعدة البيانات الخاصة بك</li>";
echo "<li>انسخ المعلومات التالية:</li>";
echo "<ul class='list-disc list-inside ml-6 mt-2'>";
echo "<li><strong>Database Name:</strong> عادة يبدأ بـ u404269408_</li>";
echo "<li><strong>Username:</strong> عادة يبدأ بـ u404269408_</li>";
echo "<li><strong>Password:</strong> كلمة المرور التي حددتها</li>";
echo "<li><strong>Host:</strong> عادة localhost</li>";
echo "</ul>";
echo "</ol>";
echo "</div>";

echo "<div class='bg-green-50 border border-green-200 rounded-lg p-4'>";
echo "<h3 class='font-semibold text-green-700 mb-2'>✏️ تحديث الإعدادات:</h3>";
echo "<ol class='text-sm space-y-1 list-decimal list-inside ml-4'>";
echo "<li>افتح ملف <code>config-database.php</code></li>";
echo "<li>ابحث عن المتغير <code>\$DB_CONFIG</code></li>";
echo "<li>حدث القيم التالية:</li>";
echo "<ul class='list-disc list-inside ml-6 mt-2'>";
echo "<li><code>'database' => 'u404269408_your_db_name'</code></li>";
echo "<li><code>'username' => 'u404269408_your_username'</code></li>";
echo "<li><code>'password' => 'your_password'</code></li>";
echo "</ul>";
echo "<li>احفظ الملف وارفعه للخادم</li>";
echo "<li>اختبر الاتصال مرة أخرى</li>";
echo "</ol>";
echo "</div>";

echo "</div>";
echo "</div>";

// Common database names for this user
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>💡 أسماء محتملة لقاعدة البيانات</h2>";

echo "<div class='bg-yellow-50 border border-yellow-200 rounded-lg p-4'>";
echo "<h3 class='font-semibold text-yellow-700 mb-2'>🔍 جرب هذه الأسماء:</h3>";
echo "<div class='space-y-2 text-sm'>";

$possibleNames = [
    'u404269408_season_expo',
    'u404269408_seasonexpo',
    'u404269408_expo',
    'u404269408_season',
    'u404269408_main',
    'u404269408_db',
    'season_expo_2',
    'seasonexpo'
];

foreach ($possibleNames as $name) {
    echo "<div class='flex items-center justify-between p-2 border border-gray-200 rounded'>";
    echo "<code class='bg-gray-100 px-2 py-1 rounded'>{$name}</code>";
    echo "<span class='text-xs text-gray-500'>جرب هذا الاسم</span>";
    echo "</div>";
}

echo "</div>";
echo "</div>";

echo "</div>";

// File status
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>📁 حالة الملفات</h2>";

$files = [
    'config-database.php' => 'ملف إعدادات قاعدة البيانات',
    'exhibition-details.php' => 'صفحة المعارض الديناميكية',
    'test-dynamic-exhibitions.php' => 'اختبار النظام الديناميكي'
];

echo "<div class='space-y-3'>";

foreach ($files as $file => $description) {
    $fullPath = __DIR__ . '/' . $file;
    echo "<div class='flex items-center justify-between p-3 border border-gray-200 rounded-lg'>";
    echo "<div>";
    echo "<h3 class='font-semibold'>{$description}</h3>";
    echo "<p class='text-sm text-gray-600'>/{$file}</p>";
    echo "</div>";
    echo "<div>";
    
    if (file_exists($fullPath)) {
        $size = round(filesize($fullPath)/1024, 2);
        echo "<div class='text-green-600 text-sm'>✅ موجود ({$size} KB)</div>";
    } else {
        echo "<div class='text-red-600 text-sm'>❌ مفقود</div>";
    }
    
    echo "</div>";
    echo "</div>";
}

echo "</div>";
echo "</div>";

// Quick actions
echo "<div class='text-center space-x-reverse space-x-4'>";
echo "<a href='/config-database.php' class='text-white px-6 py-3 rounded-lg hover:opacity-80' style='background: #2C3E50;'>📝 عرض ملف الإعدادات</a>";
echo "<a href='/test-dynamic-exhibitions.php' class='bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700'>🧪 اختبار النظام</a>";
echo "<a href='/homepage-fixed.php?lang=ar' class='bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700'>🏠 الصفحة الرئيسية</a>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>

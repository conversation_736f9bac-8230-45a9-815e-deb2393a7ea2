<?php
require '../vendor/autoload.php';

$app = require_once '../bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);

echo "<h1>Clear Routes and Reload</h1>";

try {
    // Clear all caches
    $kernel->call('route:clear');
    echo "✅ Route cache cleared<br>";
    
    $kernel->call('config:clear');
    echo "✅ Config cache cleared<br>";
    
    $kernel->call('cache:clear');
    echo "✅ Application cache cleared<br>";
    
    $kernel->call('view:clear');
    echo "✅ View cache cleared<br>";
    
    // Try to list routes after clearing
    echo "<br><h2>Testing Routes After Clear:</h2>";
    
    $routes = \Illuminate\Support\Facades\Route::getRoutes();
    echo "Total routes: " . count($routes) . "<br>";
    
    // Look for home route
    foreach ($routes as $route) {
        if ($route->uri() === '/') {
            echo "✅ Home route found: " . $route->getActionName() . "<br>";
            break;
        }
    }
    
    echo "<br><strong><a href='/' style='font-size: 18px; color: blue;'>🚀 TEST APPLICATION NOW</a></strong><br>";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
}
?>

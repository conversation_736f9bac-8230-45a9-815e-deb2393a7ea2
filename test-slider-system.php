<?php
// Test the complete slider management system

echo "<h1>🧪 اختبار نظام إدارة السلايد الكامل</h1>";

// Test database connection and table
echo "<h2>1. اختبار قاعدة البيانات:</h2>";
try {
    $pdo = new PDO('sqlite:' . __DIR__ . '/database/database.sqlite');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Check if table exists
    $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='slider_images'");
    $table = $stmt->fetch();
    
    if ($table) {
        echo "<p style='color: green;'>✅ جدول slider_images موجود</p>";
        
        // Check table structure
        $stmt = $pdo->query("PRAGMA table_info(slider_images)");
        $columns = $stmt->fetchAll();
        
        echo "<details><summary>هيكل الجدول (" . count($columns) . " عمود)</summary>";
        echo "<ul>";
        foreach ($columns as $column) {
            echo "<li><strong>{$column['name']}</strong> - {$column['type']}</li>";
        }
        echo "</ul></details>";
        
        // Check current data
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM slider_images");
        $count = $stmt->fetch()['count'];
        echo "<p>📊 عدد السلايدات الحالية: <strong>$count</strong></p>";
        
        if ($count > 0) {
            $stmt = $pdo->query("SELECT * FROM slider_images ORDER BY sort_order ASC");
            $slides = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<details><summary>السلايدات الموجودة</summary>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
            echo "<tr style='background: #f0f0f0;'><th>ID</th><th>العنوان</th><th>الترتيب</th><th>نشط</th><th>الصورة</th></tr>";
            foreach ($slides as $slide) {
                $activeStatus = $slide['is_active'] ? '✅' : '❌';
                echo "<tr>";
                echo "<td>{$slide['id']}</td>";
                echo "<td>" . htmlspecialchars($slide['title']) . "</td>";
                echo "<td>{$slide['sort_order']}</td>";
                echo "<td>$activeStatus</td>";
                echo "<td><a href='{$slide['image_url']}' target='_blank'>عرض</a></td>";
                echo "</tr>";
            }
            echo "</table></details>";
        }
        
    } else {
        echo "<p style='color: red;'>❌ جدول slider_images غير موجود</p>";
        echo "<p><a href='/create-slider-table.php'>إنشاء الجدول</a></p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
}

// Test admin pages
echo "<h2>2. اختبار صفحات الإدارة:</h2>";

$adminPages = [
    '/admin/slider' => 'صفحة إدارة السلايد (Laravel Route)',
    '/admin-slider-management.php' => 'صفحة إدارة السلايد (PHP مباشر)',
    '/admin/exhibitions-management' => 'صفحة إدارة المعارض',
    '/dashboard' => 'لوحة التحكم'
];

foreach ($adminPages as $url => $description) {
    echo "<p>🔗 <a href='$url' target='_blank'>$description</a></p>";
}

// Test main page
echo "<h2>3. اختبار الصفحة الرئيسية:</h2>";
echo "<p>🏠 <a href='/' target='_blank'>الصفحة الرئيسية (يجب أن تظهر السلايدات من قاعدة البيانات)</a></p>";

// Test slider functionality
echo "<h2>4. اختبار وظائف السلايد:</h2>";

echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>الميزات المتاحة:</h3>";
echo "<ul>";
echo "<li>✅ قراءة السلايدات من قاعدة البيانات</li>";
echo "<li>✅ عرض السلايدات النشطة فقط</li>";
echo "<li>✅ ترتيب السلايدات حسب sort_order</li>";
echo "<li>✅ صور احتياطية مع ألوان خلفية</li>";
echo "<li>✅ أزرار ديناميكية من قاعدة البيانات</li>";
echo "<li>✅ واجهة إدارة كاملة للأدمن</li>";
echo "</ul>";
echo "</div>";

echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>وظائف الإدارة:</h3>";
echo "<ul>";
echo "<li>➕ إضافة سلايد جديد</li>";
echo "<li>✏️ تعديل السلايدات الموجودة</li>";
echo "<li>🗑️ حذف السلايدات</li>";
echo "<li>👁️ إظهار/إخفاء السلايدات</li>";
echo "<li>🔄 تغيير ترتيب العرض</li>";
echo "<li>🎨 تخصيص الألوان والصور</li>";
echo "<li>🔗 إضافة أزرار مخصصة</li>";
echo "</ul>";
echo "</div>";

// Test instructions
echo "<h2>5. تعليمات الاختبار:</h2>";
echo "<ol>";
echo "<li><strong>اختبار العرض:</strong> اذهب إلى الصفحة الرئيسية وتأكد من ظهور السلايدات</li>";
echo "<li><strong>اختبار الإدارة:</strong> اذهب إلى صفحة إدارة السلايد وجرب إضافة سلايد جديد</li>";
echo "<li><strong>اختبار التعديل:</strong> جرب تعديل سلايد موجود وغير العنوان أو الصورة</li>";
echo "<li><strong>اختبار الإخفاء:</strong> جرب إخفاء سلايد وتأكد من اختفائه من الصفحة الرئيسية</li>";
echo "<li><strong>اختبار الترتيب:</strong> غير ترتيب السلايدات وتأكد من تغير الترتيب في الصفحة الرئيسية</li>";
echo "</ol>";

// Performance test
echo "<h2>6. اختبار الأداء:</h2>";

$startTime = microtime(true);

try {
    $pdo = new PDO('sqlite:' . __DIR__ . '/database/database.sqlite');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $stmt = $pdo->query("SELECT * FROM slider_images WHERE is_active = 1 ORDER BY sort_order ASC");
    $slides = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $endTime = microtime(true);
    $queryTime = ($endTime - $startTime) * 1000; // Convert to milliseconds
    
    echo "<p>⚡ وقت تحميل السلايدات: <strong>" . number_format($queryTime, 2) . " مللي ثانية</strong></p>";
    
    if ($queryTime < 10) {
        echo "<p style='color: green;'>✅ الأداء ممتاز (أقل من 10 مللي ثانية)</p>";
    } elseif ($queryTime < 50) {
        echo "<p style='color: orange;'>⚠️ الأداء جيد (أقل من 50 مللي ثانية)</p>";
    } else {
        echo "<p style='color: red;'>❌ الأداء بطيء (أكثر من 50 مللي ثانية)</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في اختبار الأداء: " . $e->getMessage() . "</p>";
}

// Summary
echo "<h2>7. الملخص:</h2>";
echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>✅ تم إنجاز النظام بنجاح!</h3>";
echo "<p><strong>الميزات المكتملة:</strong></p>";
echo "<ul>";
echo "<li>🗄️ قاعدة بيانات للسلايدات</li>";
echo "<li>🖼️ عرض ديناميكي في الصفحة الرئيسية</li>";
echo "<li>⚙️ لوحة تحكم كاملة للأدمن</li>";
echo "<li>🎨 تخصيص كامل للمحتوى والتصميم</li>";
echo "<li>📱 تصميم متجاوب</li>";
echo "<li>🔒 حماية صفحات الأدمن</li>";
echo "</ul>";
echo "</div>";

echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>📋 الخطوات التالية:</h3>";
echo "<ol>";
echo "<li>اختبار جميع الوظائف</li>";
echo "<li>إضافة المزيد من السلايدات</li>";
echo "<li>تخصيص التصميم حسب الحاجة</li>";
echo "<li>رفع التحديثات إلى الخادم</li>";
echo "</ol>";
echo "</div>";

?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h1, h2, h3 {
    color: #333;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
}

details {
    margin: 10px 0;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
}

summary {
    cursor: pointer;
    font-weight: bold;
    padding: 5px;
}

table {
    font-size: 14px;
}

th, td {
    padding: 8px;
    text-align: right;
    border: 1px solid #ddd;
}

th {
    background-color: #f0f0f0;
}

ul, ol {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    margin: 10px 0;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}
</style>

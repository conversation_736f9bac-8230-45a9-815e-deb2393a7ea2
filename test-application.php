<?php
echo "<h1>Season Expo Application Test</h1>";

try {
    require '../vendor/autoload.php';
    $app = require_once '../bootstrap/app.php';
    
    echo "<h2>Laravel Application Status:</h2>";
    echo "✅ Laravel loaded successfully<br>";
    
    // Test database connection
    try {
        $exhibitions = \App\Models\Exhibition::count();
        echo "✅ Database connected: {$exhibitions} exhibitions<br>";
        
        $categories = \App\Models\Category::count();
        echo "✅ Categories: {$categories}<br>";
        
        $users = \App\Models\User::count();
        echo "✅ Users: {$users}<br>";
        
    } catch (Exception $e) {
        echo "❌ Database error: " . $e->getMessage() . "<br>";
    }
    
    // Test routes
    echo "<h2>Route Status:</h2>";
    $routes = \Illuminate\Support\Facades\Route::getRoutes();
    echo "✅ Total routes loaded: " . count($routes) . "<br>";
    
    // Check for Season Expo routes
    $seasonExpoRoutes = 0;
    foreach ($routes as $route) {
        $action = $route->getActionName();
        if (strpos($action, 'HomeController') !== false || 
            strpos($action, 'ExhibitionController') !== false ||
            strpos($action, 'BookingController') !== false) {
            $seasonExpoRoutes++;
        }
    }
    
    if ($seasonExpoRoutes > 0) {
        echo "✅ Season Expo routes found: {$seasonExpoRoutes}<br>";
    } else {
        echo "❌ No Season Expo routes found<br>";
    }
    
    // Test storage
    echo "<h2>Storage Status:</h2>";
    $storagePath = __DIR__ . '/storage';
    if (file_exists($storagePath)) {
        if (is_link($storagePath)) {
            echo "✅ Storage symlink working<br>";
            echo "Target: " . readlink($storagePath) . "<br>";
        } elseif (is_dir($storagePath)) {
            echo "✅ Storage directory working<br>";
        }
    } else {
        echo "❌ Storage not accessible<br>";
    }
    
    // Test configuration
    echo "<h2>Configuration Status:</h2>";
    $config = $app->make('config');
    echo "✅ App Name: " . $config->get('app.name') . "<br>";
    echo "✅ App Environment: " . $config->get('app.env') . "<br>";
    echo "✅ Database: " . $config->get('database.default') . "<br>";
    
    echo "<h2>🎉 Application Status Summary:</h2>";
    
    if ($exhibitions > 0 && $seasonExpoRoutes > 0) {
        echo "<div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px;'>";
        echo "<h3 style='color: #155724;'>✅ SUCCESS: Season Expo is Ready!</h3>";
        echo "<p>Your Season Expo application should now be fully functional.</p>";
        echo "<p><strong><a href='/' style='font-size: 18px;'>🚀 VISIT YOUR SEASON EXPO APPLICATION</a></strong></p>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px;'>";
        echo "<h3 style='color: #721c24;'>⚠️ Issues Found</h3>";
        echo "<p>Some components are not working correctly:</p>";
        if ($exhibitions == 0) echo "<p>- Database has no exhibitions</p>";
        if ($seasonExpoRoutes == 0) echo "<p>- Season Expo routes not loaded</p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px;'>";
    echo "<h3 style='color: #721c24;'>❌ Application Error</h3>";
    echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "<h2>Available Pages to Test:</h2>";
echo "<ul>";
echo "<li><a href='/'>Homepage</a></li>";
echo "<li><a href='/exhibitions'>Exhibitions</a></li>";
echo "<li><a href='/login'>Login</a></li>";
echo "<li><a href='/register'>Register</a></li>";
echo "<li><a href='/dashboard'>Dashboard</a> (after login)</li>";
echo "</ul>";
?>

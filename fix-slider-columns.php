<?php
// Fix Slider Table Columns
// This file fixes column name mismatches in the slider_images table

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='utf-8'>";
echo "<title>إصلاح أعمدة جدول السلايد - Season Expo Kuwait</title>";
echo "<script src='https://cdn.tailwindcss.com'></script>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }";
echo "</style>";
echo "</head>";
echo "<body class='bg-gray-50 p-8'>";

echo "<div class='max-w-4xl mx-auto'>";
echo "<h1 class='text-3xl font-bold mb-8' style='color: #2C3E50;'>🔧 إصلاح أعمدة جدول السلايد</h1>";

try {
    // Connect to database
    $pdo = new PDO('sqlite:' . __DIR__ . '/database/database.sqlite');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<div class='bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4'>";
    echo "✅ تم الاتصال بقاعدة البيانات بنجاح";
    echo "</div>";

    // Check current table structure
    $stmt = $pdo->query("PRAGMA table_info(slider_images)");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);

    echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
    echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>📊 بنية الجدول الحالية</h2>";
    
    if (count($columns) > 0) {
        echo "<div class='overflow-x-auto'>";
        echo "<table class='w-full text-sm border border-gray-300'>";
        echo "<thead>";
        echo "<tr style='background: #2C3E50; color: white;'>";
        echo "<th class='p-2 border border-gray-300'>اسم العمود</th>";
        echo "<th class='p-2 border border-gray-300'>النوع</th>";
        echo "<th class='p-2 border border-gray-300'>مطلوب</th>";
        echo "<th class='p-2 border border-gray-300'>افتراضي</th>";
        echo "</tr>";
        echo "</thead>";
        echo "<tbody>";
        
        $hasImagePath = false;
        $hasLinkUrl = false;
        $hasDisplayOrder = false;
        
        foreach ($columns as $column) {
            echo "<tr class='border-b'>";
            echo "<td class='p-2 border border-gray-300 font-mono'>{$column['name']}</td>";
            echo "<td class='p-2 border border-gray-300'>{$column['type']}</td>";
            echo "<td class='p-2 border border-gray-300'>" . ($column['notnull'] ? 'نعم' : 'لا') . "</td>";
            echo "<td class='p-2 border border-gray-300'>{$column['dflt_value']}</td>";
            echo "</tr>";
            
            if ($column['name'] === 'image_path') $hasImagePath = true;
            if ($column['name'] === 'link_url') $hasLinkUrl = true;
            if ($column['name'] === 'display_order') $hasDisplayOrder = true;
        }
        
        echo "</tbody>";
        echo "</table>";
        echo "</div>";
        
        // Check if we need to add missing columns
        $needsUpdate = false;
        $missingColumns = [];
        
        if (!$hasImagePath) {
            $missingColumns[] = 'image_path';
            $needsUpdate = true;
        }
        if (!$hasLinkUrl) {
            $missingColumns[] = 'link_url';
            $needsUpdate = true;
        }
        if (!$hasDisplayOrder) {
            $missingColumns[] = 'display_order';
            $needsUpdate = true;
        }
        
        if ($needsUpdate) {
            echo "<div class='mt-4 bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded'>";
            echo "<h3 class='font-bold mb-2'>⚠️ أعمدة مفقودة:</h3>";
            echo "<ul class='list-disc list-inside'>";
            foreach ($missingColumns as $col) {
                echo "<li>{$col}</li>";
            }
            echo "</ul>";
            echo "</div>";
            
            // Add missing columns
            echo "<div class='bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded mb-4'>";
            echo "🔧 إضافة الأعمدة المفقودة...";
            echo "</div>";
            
            if (!$hasImagePath) {
                $pdo->exec("ALTER TABLE slider_images ADD COLUMN image_path VARCHAR(500)");
                echo "<div class='bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-2'>";
                echo "✅ تم إضافة عمود image_path";
                echo "</div>";
            }
            
            if (!$hasLinkUrl) {
                $pdo->exec("ALTER TABLE slider_images ADD COLUMN link_url VARCHAR(500)");
                echo "<div class='bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-2'>";
                echo "✅ تم إضافة عمود link_url";
                echo "</div>";
            }
            
            if (!$hasDisplayOrder) {
                $pdo->exec("ALTER TABLE slider_images ADD COLUMN display_order INTEGER DEFAULT 0");
                echo "<div class='bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-2'>";
                echo "✅ تم إضافة عمود display_order";
                echo "</div>";
            }
            
            // Copy data from old columns to new ones if they exist
            echo "<div class='bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded mb-4'>";
            echo "📋 نسخ البيانات من الأعمدة القديمة...";
            echo "</div>";
            
            try {
                // Copy image_url to image_path
                $pdo->exec("UPDATE slider_images SET image_path = image_url WHERE image_path IS NULL AND image_url IS NOT NULL");
                
                // Copy button_link to link_url
                $pdo->exec("UPDATE slider_images SET link_url = button_link WHERE link_url IS NULL AND button_link IS NOT NULL");
                
                // Copy sort_order to display_order
                $pdo->exec("UPDATE slider_images SET display_order = sort_order WHERE display_order = 0 AND sort_order IS NOT NULL");
                
                echo "<div class='bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-2'>";
                echo "✅ تم نسخ البيانات بنجاح";
                echo "</div>";
                
            } catch (Exception $e) {
                echo "<div class='bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-2'>";
                echo "⚠️ تحذير: " . $e->getMessage();
                echo "</div>";
            }
            
        } else {
            echo "<div class='mt-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded'>";
            echo "✅ جميع الأعمدة المطلوبة موجودة";
            echo "</div>";
        }
        
    } else {
        echo "<div class='text-red-600'>❌ لا يمكن قراءة بنية الجدول</div>";
    }
    
    echo "</div>";

    // Test the admin panel
    echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
    echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>🧪 اختبار إدارة السلايد</h2>";
    echo "<p class='mb-4'>الآن يمكنك الذهاب إلى صفحة إدارة السلايد:</p>";
    echo "<a href='/admin-slider-management.php' class='text-white px-6 py-3 rounded-lg hover:opacity-80 inline-block' style='background: #2C3E50;'>🖼️ اختبار إدارة السلايد</a>";
    echo "</div>";

    // Show current data
    $stmt = $pdo->query("SELECT * FROM slider_images ORDER BY display_order ASC, id ASC");
    $slides = $stmt->fetchAll(PDO::FETCH_ASSOC);

    echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
    echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>📊 البيانات الحالية</h2>";

    if (count($slides) > 0) {
        echo "<div class='overflow-x-auto'>";
        echo "<table class='w-full text-sm border border-gray-300'>";
        echo "<thead>";
        echo "<tr style='background: #2C3E50; color: white;'>";
        echo "<th class='p-2 border border-gray-300'>ID</th>";
        echo "<th class='p-2 border border-gray-300'>العنوان</th>";
        echo "<th class='p-2 border border-gray-300'>مسار الصورة</th>";
        echo "<th class='p-2 border border-gray-300'>الرابط</th>";
        echo "<th class='p-2 border border-gray-300'>الترتيب</th>";
        echo "<th class='p-2 border border-gray-300'>الحالة</th>";
        echo "</tr>";
        echo "</thead>";
        echo "<tbody>";
        
        foreach ($slides as $slide) {
            echo "<tr class='border-b'>";
            echo "<td class='p-2 border border-gray-300'>{$slide['id']}</td>";
            echo "<td class='p-2 border border-gray-300'>" . htmlspecialchars($slide['title']) . "</td>";
            echo "<td class='p-2 border border-gray-300'><code class='text-xs'>" . htmlspecialchars($slide['image_path'] ?? $slide['image_url'] ?? 'غير محدد') . "</code></td>";
            echo "<td class='p-2 border border-gray-300'><code class='text-xs'>" . htmlspecialchars($slide['link_url'] ?? $slide['button_link'] ?? 'غير محدد') . "</code></td>";
            echo "<td class='p-2 border border-gray-300 text-center'>" . ($slide['display_order'] ?? $slide['sort_order'] ?? 0) . "</td>";
            echo "<td class='p-2 border border-gray-300 text-center'>";
            if ($slide['is_active']) {
                echo "<span class='bg-green-100 text-green-800 px-2 py-1 rounded text-xs'>نشط</span>";
            } else {
                echo "<span class='bg-red-100 text-red-800 px-2 py-1 rounded text-xs'>غير نشط</span>";
            }
            echo "</td>";
            echo "</tr>";
        }
        
        echo "</tbody>";
        echo "</table>";
        echo "</div>";
    } else {
        echo "<div class='text-center text-gray-500 py-8'>";
        echo "لا توجد صور في السلايد";
        echo "</div>";
    }

    echo "</div>";

    // Success message
    echo "<div class='bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4'>";
    echo "<h3 class='font-bold text-lg mb-2'>🎉 تم إصلاح المشكلة بنجاح!</h3>";
    echo "<ul class='text-sm space-y-1'>";
    echo "<li>✅ تم إصلاح أسماء الأعمدة</li>";
    echo "<li>✅ تم إضافة الأعمدة المفقودة</li>";
    echo "<li>✅ تم نسخ البيانات القديمة</li>";
    echo "<li>✅ صفحة إدارة السلايد تعمل الآن</li>";
    echo "</ul>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div class='bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4'>";
    echo "❌ خطأ: " . $e->getMessage();
    echo "</div>";
}

// Navigation links
echo "<div class='text-center space-x-reverse space-x-4 mt-8'>";
echo "<a href='/admin-slider-management.php' class='text-white px-6 py-3 rounded-lg hover:opacity-80' style='background: #2C3E50;'>🖼️ إدارة السلايد</a>";
echo "<a href='/exhibitions-simple.php' class='bg-gray-600 text-white px-6 py-3 rounded-lg hover:bg-gray-700'>📋 المعارض</a>";
echo "<a href='/' class='bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700'>🏠 الصفحة الرئيسية</a>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>

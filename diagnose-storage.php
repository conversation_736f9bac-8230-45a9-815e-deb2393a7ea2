<?php
echo "<h1>Storage Symlink Diagnosis</h1>";

$publicPath = __DIR__;
$storagePath = dirname(__DIR__) . '/storage/app/public';
$linkPath = $publicPath . '/storage';

echo "<h2>Path Information:</h2>";
echo "Public path: {$publicPath}<br>";
echo "Storage target: {$storagePath}<br>";
echo "Link path: {$linkPath}<br><br>";

echo "<h2>Directory Checks:</h2>";

// Check if storage/app/public exists
if (is_dir($storagePath)) {
    echo "✅ Storage target directory exists<br>";
    echo "Target permissions: " . substr(sprintf('%o', fileperms($storagePath)), -4) . "<br>";
} else {
    echo "❌ Storage target directory missing<br>";
}

// Check if public_html is writable
if (is_writable($publicPath)) {
    echo "✅ Public directory is writable<br>";
} else {
    echo "❌ Public directory is not writable<br>";
}

// Check if storage link already exists
if (file_exists($linkPath)) {
    if (is_link($linkPath)) {
        echo "⚠️ Storage symlink already exists<br>";
        echo "Current target: " . readlink($linkPath) . "<br>";
    } else {
        echo "⚠️ Storage exists but is not a symlink<br>";
        if (is_dir($linkPath)) {
            echo "It's a directory<br>";
        } else {
            echo "It's a file<br>";
        }
    }
} else {
    echo "✅ No existing storage link/directory<br>";
}

echo "<h2>Alternative Solutions:</h2>";

// Solution 1: Try creating symlink with different method
echo "<h3>Solution 1: Alternative Symlink Creation</h3>";
if (file_exists($linkPath)) {
    if (is_dir($linkPath) && !is_link($linkPath)) {
        rmdir($linkPath);
        echo "Removed existing directory<br>";
    } elseif (is_link($linkPath)) {
        unlink($linkPath);
        echo "Removed existing symlink<br>";
    }
}

if (function_exists('symlink')) {
    if (symlink($storagePath, $linkPath)) {
        echo "✅ Symlink created successfully with alternative method!<br>";
    } else {
        echo "❌ Symlink still failed<br>";
        
        // Solution 2: Copy files instead of symlink
        echo "<h3>Solution 2: Copy Files (Fallback)</h3>";
        if (is_dir($storagePath)) {
            if (!is_dir($linkPath)) {
                mkdir($linkPath, 0755, true);
            }
            
            // Copy files from storage to public
            $files = scandir($storagePath);
            $copied = 0;
            foreach ($files as $file) {
                if ($file != '.' && $file != '..') {
                    $source = $storagePath . '/' . $file;
                    $dest = $linkPath . '/' . $file;
                    if (copy($source, $dest)) {
                        $copied++;
                    }
                }
            }
            echo "✅ Copied {$copied} files from storage to public<br>";
            echo "Note: You'll need to manually copy new uploaded files<br>";
        }
    }
} else {
    echo "❌ Symlink function not available on this server<br>";
}

echo "<h2>Manual Solution:</h2>";
echo "<div style='background: #ffffcc; padding: 15px; border: 1px solid #ffcc00;'>";
echo "<h3>If symlink fails, use File Manager:</h3>";
echo "<ol>";
echo "<li>Go to Hostinger File Manager</li>";
echo "<li>Navigate to: /home/<USER>/domains/myapps.fjt-q8.com/storage/app/public/</li>";
echo "<li>Copy all files and folders from there</li>";
echo "<li>Navigate to: /home/<USER>/domains/myapps.fjt-q8.com/public_html/</li>";
echo "<li>Create a folder called 'storage'</li>";
echo "<li>Paste all the copied files into public_html/storage/</li>";
echo "</ol>";
echo "</div>";

echo "<h2>Test Storage Access:</h2>";
echo "<a href='/storage/' target='_blank'>Test Storage Directory</a><br>";
echo "<small>This should show uploaded files or a directory listing</small>";
?>

<?php
echo "<h1>Clear All Laravel Caches</h1>";

try {
    require '../vendor/autoload.php';
    $app = require_once '../bootstrap/app.php';
    $kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
    
    echo "<h2>Clearing Caches:</h2>";
    
    // Clear all caches
    try {
        $kernel->call('config:clear');
        echo "✅ Config cache cleared<br>";
    } catch (Exception $e) {
        echo "❌ Config clear failed: " . $e->getMessage() . "<br>";
    }
    
    try {
        $kernel->call('cache:clear');
        echo "✅ Application cache cleared<br>";
    } catch (Exception $e) {
        echo "❌ Cache clear failed: " . $e->getMessage() . "<br>";
    }
    
    try {
        $kernel->call('view:clear');
        echo "✅ View cache cleared<br>";
    } catch (Exception $e) {
        echo "❌ View clear failed: " . $e->getMessage() . "<br>";
    }
    
    try {
        $kernel->call('route:clear');
        echo "✅ Route cache cleared<br>";
    } catch (Exception $e) {
        echo "❌ Route clear failed: " . $e->getMessage() . "<br>";
    }
    
    // Clear compiled views manually
    $viewPath = dirname(__DIR__) . '/storage/framework/views';
    if (is_dir($viewPath)) {
        $files = glob($viewPath . '/*');
        $deleted = 0;
        foreach ($files as $file) {
            if (is_file($file) && pathinfo($file, PATHINFO_EXTENSION) === 'php') {
                unlink($file);
                $deleted++;
            }
        }
        echo "✅ Manually deleted {$deleted} compiled view files<br>";
    }
    
    echo "<br><h2>🎉 All caches cleared!</h2>";
    echo "<p><strong><a href='/'>Test Application Now</a></strong></p>";
    
} catch (Exception $e) {
    echo "❌ Error clearing caches: " . $e->getMessage() . "<br>";
    echo "<br><h2>Manual Cache Clear:</h2>";
    echo "<p>Try deleting these directories manually:</p>";
    echo "<ul>";
    echo "<li>storage/framework/cache/data/*</li>";
    echo "<li>storage/framework/views/*</li>";
    echo "<li>storage/framework/sessions/*</li>";
    echo "</ul>";
}
?>

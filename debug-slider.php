<?php
// Debug Slider Issues

echo "<h1>🔍 تشخيص مشاكل السلايد</h1>";

echo "<h2>1. فحص قاعدة البيانات:</h2>";

try {
    $pdo = new PDO('sqlite:' . __DIR__ . '/database/database.sqlite');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $stmt = $pdo->query("SELECT * FROM slider_images ORDER BY sort_order ASC");
    $slides = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p>📊 إجمالي السلايدات: <strong>" . count($slides) . "</strong></p>";
    
    $activeSlides = array_filter($slides, function($slide) {
        return $slide['is_active'] == 1;
    });
    
    echo "<p>👁️ السلايدات النشطة: <strong>" . count($activeSlides) . "</strong></p>";
    
    if ($slides) {
        echo "<h3>تفاصيل السلايدات:</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th>ID</th><th>العنوان</th><th>الصورة</th><th>نشط</th><th>الترتيب</th><th>الزر</th>";
        echo "</tr>";
        
        foreach ($slides as $slide) {
            $status = $slide['is_active'] ? '✅' : '❌';
            echo "<tr>";
            echo "<td>{$slide['id']}</td>";
            echo "<td>" . htmlspecialchars(substr($slide['title'], 0, 20)) . "...</td>";
            echo "<td><a href='{$slide['image_url']}' target='_blank'>عرض</a></td>";
            echo "<td>$status</td>";
            echo "<td>{$slide['sort_order']}</td>";
            echo "<td>" . htmlspecialchars($slide['button_text'] ?? 'لا يوجد') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
}

echo "<h2>2. اختبار السلايد في الصفحة الرئيسية:</h2>";

// Test homepage slider data
try {
    $pdo = new PDO('sqlite:' . __DIR__ . '/database/database.sqlite');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $stmt = $pdo->query("SELECT * FROM slider_images WHERE is_active = 1 ORDER BY sort_order ASC");
    $homeSlides = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p>🏠 السلايدات التي ستظهر في الصفحة الرئيسية: <strong>" . count($homeSlides) . "</strong></p>";
    
    if ($homeSlides) {
        echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; margin: 20px 0;'>";
        foreach ($homeSlides as $index => $slide) {
            echo "<div style='border: 1px solid #ddd; padding: 10px; border-radius: 5px; background: white;'>";
            echo "<h4>سلايد " . ($index + 1) . "</h4>";
            echo "<p><strong>العنوان:</strong> " . htmlspecialchars($slide['title']) . "</p>";
            echo "<p><strong>الترتيب:</strong> " . $slide['sort_order'] . "</p>";
            echo "<img src='{$slide['image_url']}' style='width: 100%; height: 100px; object-fit: cover; border-radius: 3px;' onerror='this.style.backgroundColor=\"{$slide['background_color']}\"; this.innerHTML=\"صورة\"; this.style.color=\"white\"; this.style.display=\"flex\"; this.style.alignItems=\"center\"; this.style.justifyContent=\"center\";'>";
            echo "</div>";
        }
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في اختبار الصفحة الرئيسية: " . $e->getMessage() . "</p>";
}

echo "<h2>3. اختبار JavaScript للسلايد:</h2>";

echo "<div id='slider-test' style='background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
echo "<p>اختبار JavaScript...</p>";
echo "</div>";

echo "<script>";
echo "document.addEventListener('DOMContentLoaded', function() {";
echo "    const testDiv = document.getElementById('slider-test');";
echo "    ";
echo "    // Test if we can access slider variables";
echo "    testDiv.innerHTML = '<h4>نتائج اختبار JavaScript:</h4>';";
echo "    ";
echo "    // Simulate slider data";
echo "    const totalSlides = " . count($homeSlides ?? []) . ";";
echo "    testDiv.innerHTML += '<p>عدد السلايدات في JavaScript: ' + totalSlides + '</p>';";
echo "    ";
echo "    if (totalSlides > 1) {";
echo "        testDiv.innerHTML += '<p style=\"color: green;\">✅ يجب أن يعمل السلايد بشكل صحيح</p>';";
echo "    } else {";
echo "        testDiv.innerHTML += '<p style=\"color: red;\">❌ مشكلة: عدد السلايدات قليل</p>';";
echo "    }";
echo "    ";
echo "    // Test slider functions";
echo "    testDiv.innerHTML += '<button onclick=\"testSliderFunctions()\" style=\"background: #007bff; color: white; padding: 10px; border: none; border-radius: 5px; margin: 10px 0;\">اختبار وظائف السلايد</button>';";
echo "});";
echo "";
echo "function testSliderFunctions() {";
echo "    const testDiv = document.getElementById('slider-test');";
echo "    testDiv.innerHTML += '<p>🧪 اختبار وظائف السلايد...</p>';";
echo "    ";
echo "    // Test if slider elements exist on homepage";
echo "    testDiv.innerHTML += '<p><a href=\"/\" target=\"_blank\">افتح الصفحة الرئيسية لاختبار السلايد</a></p>';";
echo "}";
echo "</script>";

echo "<h2>4. المشاكل المحتملة وحلولها:</h2>";

echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
echo "<h3>مشكلة: السلايد يعرض صورة واحدة فقط</h3>";
echo "<p><strong>الأسباب المحتملة:</strong></p>";
echo "<ul>";
echo "<li>مشكلة في CSS - عرض السلايدات</li>";
echo "<li>مشكلة في JavaScript - عدد السلايدات</li>";
echo "<li>السلايدات غير نشطة في قاعدة البيانات</li>";
echo "<li>مشكلة في ترتيب السلايدات</li>";
echo "</ul>";
echo "</div>";

echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
echo "<h3>مشكلة: لا يمكن تعديل الصور</h3>";
echo "<p><strong>الأسباب المحتملة:</strong></p>";
echo "<ul>";
echo "<li>مشكلة في JavaScript - وظائف التعديل</li>";
echo "<li>مشكلة في النموذج - إرسال البيانات</li>";
echo "<li>مشكلة في معالجة POST requests</li>";
echo "<li>مشكلة في صلاحيات قاعدة البيانات</li>";
echo "</ul>";
echo "</div>";

echo "<h2>5. اختبار سريع للتعديل:</h2>";

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_edit'])) {
    try {
        $pdo = new PDO('sqlite:' . __DIR__ . '/database/database.sqlite');
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // Test updating a slide
        $stmt = $pdo->prepare("UPDATE slider_images SET updated_at = CURRENT_TIMESTAMP WHERE id = 1");
        $result = $stmt->execute();
        
        if ($result) {
            echo "<p style='color: green;'>✅ اختبار التعديل نجح - يمكن تحديث قاعدة البيانات</p>";
        } else {
            echo "<p style='color: red;'>❌ اختبار التعديل فشل</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ خطأ في اختبار التعديل: " . $e->getMessage() . "</p>";
    }
}

echo "<form method='POST'>";
echo "<button type='submit' name='test_edit' style='background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px;'>اختبار وظيفة التعديل</button>";
echo "</form>";

echo "<h2>6. الروابط للإصلاح:</h2>";

echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0;'>";

$fixLinks = [
    '/' => 'الصفحة الرئيسية - اختبار السلايد',
    '/admin/slider' => 'لوحة تحكم الأدمن',
    '/admin-slider-management.php' => 'إدارة مباشرة (PHP)',
    '/homepage-fixed.php' => 'الصفحة المصححة'
];

foreach ($fixLinks as $url => $desc) {
    echo "<a href='$url' target='_blank' style='background: #007bff; color: white; padding: 15px; text-decoration: none; border-radius: 8px; text-align: center; display: block;'>";
    echo "<strong>" . basename($url) . "</strong><br>";
    echo "<small>$desc</small>";
    echo "</a>";
}

echo "</div>";

?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
    background-color: #f8f9fa;
}

h1, h2, h3 {
    color: #333;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
}

table {
    font-size: 12px;
    margin: 10px 0;
}

th, td {
    padding: 5px;
    text-align: center;
    border: 1px solid #ddd;
}

th {
    background-color: #f0f0f0;
}

ul {
    margin: 10px 0;
    padding-left: 20px;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}
</style>

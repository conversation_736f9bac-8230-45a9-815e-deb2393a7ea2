<script setup>
import { Head, Link, router } from '@inertiajs/vue3';
import { ref } from 'vue';
import LanguageSwitcher from '@/components/LanguageSwitcher.vue';

const props = defineProps({
    signatures: Object,
    documentTypes: Object,
    filters: Object,
    locale: String,
    translations: Object,
});

const t = (key) => {
    return props.translations?.app?.[key] || key;
};

const isRTL = props.locale === 'ar';

const selectedType = ref(props.filters.document_type || '');
const selectedVerified = ref(props.filters.verified || '');

const filterSignatures = () => {
    router.get(route('signatures.index'), {
        document_type: selectedType.value,
        verified: selectedVerified.value,
    }, {
        preserveState: true,
        replace: true,
    });
};

const deleteSignature = (signatureId) => {
    if (confirm(t('confirm_delete_signature'))) {
        router.delete(route('signatures.destroy', signatureId));
    }
};

const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString(props.locale === 'ar' ? 'ar-SA' : 'en-US');
};

const getStatusColor = (isVerified) => {
    return isVerified ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800';
};

const getStatusText = (isVerified) => {
    return isVerified ? t('verified') : t('pending_verification');
};
</script>

<template>
    <Head :title="t('my_signatures')" />

    <div class="min-h-screen bg-gray-50" :dir="isRTL ? 'rtl' : 'ltr'">
        <!-- Fixed Navigation -->
        <nav class="fixed top-0 left-0 right-0 fixed-nav z-50">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <div class="flex items-center">
                        <Link href="/" class="text-2xl font-bold text-blue-600 hover:text-blue-700 transition-colors">
                            {{ t('site_name') }}
                        </Link>
                    </div>
                    <div class="flex items-center" :class="isRTL ? 'space-x-reverse space-x-4' : 'space-x-4'">
                        <LanguageSwitcher :current-locale="locale" />
                        <Link href="/dashboard" class="nav-link text-gray-600">{{ t('dashboard') }}</Link>
                        <Link href="/" class="nav-link text-gray-600">{{ t('view_homepage') }}</Link>
                    </div>
                </div>
            </div>
        </nav>

        <div class="py-12 pt-24">
            <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
                <!-- Header -->
                <div class="mb-8">
                    <div class="flex justify-between items-center">
                        <div>
                            <h1 class="text-3xl font-bold text-gray-900">{{ t('my_signatures') }}</h1>
                            <p class="text-gray-600 mt-2">{{ t('manage_digital_signatures') }}</p>
                        </div>
                        <Link 
                            :href="route('signatures.create')"
                            class="btn-primary bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700"
                        >
                            {{ t('create_new_signature') }}
                        </Link>
                    </div>
                </div>

                <!-- Filters -->
                <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">{{ t('document_type') }}</label>
                            <select 
                                v-model="selectedType" 
                                @change="filterSignatures"
                                class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                            >
                                <option value="">{{ t('all_types') }}</option>
                                <option v-for="(label, value) in documentTypes" :key="value" :value="value">
                                    {{ label }}
                                </option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">{{ t('verification_status') }}</label>
                            <select 
                                v-model="selectedVerified" 
                                @change="filterSignatures"
                                class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                            >
                                <option value="">{{ t('all_statuses') }}</option>
                                <option value="true">{{ t('verified') }}</option>
                                <option value="false">{{ t('pending_verification') }}</option>
                            </select>
                        </div>
                        <div class="flex items-end">
                            <button 
                                @click="selectedType = ''; selectedVerified = ''; filterSignatures()"
                                class="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors"
                            >
                                {{ t('clear_filters') }}
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Signatures List -->
                <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        {{ t('document') }}
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        {{ t('signer') }}
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        {{ t('signed_date') }}
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        {{ t('status') }}
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        {{ t('actions') }}
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr v-for="signature in signatures.data" :key="signature.id" class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div>
                                            <div class="text-sm font-medium text-gray-900">
                                                {{ documentTypes[signature.document_type] }}
                                            </div>
                                            <div class="text-sm text-gray-500">
                                                ID: {{ signature.document_id }}
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div>
                                            <div class="text-sm font-medium text-gray-900">
                                                {{ signature.signer_name }}
                                            </div>
                                            <div class="text-sm text-gray-500">
                                                {{ signature.signer_email }}
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {{ formatDate(signature.signed_at) }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span 
                                            :class="getStatusColor(signature.is_verified)"
                                            class="px-2 py-1 text-xs rounded-full"
                                        >
                                            {{ getStatusText(signature.is_verified) }}
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex items-center space-x-3">
                                            <Link 
                                                :href="route('signatures.show', signature.id)"
                                                class="text-blue-600 hover:text-blue-800"
                                            >
                                                {{ t('view') }}
                                            </Link>
                                            <a 
                                                :href="route('signatures.certificate', signature.id)"
                                                class="text-green-600 hover:text-green-800"
                                                target="_blank"
                                            >
                                                {{ t('certificate') }}
                                            </a>
                                            <button 
                                                @click="deleteSignature(signature.id)"
                                                class="text-red-600 hover:text-red-800"
                                            >
                                                {{ t('delete') }}
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Empty State -->
                <div v-if="!signatures.data.length" class="text-center py-12">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">{{ t('no_signatures_found') }}</h3>
                    <p class="mt-1 text-sm text-gray-500">{{ t('create_first_signature') }}</p>
                    <div class="mt-6">
                        <Link 
                            :href="route('signatures.create')"
                            class="btn-primary bg-blue-600 text-white px-4 py-2 rounded-lg font-semibold hover:bg-blue-700"
                        >
                            {{ t('create_new_signature') }}
                        </Link>
                    </div>
                </div>

                <!-- Pagination -->
                <div v-if="signatures.links && signatures.links.length > 3" class="mt-8">
                    <nav class="flex justify-center">
                        <div class="flex space-x-1">
                            <Link 
                                v-for="link in signatures.links" 
                                :key="link.label"
                                :href="link.url"
                                v-html="link.label"
                                :class="[
                                    'px-3 py-2 text-sm rounded-lg',
                                    link.active 
                                        ? 'bg-blue-600 text-white' 
                                        : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300'
                                ]"
                            />
                        </div>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</template>

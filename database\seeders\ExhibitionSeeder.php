<?php

namespace Database\Seeders;

use App\Models\Exhibition;
use App\Models\Category;
use App\Models\User;
use App\Models\Booth;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class ExhibitionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = Category::all();
        $organizers = User::where('role', 'organizer')->get();

        if ($organizers->isEmpty()) {
            $organizers = User::take(3)->get();
        }

        $exhibitions = [
            [
                'title' => 'Tech Innovation Summit 2024',
                'slug' => 'tech-innovation-summit-2024',
                'description' => 'Join us for the biggest technology exhibition of the year featuring cutting-edge innovations, AI solutions, and the latest in software development. Connect with industry leaders and discover the future of technology.',
                'short_description' => 'The biggest technology exhibition featuring AI, software development, and industry innovations.',
                'venue_name' => 'Dubai World Trade Centre',
                'venue_address' => 'Sheikh Zayed Road, Trade Centre, Dubai, UAE',
                'city' => 'Dubai',
                'country' => 'UAE',
                'start_date' => now()->addDays(30),
                'end_date' => now()->addDays(33),
                'registration_start' => now()->addDays(5),
                'registration_end' => now()->addDays(25),
                'status' => 'published',
                'is_featured' => true,
                'booth_price_from' => 2500.00,
                'currency' => 'USD',
                'max_booths' => 200,
            ],
            [
                'title' => 'Healthcare & Medical Expo 2024',
                'slug' => 'healthcare-medical-expo-2024',
                'description' => 'Discover the latest in healthcare technology, medical devices, and pharmaceutical innovations. Network with healthcare professionals and explore solutions that are transforming patient care.',
                'short_description' => 'Latest healthcare technology, medical devices, and pharmaceutical innovations.',
                'venue_name' => 'Kuwait International Fair',
                'venue_address' => 'Mishref, Kuwait City, Kuwait',
                'city' => 'Kuwait City',
                'country' => 'Kuwait',
                'start_date' => now()->addDays(45),
                'end_date' => now()->addDays(48),
                'registration_start' => now()->addDays(10),
                'registration_end' => now()->addDays(40),
                'status' => 'published',
                'is_featured' => true,
                'booth_price_from' => 1800.00,
                'currency' => 'USD',
                'max_booths' => 150,
            ],
            [
                'title' => 'Food & Beverage Festival',
                'slug' => 'food-beverage-festival',
                'description' => 'A culinary journey featuring the finest food and beverage brands, innovative cooking equipment, and gourmet experiences. Perfect for restaurants, cafes, and food enthusiasts.',
                'short_description' => 'Culinary journey with finest food brands and innovative cooking equipment.',
                'venue_name' => 'Riyadh International Convention Center',
                'venue_address' => 'King Khalid International Airport Road, Riyadh, Saudi Arabia',
                'city' => 'Riyadh',
                'country' => 'Saudi Arabia',
                'start_date' => now()->addDays(60),
                'end_date' => now()->addDays(63),
                'registration_start' => now()->addDays(15),
                'registration_end' => now()->addDays(55),
                'status' => 'published',
                'is_featured' => false,
                'booth_price_from' => 1200.00,
                'currency' => 'USD',
                'max_booths' => 120,
            ],
        ];

        foreach ($exhibitions as $index => $exhibitionData) {
            $category = $categories->where('slug', [
                'technology-innovation',
                'healthcare-medical',
                'food-beverage'
            ][$index])->first();

            if (!$category) {
                $category = $categories->random();
            }

            $organizer = $organizers->random();

            $exhibition = Exhibition::create([
                ...$exhibitionData,
                'category_id' => $category->id,
                'organizer_id' => $organizer->id,
            ]);

            // Create booths for each exhibition
            $this->createBooths($exhibition);
        }
    }

    private function createBooths(Exhibition $exhibition): void
    {
        $sizes = ['small', 'medium', 'large', 'premium'];
        $features = [
            ['electricity', 'wifi'],
            ['electricity', 'wifi', 'storage'],
            ['electricity', 'wifi', 'storage', 'meeting_room'],
            ['electricity', 'wifi', 'storage', 'meeting_room', 'premium_location'],
        ];

        $basePrices = [
            'small' => 800,
            'medium' => 1200,
            'large' => 1800,
            'premium' => 2500,
        ];

        for ($i = 1; $i <= 50; $i++) {
            $size = $sizes[array_rand($sizes)];
            $isCorner = $i % 10 === 0; // Every 10th booth is a corner booth
            $isFeatured = $i <= 5; // First 5 booths are featured

            Booth::create([
                'exhibition_id' => $exhibition->id,
                'booth_number' => sprintf('B%03d', $i),
                'name' => "Booth {$i}",
                'description' => "Premium {$size} booth space with excellent visibility",
                'size' => $size,
                'width' => $size === 'small' ? 3 : ($size === 'medium' ? 4 : ($size === 'large' ? 6 : 8)),
                'height' => $size === 'small' ? 3 : ($size === 'medium' ? 4 : ($size === 'large' ? 6 : 8)),
                'area' => $size === 'small' ? 9 : ($size === 'medium' ? 16 : ($size === 'large' ? 36 : 64)),
                'price' => $basePrices[$size] + ($isCorner ? 200 : 0) + ($isFeatured ? 300 : 0),
                'location' => "Hall A, Section " . ceil($i / 10),
                'features' => $features[array_search($size, $sizes)],
                'position' => ['x' => ($i % 10) * 50, 'y' => floor($i / 10) * 50],
                'status' => 'available',
                'is_featured' => $isFeatured,
                'is_corner' => $isCorner,
            ]);
        }
    }
}

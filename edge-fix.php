<?php
// Force no caching for Edge
header("Cache-Control: no-cache, no-store, must-revalidate, max-age=0");
header("Pragma: no-cache");
header("Expires: Thu, 01 Jan 1970 00:00:00 GMT");
header("Last-Modified: " . gmdate("D, d M Y H:i:s") . " GMT");

// Detect Edge
$isEdge = strpos($_SERVER['HTTP_USER_AGENT'], 'Edg') !== false;

echo "<!DOCTYPE html>";
echo "<html>";
echo "<head>";
echo "<meta charset='utf-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1'>";
echo "<title>Edge Fix Test</title>";
echo "</head>";
echo "<body>";

echo "<h1>Microsoft Edge Fix Test</h1>";
echo "<p>Browser: " . ($isEdge ? "Microsoft Edge" : "Other Browser") . "</p>";
echo "<p>Time: " . date('Y-m-d H:i:s') . "</p>";

if ($isEdge) {
    echo "<div style='background: #ffffcc; padding: 10px; border: 1px solid #ffcc00;'>";
    echo "<h2>Edge-Specific Instructions:</h2>";
    echo "<ol>";
    echo "<li>Press <strong>Ctrl+Shift+Delete</strong> to clear cache</li>";
    echo "<li>Select <strong>'All time'</strong></li>";
    echo "<li>Check all boxes and click <strong>'Clear now'</strong></li>";
    echo "<li>Press <strong>F12</strong> to open Developer Tools</li>";
    echo "<li>Go to <strong>Network tab</strong></li>";
    echo "<li>Check <strong>'Disable cache'</strong></li>";
    echo "<li>Refresh this page</li>";
    echo "</ol>";
    echo "</div>";
}

// Test Laravel
echo "<h2>Laravel Test:</h2>";
try {
    require '../vendor/autoload.php';
    $app = require_once '../bootstrap/app.php';
    
    echo "<p style='color: green;'>✅ Laravel is working in your browser!</p>";
    
    // Get some data
    $exhibitions = \App\Models\Exhibition::take(2)->get();
    echo "<p>✅ Found " . count($exhibitions) . " exhibitions:</p>";
    
    foreach ($exhibitions as $exhibition) {
        echo "<p>- " . htmlspecialchars($exhibition->title) . "</p>";
    }
    
    echo "<div style='background: #ccffcc; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
    echo "<h3>✅ SUCCESS!</h3>";
    echo "<p>Laravel is working correctly in your browser.</p>";
    echo "<p><strong><a href='/' style='font-size: 18px;'>Click here to test your main Season Expo application</a></strong></p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<h2>Browser Compatibility Test:</h2>";
echo "<div id='js-test' style='color: red;'>JavaScript not working</div>";
echo "<script>";
echo "document.getElementById('js-test').innerHTML = '<span style=\"color: green;\">✅ JavaScript is working</span>';";
echo "console.log('Edge fix script loaded successfully');";
echo "</script>";

echo "</body>";
echo "</html>";
?>

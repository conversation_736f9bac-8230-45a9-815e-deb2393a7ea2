<script setup>
import { Head, Link, useForm } from '@inertiajs/vue3';
import { ref, onMounted } from 'vue';
import LanguageSwitcher from '@/components/LanguageSwitcher.vue';

const props = defineProps({
    documentType: String,
    documentId: String,
    documentTypes: Object,
    locale: String,
    translations: Object,
});

const t = (key) => {
    return props.translations?.app?.[key] || key;
};

const isRTL = props.locale === 'ar';

const form = useForm({
    document_type: props.documentType,
    document_id: props.documentId,
    signer_name: '',
    signature_data: '',
    metadata: {},
});

const signatureCanvas = ref(null);
const isDrawing = ref(false);
const hasSignature = ref(false);

let ctx = null;

onMounted(() => {
    if (signatureCanvas.value) {
        ctx = signatureCanvas.value.getContext('2d');
        ctx.strokeStyle = '#000';
        ctx.lineWidth = 2;
        ctx.lineCap = 'round';
        ctx.lineJoin = 'round';
    }
});

const startDrawing = (event) => {
    isDrawing.value = true;
    const rect = signatureCanvas.value.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;
    
    ctx.beginPath();
    ctx.moveTo(x, y);
};

const draw = (event) => {
    if (!isDrawing.value) return;
    
    const rect = signatureCanvas.value.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;
    
    ctx.lineTo(x, y);
    ctx.stroke();
    hasSignature.value = true;
};

const stopDrawing = () => {
    isDrawing.value = false;
    ctx.beginPath();
};

const clearSignature = () => {
    ctx.clearRect(0, 0, signatureCanvas.value.width, signatureCanvas.value.height);
    hasSignature.value = false;
    form.signature_data = '';
};

const saveSignature = () => {
    if (!hasSignature.value) {
        alert(t('please_draw_signature'));
        return;
    }
    
    const dataURL = signatureCanvas.value.toDataURL('image/png');
    form.signature_data = dataURL;
};

const submit = () => {
    saveSignature();
    
    if (!form.signature_data) {
        alert(t('signature_required'));
        return;
    }
    
    form.post(route('signatures.store'));
};
</script>

<template>
    <Head :title="t('create_digital_signature')" />

    <div class="min-h-screen bg-gray-50" :dir="isRTL ? 'rtl' : 'ltr'">
        <!-- Fixed Navigation -->
        <nav class="fixed top-0 left-0 right-0 fixed-nav z-50">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <div class="flex items-center">
                        <Link href="/" class="text-2xl font-bold text-blue-600 hover:text-blue-700 transition-colors">
                            {{ t('site_name') }}
                        </Link>
                    </div>
                    <div class="flex items-center" :class="isRTL ? 'space-x-reverse space-x-4' : 'space-x-4'">
                        <LanguageSwitcher :current-locale="locale" />
                        <Link href="/signatures" class="nav-link text-gray-600">{{ t('my_signatures') }}</Link>
                        <Link href="/dashboard" class="nav-link text-gray-600">{{ t('dashboard') }}</Link>
                    </div>
                </div>
            </div>
        </nav>

        <div class="py-12 pt-24">
            <div class="mx-auto max-w-4xl sm:px-6 lg:px-8">
                <!-- Header -->
                <div class="mb-8">
                    <div class="flex items-center gap-4">
                        <Link 
                            :href="route('signatures.index')"
                            class="text-gray-600 hover:text-gray-900"
                        >
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="isRTL ? 'M9 5l7 7-7 7' : 'M15 19l-7-7 7-7'" />
                            </svg>
                        </Link>
                        <div>
                            <h1 class="text-3xl font-bold text-gray-900">{{ t('create_digital_signature') }}</h1>
                            <p class="text-gray-600 mt-2">{{ t('create_signature_description') }}</p>
                        </div>
                    </div>
                </div>

                <!-- Form -->
                <div class="bg-white rounded-lg shadow-sm p-8">
                    <form @submit.prevent="submit" class="space-y-6">
                        <!-- Document Information -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Document Type -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    {{ t('document_type') }} *
                                </label>
                                <select 
                                    v-model="form.document_type"
                                    class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                                >
                                    <option v-for="(label, value) in documentTypes" :key="value" :value="value">
                                        {{ label }}
                                    </option>
                                </select>
                                <div v-if="form.errors.document_type" class="mt-1 text-sm text-red-600">{{ form.errors.document_type }}</div>
                            </div>

                            <!-- Document ID -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    {{ t('document_id') }} *
                                </label>
                                <input 
                                    v-model="form.document_id"
                                    type="text" 
                                    class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                                    :placeholder="t('enter_document_id')"
                                />
                                <div v-if="form.errors.document_id" class="mt-1 text-sm text-red-600">{{ form.errors.document_id }}</div>
                            </div>
                        </div>

                        <!-- Signer Name -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                {{ t('signer_name') }} *
                            </label>
                            <input 
                                v-model="form.signer_name"
                                type="text" 
                                class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                                :placeholder="t('enter_signer_name')"
                            />
                            <div v-if="form.errors.signer_name" class="mt-1 text-sm text-red-600">{{ form.errors.signer_name }}</div>
                        </div>

                        <!-- Signature Canvas -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                {{ t('digital_signature') }} *
                            </label>
                            <div class="border-2 border-dashed border-gray-300 rounded-lg p-4">
                                <div class="text-center mb-4">
                                    <p class="text-gray-600">{{ t('draw_signature_instruction') }}</p>
                                </div>
                                
                                <!-- Canvas -->
                                <div class="bg-white border border-gray-300 rounded-lg mx-auto" style="width: 600px; height: 200px;">
                                    <canvas
                                        ref="signatureCanvas"
                                        width="600"
                                        height="200"
                                        class="w-full h-full cursor-crosshair rounded-lg"
                                        @mousedown="startDrawing"
                                        @mousemove="draw"
                                        @mouseup="stopDrawing"
                                        @mouseleave="stopDrawing"
                                    ></canvas>
                                </div>

                                <!-- Canvas Controls -->
                                <div class="flex justify-center mt-4 space-x-4">
                                    <button 
                                        type="button"
                                        @click="clearSignature"
                                        class="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors"
                                    >
                                        {{ t('clear_signature') }}
                                    </button>
                                </div>
                            </div>
                            <div v-if="form.errors.signature_data" class="mt-1 text-sm text-red-600">{{ form.errors.signature_data }}</div>
                        </div>

                        <!-- Legal Notice -->
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <div class="flex">
                                <svg class="w-5 h-5 text-blue-600 mt-0.5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                <div>
                                    <h3 class="text-sm font-medium text-blue-800">{{ t('legal_notice') }}</h3>
                                    <p class="text-sm text-blue-700 mt-1">
                                        {{ t('signature_legal_notice') }}
                                    </p>
                                </div>
                            </div>
                        </div>

                        <!-- Actions -->
                        <div class="flex items-center justify-between pt-6 border-t border-gray-200">
                            <Link 
                                :href="route('signatures.index')"
                                class="bg-gray-100 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-200 transition-colors"
                            >
                                {{ t('cancel') }}
                            </Link>
                            
                            <button 
                                type="submit"
                                :disabled="form.processing"
                                class="btn-primary bg-blue-600 text-white px-6 py-2 rounded-lg font-semibold hover:bg-blue-700 disabled:opacity-50"
                            >
                                <span v-if="form.processing">{{ t('creating_signature') }}...</span>
                                <span v-else>{{ t('create_signature') }}</span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped>
canvas {
    touch-action: none;
}
</style>

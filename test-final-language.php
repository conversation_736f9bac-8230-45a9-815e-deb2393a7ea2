<?php
// Final Language Test
// This file tests the complete language system

session_start();

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='utf-8'>";
echo "<title>اختبار نهائي للغة - Season Expo Kuwait</title>";
echo "<script src='https://cdn.tailwindcss.com'></script>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }";
echo "</style>";
echo "</head>";
echo "<body class='bg-gray-50 p-8'>";

echo "<div class='max-w-6xl mx-auto'>";
echo "<h1 class='text-3xl font-bold mb-8' style='color: #2C3E50;'>🎯 اختبار نهائي لنظام اللغة</h1>";

// Current status
$currentLang = $_SESSION['language'] ?? $_COOKIE['language'] ?? $_GET['lang'] ?? 'ar';
$langFromUrl = $_GET['lang'] ?? null;

echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>📊 الحالة الحالية</h2>";

echo "<div class='grid grid-cols-1 md:grid-cols-3 gap-4'>";

echo "<div class='bg-blue-50 border border-blue-200 rounded-lg p-4'>";
echo "<h3 class='font-semibold mb-2'>اللغة المحفوظة:</h3>";
echo "<div class='space-y-2 text-sm'>";
echo "<div>Session: " . (isset($_SESSION['language']) ? "<span class='text-green-600'>✅ " . $_SESSION['language'] . "</span>" : "<span class='text-red-600'>❌ غير محددة</span>") . "</div>";
echo "<div>Cookie: " . (isset($_COOKIE['language']) ? "<span class='text-green-600'>✅ " . $_COOKIE['language'] . "</span>" : "<span class='text-red-600'>❌ غير محددة</span>") . "</div>";
echo "<div>URL: " . ($langFromUrl ? "<span class='text-green-600'>✅ " . $langFromUrl . "</span>" : "<span class='text-gray-600'>➖ غير محددة</span>") . "</div>";
echo "</div>";
echo "</div>";

echo "<div class='bg-green-50 border border-green-200 rounded-lg p-4'>";
echo "<h3 class='font-semibold mb-2'>اللغة النشطة:</h3>";
echo "<div class='text-2xl text-center'>";
if ($currentLang === 'ar') {
    echo "<span class='text-green-600'>🇰🇼 العربية</span>";
} else {
    echo "<span class='text-blue-600'>🇺🇸 English</span>";
}
echo "</div>";
echo "</div>";

echo "<div class='bg-yellow-50 border border-yellow-200 rounded-lg p-4'>";
echo "<h3 class='font-semibold mb-2'>اتجاه الصفحة:</h3>";
echo "<div class='text-lg text-center'>";
if ($currentLang === 'ar') {
    echo "<span class='text-green-600'>➡️ RTL</span>";
} else {
    echo "<span class='text-blue-600'>⬅️ LTR</span>";
}
echo "</div>";
echo "</div>";

echo "</div>";
echo "</div>";

// Test all pages
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>🧪 اختبار جميع الصفحات</h2>";

$pages = [
    '/' => 'الصفحة الرئيسية (index.php)',
    '/homepage-fixed.php' => 'الصفحة الرئيسية المحدثة',
    '/login-simple.php' => 'صفحة التسجيل المحدثة',
    '/login-simple' => 'صفحة التسجيل Laravel'
];

echo "<div class='grid grid-cols-1 md:grid-cols-2 gap-4'>";

foreach ($pages as $url => $description) {
    echo "<div class='border border-gray-200 rounded-lg p-4'>";
    echo "<h3 class='font-semibold mb-2'>{$description}</h3>";
    echo "<div class='space-y-2'>";
    echo "<a href='{$url}?lang=ar' class='block w-full text-center px-3 py-2 text-sm border border-gray-300 rounded hover:bg-gray-50'>🇰🇼 العربية</a>";
    echo "<a href='{$url}?lang=en' class='block w-full text-center px-3 py-2 text-sm border border-gray-300 rounded hover:bg-gray-50'>🇺🇸 English</a>";
    echo "</div>";
    echo "</div>";
}

echo "</div>";
echo "</div>";

// File status
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>📁 حالة الملفات</h2>";

$files = [
    'index.php' => 'ملف الفهرس الرئيسي',
    'homepage-fixed.php' => 'الصفحة الرئيسية المحدثة',
    'login-simple.php' => 'صفحة التسجيل المحدثة',
    'switch-language.php' => 'معالج تغيير اللغة',
    'ar.php' => 'معالج اللغة العربية',
    'en.php' => 'معالج اللغة الإنجليزية',
    'resources/views/homepage.blade.php' => 'صفحة Laravel الرئيسية'
];

echo "<div class='grid grid-cols-1 md:grid-cols-2 gap-4'>";

foreach ($files as $file => $description) {
    $fullPath = __DIR__ . '/' . $file;
    echo "<div class='border border-gray-200 rounded-lg p-4'>";
    echo "<h3 class='font-semibold mb-2'>{$description}</h3>";
    echo "<p class='text-sm text-gray-600 mb-2'>/{$file}</p>";
    
    if (file_exists($fullPath)) {
        echo "<div class='text-green-600 mb-2'>✅ موجود</div>";
        
        // Check for language support
        $content = file_get_contents($fullPath);
        if (strpos($content, '$currentLang') !== false || strpos($content, 'language') !== false) {
            echo "<div class='text-green-600 text-sm'>✅ يدعم اللغات</div>";
        } else {
            echo "<div class='text-yellow-600 text-sm'>⚠️ دعم محدود للغات</div>";
        }
        
    } else {
        echo "<div class='text-red-600'>❌ مفقود</div>";
    }
    echo "</div>";
}

echo "</div>";
echo "</div>";

// Expected behavior
echo "<div class='bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>✅ السلوك المتوقع بعد الإصلاح</h2>";

echo "<div class='grid grid-cols-1 md:grid-cols-2 gap-6'>";

echo "<div class='bg-white p-4 rounded-lg border'>";
echo "<h3 class='font-semibold mb-2' style='color: #2C3E50;'>المشاكل المحلولة:</h3>";
echo "<ul class='text-sm space-y-1 list-disc list-inside ml-4'>";
echo "<li>✅ زر اللغة يظهر في جميع الصفحات</li>";
echo "<li>✅ تغيير فوري للنصوص عند تغيير اللغة</li>";
echo "<li>✅ حفظ اللغة بين الصفحات</li>";
echo "<li>✅ صفحة تسجيل موحدة ومحدثة</li>";
echo "<li>✅ حل مشكلة /en و /ar</li>";
echo "<li>✅ توجيه تلقائي للصفحة المحدثة</li>";
echo "</ul>";
echo "</div>";

echo "<div class='bg-white p-4 rounded-lg border'>";
echo "<h3 class='font-semibold mb-2' style='color: #2C3E50;'>الوظائف الجديدة:</h3>";
echo "<ul class='text-sm space-y-1 list-disc list-inside ml-4'>";
echo "<li>🔄 تغيير اتجاه الصفحة (RTL/LTR)</li>";
echo "<li>🌐 ترجمة شاملة للنصوص</li>";
echo "<li>💾 حفظ دائم للإعدادات</li>";
echo "<li>🔗 روابط تحمل اللغة</li>";
echo "<li>📱 متوافق مع الجوال</li>";
echo "<li>⚡ أداء محسن</li>";
echo "</ul>";
echo "</div>";

echo "</div>";
echo "</div>";

// Quick test
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>🚀 اختبار سريع</h2>";

echo "<div class='text-center'>";
echo "<p class='mb-4'>اختبر النظام الجديد:</p>";

echo "<div class='flex items-center justify-center space-x-reverse space-x-4 mb-6'>";
echo "<a href='/switch-language.php?lang=ar' class='inline-flex items-center px-6 py-3 border-2 rounded-lg font-semibold transition-colors' style='border-color: #2C3E50; color: #2C3E50;' onmouseover='this.style.background=\"#2C3E50\"; this.style.color=\"white\"' onmouseout='this.style.background=\"transparent\"; this.style.color=\"#2C3E50\"'>";
echo "<span class='ml-2'>🇰🇼</span>";
echo "تغيير للعربية";
echo "</a>";

echo "<a href='/switch-language.php?lang=en' class='inline-flex items-center px-6 py-3 border-2 rounded-lg font-semibold transition-colors' style='border-color: #2C3E50; color: #2C3E50;' onmouseover='this.style.background=\"#2C3E50\"; this.style.color=\"white\"' onmouseout='this.style.background=\"transparent\"; this.style.color=\"#2C3E50\"'>";
echo "<span class='ml-2'>🇺🇸</span>";
echo "Switch to English";
echo "</a>";
echo "</div>";

if ($langFromUrl) {
    echo "<div class='bg-green-50 border border-green-200 rounded-lg p-4 mb-4'>";
    echo "<h3 class='font-semibold mb-2'>✅ نتيجة الاختبار:</h3>";
    echo "<p>تم تغيير اللغة إلى: <strong>" . ($langFromUrl === 'ar' ? 'العربية 🇰🇼' : 'English 🇺🇸') . "</strong></p>";
    echo "<p class='text-sm text-gray-600 mt-2'>النظام يعمل بشكل صحيح!</p>";
    echo "</div>";
}

echo "</div>";
echo "</div>";

// Navigation
echo "<div class='text-center space-x-reverse space-x-4'>";
echo "<a href='/' class='text-white px-6 py-3 rounded-lg hover:opacity-80' style='background: #2C3E50;'>🏠 الصفحة الرئيسية</a>";
echo "<a href='/login-simple.php' class='bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700'>🔐 صفحة التسجيل</a>";
echo "<a href='/admin-slider-management.php' class='bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700'>🖼️ إدارة السلايد</a>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>

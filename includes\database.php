<?php
// Database Configuration for Season Expo Kuwait
// This file contains database connection settings and helper functions

/**
 * Get database connection
 * @return PDO Database connection object
 */
function getDatabaseConnection() {
    // Database configuration (from Laravel .env file - corrected)
    $host = '127.0.0.1';
    $database = 'u404269408_seasonexpodb';
    $username = 'u404269408_expo';
    $password = '43674367@Kwi';

    try {
        $dsn = "mysql:host={$host};dbname={$database};charset=utf8mb4";
        $options = [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
        ];

        $pdo = new PDO($dsn, $username, $password, $options);
        return $pdo;

    } catch (PDOException $e) {
        throw new Exception("Database connection failed: " . $e->getMessage());
    }
}

/**
 * Get database information and statistics
 * @return array Database statistics
 */
function getDatabaseInfo() {
    try {
        $pdo = getDatabaseConnection();

        // Get exhibitions count
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM exhibitions");
        $exhibitions_count = $stmt->fetch()['count'];

        // Get published exhibitions count
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM exhibitions WHERE status = 'published'");
        $published_exhibitions_count = $stmt->fetch()['count'];

        // Get booths count
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM booths");
        $booths_count = $stmt->fetch()['count'];

        // Get available booths count
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM booths WHERE status = 'available'");
        $available_booths_count = $stmt->fetch()['count'];

        return [
            'exhibitions_count' => $exhibitions_count,
            'published_exhibitions_count' => $published_exhibitions_count,
            'booths_count' => $booths_count,
            'available_booths_count' => $available_booths_count,
            'connection_status' => 'connected'
        ];

    } catch (Exception $e) {
        return [
            'exhibitions_count' => 0,
            'published_exhibitions_count' => 0,
            'booths_count' => 0,
            'available_booths_count' => 0,
            'connection_status' => 'error',
            'error_message' => $e->getMessage()
        ];
    }
}

/**
 * Test database connection
 * @return bool True if connection successful
 */
function testDatabaseConnection() {
    try {
        $pdo = getDatabaseConnection();
        $stmt = $pdo->query("SELECT 1");
        return true;
    } catch (Exception $e) {
        return false;
    }
}
?>

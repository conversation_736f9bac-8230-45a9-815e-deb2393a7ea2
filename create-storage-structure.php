<?php
echo "<h1>Create Laravel Storage Structure</h1>";

$basePath = dirname(__DIR__);

// Required storage directories
$storageDirectories = [
    'storage',
    'storage/app',
    'storage/app/public',
    'storage/framework',
    'storage/framework/cache',
    'storage/framework/cache/data',
    'storage/framework/sessions',
    'storage/framework/testing',
    'storage/framework/views',
    'storage/logs',
];

echo "<h2>Creating Storage Directory Structure:</h2>";

$created = 0;
$existed = 0;

foreach ($storageDirectories as $dir) {
    $fullPath = $basePath . '/' . $dir;
    
    if (!is_dir($fullPath)) {
        if (mkdir($fullPath, 0755, true)) {
            echo "✅ Created: {$dir}<br>";
            $created++;
        } else {
            echo "❌ Failed to create: {$dir}<br>";
        }
    } else {
        echo "✅ Exists: {$dir}<br>";
        $existed++;
    }
}

echo "<br><strong>Summary:</strong><br>";
echo "Created: {$created} directories<br>";
echo "Already existed: {$existed} directories<br>";

// Create .gitkeep files
echo "<h2>Creating .gitkeep Files:</h2>";

$gitkeepDirs = [
    'storage/app/public',
    'storage/framework/cache/data',
    'storage/framework/sessions',
    'storage/framework/testing',
    'storage/framework/views',
    'storage/logs',
];

foreach ($gitkeepDirs as $dir) {
    $gitkeepPath = $basePath . '/' . $dir . '/.gitkeep';
    if (!file_exists($gitkeepPath)) {
        if (file_put_contents($gitkeepPath, '# Keep this directory') !== false) {
            echo "✅ Created .gitkeep in {$dir}<br>";
        } else {
            echo "❌ Failed to create .gitkeep in {$dir}<br>";
        }
    } else {
        echo "✅ .gitkeep exists in {$dir}<br>";
    }
}

// Now try to create the symlink
echo "<h2>Creating Storage Symlink:</h2>";

$publicPath = __DIR__;
$storagePath = $basePath . '/storage/app/public';
$linkPath = $publicPath . '/storage';

// Remove existing if present
if (file_exists($linkPath)) {
    if (is_link($linkPath)) {
        unlink($linkPath);
        echo "Removed existing symlink<br>";
    } elseif (is_dir($linkPath)) {
        rmdir($linkPath);
        echo "Removed existing directory<br>";
    } else {
        unlink($linkPath);
        echo "Removed existing file<br>";
    }
}

// Try to create symlink
if (symlink($storagePath, $linkPath)) {
    echo "✅ SUCCESS: Storage symlink created!<br>";
    echo "Link: {$linkPath}<br>";
    echo "Target: {$storagePath}<br>";
} else {
    echo "❌ Symlink creation failed, creating directory copy...<br>";
    
    // Create directory and copy structure
    if (!is_dir($linkPath)) {
        mkdir($linkPath, 0755, true);
        echo "✅ Created storage directory in public_html<br>";
    }
    
    // Create basic subdirectories for uploads
    $publicStorageDirs = ['images', 'documents', 'media'];
    foreach ($publicStorageDirs as $subdir) {
        $subdirPath = $linkPath . '/' . $subdir;
        if (!is_dir($subdirPath)) {
            mkdir($subdirPath, 0755, true);
            echo "✅ Created {$subdir} directory<br>";
        }
    }
}

echo "<h2>Verification:</h2>";

if (file_exists($linkPath)) {
    if (is_link($linkPath)) {
        echo "✅ Storage symlink working<br>";
    } elseif (is_dir($linkPath)) {
        echo "✅ Storage directory working<br>";
    }
    echo "<br><strong>Storage is now ready!</strong><br>";
} else {
    echo "❌ Storage setup failed<br>";
}

echo "<h2>Next Steps:</h2>";
echo "<ol>";
echo "<li><a href='test-application.php'>Test Season Expo Application</a></li>";
echo "<li><a href='/'>Visit Homepage</a></li>";
echo "<li>Check if images and uploads work</li>";
echo "</ol>";
?>

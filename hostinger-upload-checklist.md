# ✅ قائمة مرجعية لرفع التحديثات إلى Hostinger

## 📋 قبل الرفع:
- [ ] تأكد من عمل الوظيفة محلياً
- [ ] احتفظ بنسخة احتياطية من الملفات الحالية على السيرفر
- [ ] تأكد من بيانات الاتصال بـ Hostinger

## 📁 الملفات المطلوب رفعها:

### الملفات الأساسية (ضرورية):
- [ ] `resources/views/auth/register-simple.blade.php`
- [ ] `resources/views/auth/forgot-password.blade.php`  
- [ ] `routes/web.php`

### ملفات الاختبار (اختيارية):
- [ ] `test-forgot-password.php`
- [ ] `test-login-page.php`
- [ ] `test-email-functionality.php`

## 🚀 خطوات الرفع:

### 1. الدخول إلى Hostinger:
- [ ] ادخل إلى لوحة تحكم Hostinger
- [ ] اذهب إلى File Manager
- [ ] انتقل إلى مجلد موقعك

### 2. رفع الملفات:
- [ ] ارفع `register-simple.blade.php` إلى `resources/views/auth/`
- [ ] ارفع `forgot-password.blade.php` إلى `resources/views/auth/`
- [ ] ارفع `web.php` إلى `routes/` (احذف النسخة القديمة أولاً)
- [ ] ارفع ملفات الاختبار إلى الجذر الرئيسي

### 3. بعد الرفع:
- [ ] اذهب إلى `yourdomain.com/clear-all-caches.php`
- [ ] أو استخدم SSH: `php artisan cache:clear`

## 🧪 اختبار الوظيفة:

### 1. اختبار صفحة تسجيل الدخول:
- [ ] اذهب إلى `yourdomain.com/login-simple`
- [ ] تأكد من وجود رابط "🔑 نسيت كلمة المرور؟" في مكانين
- [ ] انقر على الرابط للتأكد من أنه يعمل

### 2. اختبار صفحة استعادة كلمة المرور:
- [ ] اذهب إلى `yourdomain.com/reset-password-form`
- [ ] أدخل بريد إلكتروني صحيح
- [ ] اضغط على "إرسال رابط إعادة التعيين"
- [ ] تأكد من ظهور رسالة النجاح

### 3. اختبار البريد الإلكتروني:
- [ ] تحقق من إعدادات MAIL في `.env`
- [ ] اختبر إرسال بريد إلكتروني
- [ ] تحقق من ملف السجلات إذا كان `MAIL_MAILER=log`

## ⚙️ إعداد البريد الإلكتروني:

### إعدادات Hostinger SMTP:
```env
MAIL_MAILER=smtp
MAIL_HOST=smtp.hostinger.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-email-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="Season Expo"
```

### أو استخدام Gmail:
```env
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="Season Expo"
```

## 🔍 التحقق النهائي:

### وظائف يجب أن تعمل:
- [ ] صفحة تسجيل الدخول تظهر رابط "نسيت كلمة المرور"
- [ ] رابط "نسيت كلمة المرور" يؤدي إلى الصفحة الصحيحة
- [ ] صفحة استعادة كلمة المرور تقبل البريد الإلكتروني
- [ ] رسالة النجاح تظهر بعد الإرسال
- [ ] البريد الإلكتروني يُرسل (أو يُحفظ في السجلات)
- [ ] رابط إعادة التعيين في البريد يعمل
- [ ] صفحة إعادة تعيين كلمة المرور تعمل
- [ ] تحديث كلمة المرور يعمل بنجاح

## 🆘 في حالة المشاكل:

### مشاكل شائعة وحلولها:
- **الصفحة لا تظهر**: تأكد من رفع الملفات في المواقع الصحيحة
- **خطأ 500**: تحقق من ملف السجلات في `storage/logs/`
- **البريد لا يُرسل**: تحقق من إعدادات SMTP في `.env`
- **الروابط لا تعمل**: امسح الكاش باستخدام `/clear-all-caches.php`

### ملفات مهمة للفحص:
- [ ] `storage/logs/laravel.log` - للأخطاء
- [ ] `.env` - للإعدادات
- [ ] `database/database.sqlite` - لقاعدة البيانات

## 📞 الدعم:
إذا واجهت مشاكل، تحقق من:
1. ملفات السجلات
2. إعدادات قاعدة البيانات
3. صلاحيات الملفات
4. إعدادات البريد الإلكتروني

---
**تاريخ آخر تحديث**: اليوم
**الإصدار**: 1.0 - إضافة وظيفة نسيت كلمة المرور

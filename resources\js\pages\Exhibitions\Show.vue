<script setup>
import { Head, <PERSON> } from '@inertiajs/vue3';
import LanguageSwitcher from '@/components/LanguageSwitcher.vue';

const props = defineProps({
  exhibition: Object,
  availableBooths: Object,
  boothStats: Object,
  relatedExhibitions: Array,
  locale: String,
  translations: Object,
});

const t = (key) => {
  return props.translations?.app?.[key] || key;
};

const isRTL = props.locale === 'ar';

const formatDate = (dateString) => {
  const locale = props.locale === 'ar' ? 'ar-SA' : 'en-US';
  return new Date(dateString).toLocaleDateString(locale, {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};

const formatPrice = (price, currency) => {
  const locale = props.locale === 'ar' ? 'ar-SA' : 'en-US';

  // Always use KWD for display
  if (currency === 'USD' || currency === 'KWD') {
    // Format as KWD with 3 decimal places
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency: 'KWD',
      minimumFractionDigits: 3,
      maximumFractionDigits: 3
    }).format(price);
  }

  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 3,
    maximumFractionDigits: 3
  }).format(price);
};

// Image URL helper
const getImageUrl = (imagePath) => {
  if (!imagePath) return null;

  // If it's already a full URL, return as is
  if (imagePath.startsWith('http')) {
    return imagePath;
  }

  // If it starts with storage/, add the asset URL
  if (imagePath.startsWith('storage/')) {
    return `/storage/${imagePath.replace('storage/', '')}`;
  }

  // If it's just a filename, assume it's in storage/exhibitions
  if (!imagePath.includes('/')) {
    return `/storage/exhibitions/${imagePath}`;
  }

  // Default: prepend /storage/
  return `/storage/${imagePath}`;
};

// Handle image loading errors
const handleImageError = (event) => {
  // Replace broken image with placeholder
  const img = event.target;
  const placeholder = img.parentElement.querySelector('.image-placeholder');

  if (placeholder) {
    img.style.display = 'none';
    placeholder.style.display = 'flex';
  } else {
    // Create placeholder if it doesn't exist
    const placeholderDiv = document.createElement('div');
    placeholderDiv.className = 'w-full h-full flex items-center justify-center bg-gradient-to-br from-blue-100 to-purple-100 image-placeholder';
    placeholderDiv.innerHTML = `
      <div class="text-center">
        <div class="text-6xl mb-4">🎪</div>
        <div class="text-lg text-gray-600">Exhibition Image</div>
        <div class="text-sm text-gray-500">Image not available</div>
      </div>
    `;
    img.style.display = 'none';
    img.parentElement.appendChild(placeholderDiv);
  }
};

const getSizeColor = (size) => {
  const colors = {
    small: 'bg-green-100 text-green-800',
    medium: 'bg-blue-100 text-blue-800',
    large: 'bg-purple-100 text-purple-800',
    premium: 'bg-yellow-100 text-yellow-800'
  };
  return colors[size] || 'bg-gray-100 text-gray-800';
};

const getStatusColor = (status) => {
  const colors = {
    available: 'bg-green-100 text-green-800',
    booked: 'bg-red-100 text-red-800',
    reserved: 'bg-yellow-100 text-yellow-800',
    maintenance: 'bg-gray-100 text-gray-800'
  };
  return colors[status] || 'bg-gray-100 text-gray-800';
};
</script>

<template>
  <Head :title="`${exhibition.title} - ${t('site_name')}`" />

  <div class="min-h-screen bg-gray-50" :dir="isRTL ? 'rtl' : 'ltr'">
    <!-- Fixed Navigation -->
    <nav class="fixed top-0 left-0 right-0 fixed-nav z-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex items-center">
            <Link href="/" class="text-2xl font-bold text-blue-600 hover:text-blue-700 transition-colors">
              {{ t('site_name') }}
            </Link>
          </div>
          <div class="flex items-center" :class="isRTL ? 'space-x-reverse space-x-4' : 'space-x-4'">
            <LanguageSwitcher :current-locale="locale" />
            <Link href="/exhibitions" class="nav-link text-blue-600 font-semibold">{{ t('exhibitions') }}</Link>
            <Link
              v-if="$page.props.auth?.user"
              :href="route('dashboard')"
              class="nav-link text-gray-600"
            >
              {{ t('dashboard') }}
            </Link>
            <template v-else>
              <Link
                :href="route('login')"
                class="nav-link text-gray-600"
              >
                {{ t('sign_in') }}
              </Link>
              <Link
                :href="route('register')"
                class="btn-primary bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
              >
                {{ t('get_started') }}
              </Link>
            </template>
          </div>
        </div>
      </div>
    </nav>

    <!-- Main Content -->
    <div class="pt-16">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Breadcrumb -->
        <nav class="mb-8">
          <ol class="flex items-center space-x-2 text-sm text-gray-500">
            <li><Link href="/" class="hover:text-blue-600">{{ t('home') }}</Link></li>
            <li>/</li>
            <li><Link :href="route('exhibitions.index')" class="hover:text-blue-600">{{ t('exhibitions') }}</Link></li>
            <li>/</li>
            <li class="text-gray-900">{{ exhibition.title }}</li>
          </ol>
        </nav>

      <!-- Exhibition Header -->
      <div class="bg-white rounded-lg shadow-md overflow-hidden mb-8">
        <div class="h-64 bg-gray-200 relative">
          <img
            v-if="exhibition.featured_image"
            :src="getImageUrl(exhibition.featured_image)"
            :alt="exhibition.title"
            class="w-full h-full object-cover"
            @error="handleImageError"
          />
          <!-- Placeholder when no image -->
          <div
            v-else
            class="w-full h-full flex items-center justify-center bg-gradient-to-br from-blue-100 to-purple-100"
          >
            <div class="text-center">
              <div class="text-6xl mb-4">🎪</div>
              <div class="text-lg text-gray-600">{{ exhibition.title }}</div>
              <div class="text-sm text-gray-500">Exhibition Image</div>
            </div>
          </div>
          <div class="absolute top-4 left-4">
            <span
              class="px-3 py-1 rounded-full text-sm font-medium text-white"
              :style="{ backgroundColor: exhibition.category.color }"
            >
              {{ exhibition.category.name }}
            </span>
          </div>
        </div>

        <div class="p-8">
          <div class="flex flex-col lg:flex-row lg:items-start lg:justify-between">
            <div class="flex-1">
              <h1 class="text-3xl font-bold text-gray-900 mb-4">{{ exhibition.title }}</h1>
              <p class="text-lg text-gray-600 mb-6">{{ exhibition.short_description }}</p>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div class="flex items-center text-gray-600">
                  <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd" />
                  </svg>
                  <div>
                    <div class="font-medium">{{ exhibition.venue_name }}</div>
                    <div class="text-sm">{{ exhibition.city }}, {{ exhibition.country }}</div>
                  </div>
                </div>

                <div class="flex items-center text-gray-600">
                  <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd" />
                  </svg>
                  <div>
                    <div class="font-medium">{{ formatDate(exhibition.start_date) }}</div>
                    <div class="text-sm">to {{ formatDate(exhibition.end_date) }}</div>
                  </div>
                </div>

                <div class="flex items-center text-gray-600">
                  <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z" />
                  </svg>
                  <div>
                    <div class="font-medium">{{ exhibition.organizer.name }}</div>
                    <div class="text-sm">{{ exhibition.organizer.company }}</div>
                  </div>
                </div>

                <div class="flex items-center text-gray-600">
                  <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z" />
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clip-rule="evenodd" />
                  </svg>
                  <div>
                    <div class="font-medium">From {{ formatPrice(exhibition.booth_price_from, exhibition.currency) }}</div>
                    <div class="text-sm">per booth</div>
                  </div>
                </div>
              </div>
            </div>

            <div class="lg:ml-8 lg:w-80">
              <div class="bg-gray-50 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">{{ t('booth_statistics') }}</h3>
                <div class="grid grid-cols-2 gap-4 mb-4">
                  <div class="text-center">
                    <div class="text-2xl font-bold text-blue-600">{{ boothStats.total }}</div>
                    <div class="text-sm text-gray-600">{{ t('total_booths') }}</div>
                  </div>
                  <div class="text-center">
                    <div class="text-2xl font-bold text-green-600">{{ boothStats.available }}</div>
                    <div class="text-sm text-gray-600">{{ t('available') }}</div>
                  </div>
                </div>
                <Link
                  :href="route('booths.index', exhibition.slug)"
                  class="w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-center block"
                >
                  {{ t('browse_booths') }}
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Exhibition Description -->
      <div class="bg-white rounded-lg shadow-md p-8 mb-8">
        <h2 class="text-2xl font-bold text-gray-900 mb-4">{{ t('about_this_exhibition') }}</h2>
        <div class="prose max-w-none text-gray-600">
          {{ exhibition.description }}
        </div>
      </div>

      <!-- Available Booths by Size -->
      <div class="bg-white rounded-lg shadow-md p-8 mb-8" v-if="Object.keys(availableBooths).length > 0">
        <h2 class="text-2xl font-bold text-gray-900 mb-6">Available Booths</h2>

        <div class="space-y-6">
          <div v-for="(booths, size) in availableBooths" :key="size">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg font-semibold text-gray-900 capitalize">{{ size }} Booths</h3>
              <span class="text-sm text-gray-500">{{ booths.length }} available</span>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div
                v-for="booth in booths.slice(0, 6)"
                :key="booth.id"
                class="border border-gray-200 rounded-lg p-4 hover:border-blue-300 transition-colors"
              >
                <div class="flex items-center justify-between mb-2">
                  <span class="font-medium text-gray-900">{{ booth.booth_number }}</span>
                  <div class="flex space-x-2">
                    <span :class="['px-2 py-1 rounded-full text-xs font-medium', getSizeColor(booth.size)]">
                      {{ booth.size }}
                    </span>
                    <span v-if="booth.is_corner" class="px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                      Corner
                    </span>
                  </div>
                </div>

                <div class="text-sm text-gray-600 mb-2">
                  {{ booth.width }}m × {{ booth.height }}m ({{ booth.area }}m²)
                </div>

                <div class="text-sm text-gray-600 mb-3">
                  {{ booth.location }}
                </div>

                <div class="flex items-center justify-between">
                  <span class="text-lg font-bold text-blue-600">
                    {{ formatPrice(booth.price, exhibition.currency) }}
                  </span>
                  <Link
                    :href="route('booths.show', [exhibition.slug, booth.id])"
                    class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700 transition-colors"
                  >
                    View Details
                  </Link>
                </div>
              </div>
            </div>

            <div v-if="booths.length > 6" class="mt-4 text-center">
              <Link
                :href="route('booths.index', { exhibition: exhibition.slug, size: size })"
                class="text-blue-600 hover:text-blue-800 font-medium"
              >
                View all {{ booths.length }} {{ size }} booths →
              </Link>
            </div>
          </div>
        </div>
      </div>

      <!-- Related Exhibitions -->
      <div v-if="relatedExhibitions.length > 0" class="bg-white rounded-lg shadow-md p-8">
        <h2 class="text-2xl font-bold text-gray-900 mb-6">Related Exhibitions</h2>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Link
            v-for="related in relatedExhibitions"
            :key="related.id"
            :href="route('exhibitions.show', related.slug)"
            class="group"
          >
            <div class="bg-gray-100 h-32 rounded-lg mb-3 overflow-hidden">
              <img
                v-if="related.featured_image"
                :src="getImageUrl(related.featured_image)"
                :alt="related.title"
                class="w-full h-full object-cover group-hover:scale-105 transition-transform"
                @error="handleImageError"
              />
              <!-- Placeholder for related exhibitions -->
              <div
                v-else
                class="w-full h-full flex items-center justify-center bg-gradient-to-br from-gray-100 to-gray-200"
              >
                <div class="text-center">
                  <div class="text-2xl mb-1">🎪</div>
                  <div class="text-xs text-gray-500">Exhibition</div>
                </div>
              </div>
            </div>
            <h3 class="font-semibold text-gray-900 group-hover:text-blue-600 transition-colors mb-1">
              {{ related.title }}
            </h3>
            <p class="text-sm text-gray-600">{{ related.city }}, {{ related.country }}</p>
            <p class="text-sm text-gray-500">{{ formatDate(related.start_date) }}</p>
          </Link>
        </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* Fix spacing issues */
.min-h-screen {
  margin: 0 !important;
  padding: 0 !important;
}

.pt-16 {
  padding-top: 4rem !important;
  margin-top: 0 !important;
}

/* Fixed navigation styles */
.fixed-nav {
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid #e5e7eb;
}

.nav-link {
  transition: color 0.2s ease;
}

.nav-link:hover {
  color: #2563eb;
}

.btn-primary {
  transition: all 0.2s ease;
}

/* Remove excessive margins */
.max-w-7xl {
  margin-top: 0 !important;
}

/* Ensure content starts immediately after nav */
.py-12 {
  padding-top: 0 !important;
  margin-top: 0 !important;
}

/* RTL Support */
[dir="rtl"] .space-x-reverse > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
}
</style>

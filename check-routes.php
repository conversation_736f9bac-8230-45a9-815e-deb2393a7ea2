<?php
echo "<h1>Route Configuration Check</h1>";

try {
    require dirname(__DIR__) . '/vendor/autoload.php';
    $app = require_once dirname(__DIR__) . '/bootstrap/app.php';
    
    echo "<h2>Routes Analysis:</h2>";
    
    // Get all routes
    $routes = \Illuminate\Support\Facades\Route::getRoutes();
    echo "✅ Total routes loaded: " . count($routes) . "<br><br>";
    
    // Look for home route
    $homeRoute = null;
    $allRoutes = [];
    
    foreach ($routes as $route) {
        $uri = $route->uri();
        $methods = implode('|', $route->methods());
        $action = $route->getActionName();
        
        $allRoutes[] = [
            'uri' => $uri,
            'methods' => $methods,
            'action' => $action
        ];
        
        if ($uri === '/') {
            $homeRoute = $route;
        }
    }
    
    echo "<h2>Home Route (/) Status:</h2>";
    if ($homeRoute) {
        echo "<div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px;'>";
        echo "✅ Home route found<br>";
        echo "Methods: " . implode(', ', $homeRoute->methods()) . "<br>";
        echo "Action: " . $homeRoute->getActionName() . "<br>";
        echo "Name: " . ($homeRoute->getName() ?? 'No name') . "<br>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px;'>";
        echo "❌ No home route (/) found!<br>";
        echo "This is likely the cause of your 500 error.<br>";
        echo "</div>";
    }
    
    echo "<h2>All Available Routes:</h2>";
    echo "<div style='max-height: 400px; overflow-y: auto; background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
    echo "<table style='width: 100%; border-collapse: collapse;'>";
    echo "<tr style='background: #e9ecef;'>";
    echo "<th style='padding: 8px; border: 1px solid #dee2e6;'>Methods</th>";
    echo "<th style='padding: 8px; border: 1px solid #dee2e6;'>URI</th>";
    echo "<th style='padding: 8px; border: 1px solid #dee2e6;'>Action</th>";
    echo "</tr>";
    
    foreach ($allRoutes as $route) {
        echo "<tr>";
        echo "<td style='padding: 8px; border: 1px solid #dee2e6; font-family: monospace;'>" . htmlspecialchars($route['methods']) . "</td>";
        echo "<td style='padding: 8px; border: 1px solid #dee2e6; font-family: monospace;'>" . htmlspecialchars($route['uri']) . "</td>";
        echo "<td style='padding: 8px; border: 1px solid #dee2e6; font-family: monospace; font-size: 12px;'>" . htmlspecialchars($route['action']) . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    echo "</div>";
    
    // Check for Season Expo specific routes
    echo "<h2>Season Expo Routes:</h2>";
    $seasonExpoRoutes = array_filter($allRoutes, function($route) {
        return strpos($route['action'], 'HomeController') !== false ||
               strpos($route['action'], 'ExhibitionController') !== false ||
               strpos($route['uri'], 'exhibition') !== false ||
               strpos($route['uri'], 'dashboard') !== false;
    });
    
    if (!empty($seasonExpoRoutes)) {
        echo "<div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px;'>";
        echo "✅ Found " . count($seasonExpoRoutes) . " Season Expo routes:<br>";
        foreach ($seasonExpoRoutes as $route) {
            echo "- " . $route['methods'] . " " . $route['uri'] . " → " . $route['action'] . "<br>";
        }
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px;'>";
        echo "❌ No Season Expo routes found!<br>";
        echo "Your routes/web.php might not be loading Season Expo routes.<br>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px;'>";
    echo "<h3>❌ Error Loading Routes:</h3>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "<h2>Next Steps:</h2>";
echo "<ul>";
echo "<li><a href='get-full-error.php'>Get Full Error Details</a></li>";
echo "<li><a href='check-laravel-logs.php'>Check Laravel Logs</a></li>";
echo "<li><a href='show-routes-file.php'>Show routes/web.php Content</a></li>";
echo "</ul>";
?>

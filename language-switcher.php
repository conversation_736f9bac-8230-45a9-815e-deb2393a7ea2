<?php
// Language Switcher Handler
// This file handles language switching for the website

session_start();

// Get the requested language from URL
$requestedLang = $_GET['lang'] ?? $_POST['lang'] ?? 'ar';

// Validate language
$supportedLanguages = ['ar', 'en'];
if (!in_array($requestedLang, $supportedLanguages)) {
    $requestedLang = 'ar'; // Default to Arabic
}

// Store language preference in session
$_SESSION['language'] = $requestedLang;

// Store in cookie for persistence (30 days)
setcookie('language', $requestedLang, time() + (30 * 24 * 60 * 60), '/');

// Get the referrer URL to redirect back
$referrer = $_SERVER['HTTP_REFERER'] ?? '/';

// Clean the referrer URL to remove any existing language parameters
$referrer = preg_replace('/[?&]lang=[^&]*/', '', $referrer);

// Add language parameter to the URL
$separator = strpos($referrer, '?') !== false ? '&' : '?';
$redirectUrl = $referrer . $separator . 'lang=' . $requestedLang;

// Redirect back to the previous page with language parameter
header('Location: ' . $redirectUrl);
exit;
?>

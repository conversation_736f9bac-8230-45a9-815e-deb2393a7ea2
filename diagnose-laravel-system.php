<?php
// Diagnose Laravel System
// Check why Laravel routes are not working

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='utf-8'>";
echo "<title>تشخيص نظام Laravel</title>";
echo "<script src='https://cdn.tailwindcss.com'></script>";
echo "</head>";
echo "<body class='bg-gray-50 p-8'>";

echo "<div class='max-w-4xl mx-auto'>";
echo "<h1 class='text-2xl font-bold mb-6'>🔍 تشخيص نظام Laravel</h1>";

// Check Laravel files
echo "<div class='bg-white rounded-lg shadow p-6 mb-6'>";
echo "<h2 class='text-lg font-bold mb-4'>📁 فحص ملفات Laravel</h2>";

$laravelFiles = [
    'index.php' => 'Laravel Entry Point',
    'artisan' => 'Laravel Artisan Command',
    'composer.json' => 'Composer Dependencies',
    'app/Http/Controllers/BookingController.php' => 'Booking Controller',
    'app/Http/Controllers/PaymentController.php' => 'Payment Controller',
    'routes/web.php' => 'Web Routes',
    'config/app.php' => 'App Configuration',
    'bootstrap/app.php' => 'Bootstrap File',
    '.env' => 'Environment Configuration'
];

echo "<div class='space-y-2'>";

foreach ($laravelFiles as $file => $description) {
    $exists = file_exists($file) ? '✅' : '❌';
    $color = file_exists($file) ? 'text-green-600' : 'text-red-600';
    
    echo "<div class='flex items-center justify-between p-2 border border-gray-200 rounded'>";
    echo "<div>";
    echo "<span class='font-medium'>{$description}</span>";
    echo "<br><code class='text-xs text-gray-500'>{$file}</code>";
    echo "</div>";
    echo "<span class='{$color} text-lg'>{$exists}</span>";
    echo "</div>";
}

echo "</div>";
echo "</div>";

// Check if Laravel is working
echo "<div class='bg-white rounded-lg shadow p-6 mb-6'>";
echo "<h2 class='text-lg font-bold mb-4'>🏗️ اختبار Laravel</h2>";

// Test if we can access Laravel
try {
    // Try to include Laravel bootstrap
    if (file_exists('vendor/autoload.php')) {
        require_once 'vendor/autoload.php';
        echo "<div class='bg-green-50 border border-green-200 rounded p-4 mb-4'>";
        echo "<p class='text-green-700'>✅ تم تحميل Composer autoload بنجاح</p>";
        echo "</div>";
        
        // Try to bootstrap Laravel
        if (file_exists('bootstrap/app.php')) {
            try {
                $app = require_once 'bootstrap/app.php';
                echo "<div class='bg-green-50 border border-green-200 rounded p-4 mb-4'>";
                echo "<p class='text-green-700'>✅ تم تحميل Laravel bootstrap بنجاح</p>";
                echo "</div>";
                
                // Check if we can access models
                try {
                    $bookingsCount = \App\Models\Booking::count();
                    $boothsCount = \App\Models\Booth::count();
                    
                    echo "<div class='bg-blue-50 border border-blue-200 rounded p-4 mb-4'>";
                    echo "<p class='text-blue-700'>✅ Laravel Models تعمل:</p>";
                    echo "<p class='text-blue-600'>- عدد الحجوزات: {$bookingsCount}</p>";
                    echo "<p class='text-blue-600'>- عدد الأجنحة: {$boothsCount}</p>";
                    echo "</div>";
                    
                } catch (Exception $e) {
                    echo "<div class='bg-yellow-50 border border-yellow-200 rounded p-4 mb-4'>";
                    echo "<p class='text-yellow-700'>⚠️ خطأ في Laravel Models: " . htmlspecialchars($e->getMessage()) . "</p>";
                    echo "</div>";
                }
                
            } catch (Exception $e) {
                echo "<div class='bg-red-50 border border-red-200 rounded p-4 mb-4'>";
                echo "<p class='text-red-700'>❌ خطأ في Laravel bootstrap: " . htmlspecialchars($e->getMessage()) . "</p>";
                echo "</div>";
            }
        } else {
            echo "<div class='bg-red-50 border border-red-200 rounded p-4 mb-4'>";
            echo "<p class='text-red-700'>❌ ملف bootstrap/app.php غير موجود</p>";
            echo "</div>";
        }
        
    } else {
        echo "<div class='bg-red-50 border border-red-200 rounded p-4 mb-4'>";
        echo "<p class='text-red-700'>❌ ملف vendor/autoload.php غير موجود - Laravel غير مثبت</p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='bg-red-50 border border-red-200 rounded p-4 mb-4'>";
    echo "<p class='text-red-700'>❌ خطأ عام في Laravel: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "</div>";

// Check .htaccess
echo "<div class='bg-white rounded-lg shadow p-6 mb-6'>";
echo "<h2 class='text-lg font-bold mb-4'>⚙️ فحص .htaccess</h2>";

if (file_exists('.htaccess')) {
    $htaccessContent = file_get_contents('.htaccess');
    
    // Check for Laravel rules
    $hasLaravelRules = strpos($htaccessContent, 'index.php') !== false;
    $hasBookingRules = strpos($htaccessContent, 'bookings') !== false;
    
    echo "<div class='space-y-3'>";
    
    echo "<div class='flex items-center justify-between p-3 border border-gray-200 rounded'>";
    echo "<span>يحتوي على قواعد Laravel (index.php)</span>";
    echo "<span class='" . ($hasLaravelRules ? 'text-green-600' : 'text-red-600') . "'>" . ($hasLaravelRules ? '✅' : '❌') . "</span>";
    echo "</div>";
    
    echo "<div class='flex items-center justify-between p-3 border border-gray-200 rounded'>";
    echo "<span>يحتوي على قواعد الحجز (bookings)</span>";
    echo "<span class='" . ($hasBookingRules ? 'text-green-600' : 'text-red-600') . "'>" . ($hasBookingRules ? '✅' : '❌') . "</span>";
    echo "</div>";
    
    // Show first 20 lines of .htaccess
    $lines = explode("\n", $htaccessContent);
    $firstLines = array_slice($lines, 0, 20);
    
    echo "<div class='bg-gray-50 p-4 rounded'>";
    echo "<h3 class='font-semibold mb-2'>أول 20 سطر من .htaccess:</h3>";
    echo "<pre class='text-xs overflow-x-auto'>" . htmlspecialchars(implode("\n", $firstLines)) . "</pre>";
    echo "</div>";
    
} else {
    echo "<div class='bg-red-50 border border-red-200 rounded p-4'>";
    echo "<p class='text-red-700'>❌ ملف .htaccess غير موجود</p>";
    echo "</div>";
}

echo "</div>";

// Test URLs
echo "<div class='bg-white rounded-lg shadow p-6 mb-6'>";
echo "<h2 class='text-lg font-bold mb-4'>🧪 اختبار الروابط</h2>";

$testUrls = [
    'Laravel Index' => '/',
    'Bookings Index' => '/bookings',
    'Bookings Create' => '/bookings/create?booth_id=23&exhibition_id=1',
    'Dashboard' => '/dashboard',
    'Payment Test' => '/payment/initiate/1'
];

echo "<div class='space-y-3'>";

foreach ($testUrls as $name => $url) {
    echo "<div class='flex items-center justify-between p-3 border border-gray-200 rounded'>";
    echo "<div>";
    echo "<h3 class='font-semibold'>{$name}</h3>";
    echo "<code class='text-sm text-gray-600'>{$url}</code>";
    echo "</div>";
    echo "<a href='{$url}' target='_blank' class='bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 text-sm'>اختبار</a>";
    echo "</div>";
}

echo "</div>";
echo "</div>";

// Recommendations
echo "<div class='bg-yellow-50 border border-yellow-200 rounded-lg p-6'>";
echo "<h2 class='text-lg font-bold text-yellow-700 mb-4'>💡 التوصيات</h2>";

echo "<div class='space-y-4'>";

echo "<div class='bg-white p-4 rounded border'>";
echo "<h3 class='font-semibold mb-2'>إذا كان Laravel لا يعمل:</h3>";
echo "<ul class='text-sm space-y-1 list-disc list-inside'>";
echo "<li>تحقق من أن composer install تم تشغيله</li>";
echo "<li>تحقق من ملف .env</li>";
echo "<li>تحقق من أن Apache mod_rewrite مفعل</li>";
echo "<li>تحقق من صلاحيات الملفات</li>";
echo "</ul>";
echo "</div>";

echo "<div class='bg-white p-4 rounded border'>";
echo "<h3 class='font-semibold mb-2'>إذا كان Laravel يعمل لكن Routes لا تعمل:</h3>";
echo "<ul class='text-sm space-y-1 list-disc list-inside'>";
echo "<li>تحقق من ملف routes/web.php</li>";
echo "<li>تحقق من أن BookingController موجود</li>";
echo "<li>تحقق من middleware المطلوب</li>";
echo "<li>تحقق من route caching</li>";
echo "</ul>";
echo "</div>";

echo "<div class='bg-white p-4 rounded border'>";
echo "<h3 class='font-semibold mb-2'>الحل البديل:</h3>";
echo "<ul class='text-sm space-y-1 list-disc list-inside'>";
echo "<li>استخدام booking-alternative.php مؤقتاً</li>";
echo "<li>إصلاح Laravel تدريجياً</li>";
echo "<li>إضافة تكامل MyFatoorah لاحقاً</li>";
echo "</ul>";
echo "</div>";

echo "</div>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>

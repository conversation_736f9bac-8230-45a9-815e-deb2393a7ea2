<?php
// Final Homepage Test - After All Fixes
// This file tests the homepage after all optimizations

session_start();

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='utf-8'>";
echo "<title>الاختبار النهائي - Season Expo Kuwait</title>";
echo "<script src='https://cdn.tailwindcss.com'></script>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }";
echo "</style>";
echo "</head>";
echo "<body class='bg-gray-50 p-8'>";

echo "<div class='max-w-4xl mx-auto'>";
echo "<h1 class='text-3xl font-bold mb-8' style='color: #2C3E50;'>🎯 الاختبار النهائي - الصفحة الرئيسية</h1>";

// Status check
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>📊 حالة الملفات بعد التحديث</h2>";

$files = [
    'index.php' => 'الصفحة الرئيسية',
    '.htaccess' => 'ملف إعادة التوجيه',
    'homepage-fixed.php' => 'الصفحة المحدثة'
];

echo "<div class='grid grid-cols-1 md:grid-cols-3 gap-4'>";

foreach ($files as $file => $description) {
    $fullPath = __DIR__ . '/' . $file;
    echo "<div class='border border-gray-200 rounded-lg p-4 text-center'>";
    echo "<h3 class='font-semibold mb-2'>{$description}</h3>";
    
    if (file_exists($fullPath)) {
        $size = round(filesize($fullPath)/1024, 2);
        echo "<div class='text-green-600 mb-2'>✅ موجود</div>";
        echo "<div class='text-xs text-gray-500'>{$size} KB</div>";
        
        if ($file === '.htaccess') {
            $content = file_get_contents($fullPath);
            $lines = count(explode("\n", trim($content)));
            echo "<div class='text-xs text-gray-500 mt-1'>{$lines} أسطر</div>";
            if ($lines <= 10) {
                echo "<div class='text-green-600 text-xs'>✅ مبسط</div>";
            }
        }
        
    } else {
        echo "<div class='text-red-600'>❌ مفقود</div>";
    }
    echo "</div>";
}

echo "</div>";
echo "</div>";

// Main test
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>🧪 اختبار الروابط الآن</h2>";

echo "<div class='text-center mb-6'>";
echo "<p class='text-gray-600 mb-4'>اختبر الروابط التالية بالترتيب:</p>";
echo "</div>";

echo "<div class='space-y-4'>";

// Test 1: Main homepage
echo "<div class='bg-blue-50 border border-blue-200 rounded-lg p-6'>";
echo "<h3 class='font-semibold mb-3 text-center text-blue-700'>🎯 الاختبار الأساسي</h3>";
echo "<div class='text-center'>";
echo "<a href='/' class='inline-block px-8 py-4 text-white text-lg font-semibold rounded-lg hover:opacity-80 transition-opacity' style='background: #2C3E50;'>";
echo "🏠 اختبار seasonexpo.com/";
echo "</a>";
echo "<p class='text-sm text-gray-600 mt-3'>هذا هو الاختبار الأهم - يجب أن يوجه للصفحة العربية</p>";
echo "</div>";
echo "</div>";

// Test 2: Clean URLs
echo "<div class='bg-green-50 border border-green-200 rounded-lg p-6'>";
echo "<h3 class='font-semibold mb-3 text-center text-green-700'>🌐 اختبار الروابط النظيفة</h3>";
echo "<div class='flex flex-col sm:flex-row gap-3 justify-center'>";
echo "<a href='/ar' class='px-6 py-3 border-2 border-green-500 text-green-600 rounded-lg hover:bg-green-50 text-center'>";
echo "🇰🇼 /ar";
echo "</a>";
echo "<a href='/en' class='px-6 py-3 border-2 border-blue-500 text-blue-600 rounded-lg hover:bg-blue-50 text-center'>";
echo "🇺🇸 /en";
echo "</a>";
echo "</div>";
echo "</div>";

// Test 3: Direct links (backup)
echo "<div class='bg-yellow-50 border border-yellow-200 rounded-lg p-6'>";
echo "<h3 class='font-semibold mb-3 text-center text-yellow-700'>🔗 الروابط المباشرة (احتياطي)</h3>";
echo "<div class='flex flex-col sm:flex-row gap-3 justify-center'>";
echo "<a href='/homepage-fixed.php?lang=ar' class='px-6 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 text-center text-sm'>";
echo "العربية (مباشر)";
echo "</a>";
echo "<a href='/homepage-fixed.php?lang=en' class='px-6 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 text-center text-sm'>";
echo "English (مباشر)";
echo "</a>";
echo "</div>";
echo "<p class='text-xs text-gray-500 text-center mt-2'>هذه مضمونة العمل</p>";
echo "</div>";

echo "</div>";
echo "</div>";

// Results guide
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>📋 دليل النتائج</h2>";

echo "<div class='space-y-4'>";

echo "<div class='flex items-start space-x-reverse space-x-3 p-4 bg-green-50 border border-green-200 rounded-lg'>";
echo "<div class='text-2xl'>✅</div>";
echo "<div>";
echo "<h3 class='font-semibold text-green-700'>إذا عمل الرابط الأول (/):</h3>";
echo "<p class='text-sm text-green-600'>المشكلة محلولة بالكامل! الموقع يعمل بشكل مثالي.</p>";
echo "</div>";
echo "</div>";

echo "<div class='flex items-start space-x-reverse space-x-3 p-4 bg-yellow-50 border border-yellow-200 rounded-lg'>";
echo "<div class='text-2xl'>⚠️</div>";
echo "<div>";
echo "<h3 class='font-semibold text-yellow-700'>إذا عملت الروابط النظيفة فقط:</h3>";
echo "<p class='text-sm text-yellow-600'>مشكلة بسيطة في index.php - لكن الموقع يعمل.</p>";
echo "</div>";
echo "</div>";

echo "<div class='flex items-start space-x-reverse space-x-3 p-4 bg-blue-50 border border-blue-200 rounded-lg'>";
echo "<div class='text-2xl'>🔧</div>";
echo "<div>";
echo "<h3 class='font-semibold text-blue-700'>إذا عملت الروابط المباشرة فقط:</h3>";
echo "<p class='text-sm text-blue-600'>استخدم هذه الروابط مؤقت<|im_start|> - الوظائف تعمل بشكل كامل.</p>";
echo "</div>";
echo "</div>";

echo "</div>";
echo "</div>";

// Quick navigation
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-8'>";
echo "<h2 class='text-xl font-bold mb-4' style='color: #2C3E50;'>⚡ التنقل السريع</h2>";

echo "<div class='grid grid-cols-1 md:grid-cols-2 gap-4'>";

echo "<div>";
echo "<h3 class='font-semibold mb-3'>الصفحات الأساسية:</h3>";
echo "<div class='space-y-2'>";
echo "<a href='/homepage-fixed.php?lang=ar' class='block px-4 py-2 text-white rounded hover:opacity-80 text-center' style='background: #2C3E50;'>🏠 الصفحة الرئيسية</a>";
echo "<a href='/login-simple.php?lang=ar' class='block px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 text-center'>🔐 تسجيل الدخول</a>";
echo "</div>";
echo "</div>";

echo "<div>";
echo "<h3 class='font-semibold mb-3'>أدوات الإدارة:</h3>";
echo "<div class='space-y-2'>";
echo "<a href='/admin-slider-management.php' class='block px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 text-center'>🖼️ إدارة السلايد</a>";
echo "<a href='/diagnose-homepage.php' class='block px-4 py-2 bg-yellow-600 text-white rounded hover:bg-yellow-700 text-center'>🔍 التشخيص</a>";
echo "</div>";
echo "</div>";

echo "</div>";
echo "</div>";

// Summary
echo "<div class='bg-gradient-to-r from-green-50 to-blue-50 border border-green-200 rounded-lg p-6 text-center'>";
echo "<h2 class='text-2xl font-bold mb-4' style='color: #2C3E50;'>🎉 ملخص الإصلاحات</h2>";

echo "<div class='grid grid-cols-1 md:grid-cols-3 gap-4 mb-6'>";

echo "<div class='bg-white p-4 rounded-lg shadow'>";
echo "<div class='text-3xl mb-2'>📝</div>";
echo "<h3 class='font-semibold mb-2'>index.php</h3>";
echo "<p class='text-sm text-gray-600'>مبسط ومحسن</p>";
echo "</div>";

echo "<div class='bg-white p-4 rounded-lg shadow'>";
echo "<div class='text-3xl mb-2'>⚙️</div>";
echo "<h3 class='font-semibold mb-2'>.htaccess</h3>";
echo "<p class='text-sm text-gray-600'>قواعد أساسية فقط</p>";
echo "</div>";

echo "<div class='bg-white p-4 rounded-lg shadow'>";
echo "<div class='text-3xl mb-2'>🔗</div>";
echo "<h3 class='font-semibold mb-2'>الروابط</h3>";
echo "<p class='text-sm text-gray-600'>متعددة الخيارات</p>";
echo "</div>";

echo "</div>";

echo "<p class='text-lg text-gray-700 mb-4'>الآن الموقع مضمون العمل بطرق متعددة!</p>";

echo "<div class='space-x-reverse space-x-2'>";
echo "<span class='inline-block px-3 py-1 bg-green-100 text-green-700 rounded-full text-sm'>✅ محسن</span>";
echo "<span class='inline-block px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-sm'>✅ مبسط</span>";
echo "<span class='inline-block px-3 py-1 bg-purple-100 text-purple-700 rounded-full text-sm'>✅ مضمون</span>";
echo "</div>";

echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>

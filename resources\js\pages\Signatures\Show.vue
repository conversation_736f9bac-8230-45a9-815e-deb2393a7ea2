<script setup>
import { Head, Link, router } from '@inertiajs/vue3';
import { ref } from 'vue';
import LanguageSwitcher from '@/components/LanguageSwitcher.vue';

const props = defineProps({
    signature: Object,
    verificationUrl: String,
    locale: String,
    translations: Object,
});

const t = (key) => {
    return props.translations?.app?.[key] || key;
};

const isRTL = props.locale === 'ar';

const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString(props.locale === 'ar' ? 'ar-SA' : 'en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
};

const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text).then(() => {
        alert(t('copied_to_clipboard'));
    });
};

const deleteSignature = () => {
    if (confirm(t('confirm_delete_signature'))) {
        router.delete(route('signatures.destroy', props.signature.id));
    }
};

const getStatusColor = (isVerified) => {
    return isVerified ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800';
};

const getStatusText = (isVerified) => {
    return isVerified ? t('verified') : t('pending_verification');
};
</script>

<template>
    <Head :title="t('signature_details')" />

    <div class="min-h-screen bg-gray-50" :dir="isRTL ? 'rtl' : 'ltr'">
        <!-- Fixed Navigation -->
        <nav class="fixed top-0 left-0 right-0 fixed-nav z-50">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <div class="flex items-center">
                        <Link href="/" class="text-2xl font-bold text-blue-600 hover:text-blue-700 transition-colors">
                            {{ t('site_name') }}
                        </Link>
                    </div>
                    <div class="flex items-center" :class="isRTL ? 'space-x-reverse space-x-4' : 'space-x-4'">
                        <LanguageSwitcher :current-locale="locale" />
                        <Link href="/signatures" class="nav-link text-gray-600">{{ t('my_signatures') }}</Link>
                        <Link href="/dashboard" class="nav-link text-gray-600">{{ t('dashboard') }}</Link>
                    </div>
                </div>
            </div>
        </nav>

        <div class="py-12 pt-24">
            <div class="mx-auto max-w-4xl sm:px-6 lg:px-8">
                <!-- Header -->
                <div class="mb-8">
                    <div class="flex items-center gap-4">
                        <Link 
                            :href="route('signatures.index')"
                            class="text-gray-600 hover:text-gray-900"
                        >
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="isRTL ? 'M9 5l7 7-7 7' : 'M15 19l-7-7 7-7'" />
                            </svg>
                        </Link>
                        <div>
                            <h1 class="text-3xl font-bold text-gray-900">{{ t('signature_details') }}</h1>
                            <p class="text-gray-600 mt-2">{{ t('view_signature_information') }}</p>
                        </div>
                    </div>
                </div>

                <!-- Signature Details -->
                <div class="bg-white rounded-lg shadow-sm p-8 mb-8">
                    <!-- Status Badge -->
                    <div class="flex justify-between items-start mb-6">
                        <span 
                            :class="getStatusColor(signature.is_verified)"
                            class="px-3 py-1 text-sm rounded-full font-medium"
                        >
                            {{ getStatusText(signature.is_verified) }}
                        </span>
                        <div class="flex space-x-3">
                            <a 
                                :href="route('signatures.certificate', signature.id)"
                                class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors"
                                target="_blank"
                            >
                                {{ t('download_certificate') }}
                            </a>
                            <button 
                                @click="deleteSignature"
                                class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
                            >
                                {{ t('delete') }}
                            </button>
                        </div>
                    </div>

                    <!-- Basic Information -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">{{ t('document_type') }}</label>
                            <p class="text-lg text-gray-900">{{ signature.document_type }}</p>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">{{ t('document_id') }}</label>
                            <p class="text-lg text-gray-900">{{ signature.document_id }}</p>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">{{ t('signer_name') }}</label>
                            <p class="text-lg text-gray-900">{{ signature.signer_name }}</p>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">{{ t('signer_email') }}</label>
                            <p class="text-lg text-gray-900">{{ signature.signer_email }}</p>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">{{ t('signed_date') }}</label>
                            <p class="text-lg text-gray-900">{{ formatDate(signature.signed_at) }}</p>
                        </div>
                        
                        <div v-if="signature.verified_at">
                            <label class="block text-sm font-medium text-gray-700 mb-1">{{ t('verified_date') }}</label>
                            <p class="text-lg text-gray-900">{{ formatDate(signature.verified_at) }}</p>
                        </div>
                    </div>

                    <!-- Signature Image -->
                    <div class="mb-8">
                        <label class="block text-sm font-medium text-gray-700 mb-3">{{ t('digital_signature') }}</label>
                        <div class="border border-gray-300 rounded-lg p-4 bg-gray-50">
                            <img 
                                :src="signature.signature_data" 
                                :alt="t('digital_signature')"
                                class="max-w-full h-auto mx-auto"
                                style="max-height: 200px;"
                            />
                        </div>
                    </div>

                    <!-- Technical Details -->
                    <div class="border-t border-gray-200 pt-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">{{ t('technical_details') }}</h3>
                        
                        <div class="space-y-4">
                            <!-- Signature Hash -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">{{ t('signature_hash') }}</label>
                                <div class="flex items-center space-x-2">
                                    <p class="text-xs text-gray-600 font-mono bg-gray-100 p-2 rounded flex-1 break-all">
                                        {{ signature.signature_hash }}
                                    </p>
                                    <button 
                                        @click="copyToClipboard(signature.signature_hash)"
                                        class="bg-gray-200 text-gray-700 px-3 py-2 rounded hover:bg-gray-300 transition-colors"
                                    >
                                        {{ t('copy') }}
                                    </button>
                                </div>
                            </div>

                            <!-- Verification Token -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">{{ t('verification_token') }}</label>
                                <div class="flex items-center space-x-2">
                                    <p class="text-xs text-gray-600 font-mono bg-gray-100 p-2 rounded flex-1 break-all">
                                        {{ signature.verification_token }}
                                    </p>
                                    <button 
                                        @click="copyToClipboard(signature.verification_token)"
                                        class="bg-gray-200 text-gray-700 px-3 py-2 rounded hover:bg-gray-300 transition-colors"
                                    >
                                        {{ t('copy') }}
                                    </button>
                                </div>
                            </div>

                            <!-- Verification URL -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">{{ t('verification_url') }}</label>
                                <div class="flex items-center space-x-2">
                                    <p class="text-xs text-gray-600 bg-gray-100 p-2 rounded flex-1 break-all">
                                        {{ verificationUrl }}
                                    </p>
                                    <button 
                                        @click="copyToClipboard(verificationUrl)"
                                        class="bg-gray-200 text-gray-700 px-3 py-2 rounded hover:bg-gray-300 transition-colors"
                                    >
                                        {{ t('copy') }}
                                    </button>
                                    <a 
                                        :href="verificationUrl"
                                        target="_blank"
                                        class="bg-blue-600 text-white px-3 py-2 rounded hover:bg-blue-700 transition-colors"
                                    >
                                        {{ t('verify') }}
                                    </a>
                                </div>
                            </div>

                            <!-- IP Address -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">{{ t('signer_ip') }}</label>
                                <p class="text-sm text-gray-600">{{ signature.signer_ip }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Security Notice -->
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                    <div class="flex">
                        <svg class="w-6 h-6 text-blue-600 mt-0.5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                        </svg>
                        <div>
                            <h3 class="text-lg font-medium text-blue-800">{{ t('security_information') }}</h3>
                            <p class="text-sm text-blue-700 mt-2">
                                {{ t('signature_security_info') }}
                            </p>
                            <ul class="text-sm text-blue-700 mt-2 list-disc list-inside space-y-1">
                                <li>{{ t('cryptographic_hash_protection') }}</li>
                                <li>{{ t('timestamp_verification') }}</li>
                                <li>{{ t('ip_address_logging') }}</li>
                                <li>{{ t('tamper_proof_storage') }}</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

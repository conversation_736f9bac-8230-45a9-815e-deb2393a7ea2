<?php

namespace App\Http\Controllers;

use App\Models\DigitalSignature;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;

class DigitalSignatureController extends Controller
{
    /**
     * Display a listing of signatures.
     */
    public function index(Request $request)
    {
        $query = DigitalSignature::with('user')
            ->where('user_id', Auth::id())
            ->orderBy('created_at', 'desc');

        if ($request->has('document_type')) {
            $query->byDocumentType($request->document_type);
        }

        if ($request->has('verified')) {
            if ($request->verified === 'true') {
                $query->verified();
            } else {
                $query->unverified();
            }
        }

        $signatures = $query->paginate(10);

        return Inertia::render('Signatures/Index', [
            'signatures' => $signatures,
            'documentTypes' => DigitalSignature::getTypes(),
            'filters' => $request->only(['document_type', 'verified']),
            'locale' => app()->getLocale(),
            'translations' => [
                'app' => __('app'),
            ],
        ]);
    }

    /**
     * Show the form for creating a new signature.
     */
    public function create(Request $request)
    {
        $documentType = $request->get('document_type', 'document');
        $documentId = $request->get('document_id', '');

        return Inertia::render('Signatures/Create', [
            'documentType' => $documentType,
            'documentId' => $documentId,
            'documentTypes' => DigitalSignature::getTypes(),
            'locale' => app()->getLocale(),
            'translations' => [
                'app' => __('app'),
            ],
        ]);
    }

    /**
     * Store a newly created signature.
     */
    public function store(Request $request)
    {
        $request->validate([
            'document_type' => 'required|string|in:' . implode(',', array_keys(DigitalSignature::getTypes())),
            'document_id' => 'required|string|max:255',
            'signer_name' => 'required|string|max:255',
            'signature_data' => 'required|string', // Base64 encoded signature
            'metadata' => 'nullable|array',
        ]);

        $signature = DigitalSignature::create([
            'document_type' => $request->document_type,
            'document_id' => $request->document_id,
            'user_id' => Auth::id(),
            'signer_name' => $request->signer_name,
            'signer_email' => Auth::user()->email,
            'signer_ip' => $request->ip(),
            'signature_data' => $request->signature_data,
            'metadata' => $request->metadata,
        ]);

        // Generate certificate
        $signature->generateCertificate();

        return redirect()->route('signatures.show', $signature)
            ->with('success', 'Digital signature created successfully!');
    }

    /**
     * Display the specified signature.
     */
    public function show(DigitalSignature $signature)
    {
        // Check if user can view this signature
        if ($signature->user_id !== Auth::id()) {
            abort(403, 'Unauthorized access to signature.');
        }

        return Inertia::render('Signatures/Show', [
            'signature' => $signature->load('user'),
            'verificationUrl' => $signature->getVerificationUrl(),
            'locale' => app()->getLocale(),
            'translations' => [
                'app' => __('app'),
            ],
        ]);
    }

    /**
     * Verify a signature using verification token.
     */
    public function verify($token)
    {
        $signature = DigitalSignature::where('verification_token', $token)->first();

        if (!$signature) {
            return Inertia::render('Signatures/VerificationResult', [
                'success' => false,
                'message' => 'Invalid verification token.',
                'locale' => app()->getLocale(),
                'translations' => [
                    'app' => __('app'),
                ],
            ]);
        }

        $isValid = $signature->isValid();

        if ($isValid && !$signature->is_verified) {
            $signature->verify();
        }

        return Inertia::render('Signatures/VerificationResult', [
            'success' => $isValid,
            'signature' => $signature,
            'message' => $isValid ? 'Signature is valid and verified.' : 'Signature verification failed.',
            'locale' => app()->getLocale(),
            'translations' => [
                'app' => __('app'),
            ],
        ]);
    }

    /**
     * Download signature certificate.
     */
    public function downloadCertificate(DigitalSignature $signature)
    {
        // Check if user can download this certificate
        if ($signature->user_id !== Auth::id()) {
            abort(403, 'Unauthorized access to certificate.');
        }

        if (!$signature->certificate_path) {
            $signature->generateCertificate();
        }

        if (!Storage::disk('public')->exists($signature->certificate_path)) {
            abort(404, 'Certificate file not found.');
        }

        return response()->download(
            storage_path('app/public/' . $signature->certificate_path),
            'signature_certificate_' . $signature->id . '.json'
        );
    }

    /**
     * Delete signature.
     */
    public function destroy(DigitalSignature $signature)
    {
        // Check if user can delete this signature
        if ($signature->user_id !== Auth::id()) {
            abort(403, 'Unauthorized access to signature.');
        }

        // Delete certificate file if exists
        if ($signature->certificate_path && Storage::disk('public')->exists($signature->certificate_path)) {
            Storage::disk('public')->delete($signature->certificate_path);
        }

        $signature->delete();

        return redirect()->route('signatures.index')
            ->with('success', 'Digital signature deleted successfully!');
    }

    /**
     * API endpoint to get signatures for a specific document.
     */
    public function getSignaturesForDocument(Request $request)
    {
        $request->validate([
            'document_type' => 'required|string',
            'document_id' => 'required|string',
        ]);

        $signatures = DigitalSignature::byDocument($request->document_type, $request->document_id)
            ->with('user')
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json([
            'signatures' => $signatures,
            'total_signatures' => $signatures->count(),
            'verified_signatures' => $signatures->where('is_verified', true)->count(),
        ]);
    }
}

<?php
// Simulate exactly what happens when someone visits your homepage
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h1>Web Request Simulation</h1>";

try {
    echo "<h2>Step 1: Environment Setup</h2>";
    
    // Set up the same environment as index.php
    define('LARAVEL_START', microtime(true));
    echo "✅ LARAVEL_START defined<br>";
    
    // Check maintenance mode
    $maintenanceFile = dirname(__DIR__) . '/storage/framework/maintenance.php';
    if (file_exists($maintenanceFile)) {
        echo "⚠️ Maintenance mode file exists<br>";
        require $maintenanceFile;
    } else {
        echo "✅ No maintenance mode<br>";
    }
    
    echo "<h2>Step 2: Load Dependencies</h2>";
    
    // Load autoloader
    require dirname(__DIR__) . '/vendor/autoload.php';
    echo "✅ Autoloader loaded<br>";
    
    // Load Laravel app
    $app = require_once dirname(__DIR__) . '/bootstrap/app.php';
    echo "✅ Laravel app loaded<br>";
    
    echo "<h2>Step 3: Create HTTP Kernel</h2>";
    
    // Create HTTP kernel
    $kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);
    echo "✅ HTTP Kernel created<br>";
    echo "Kernel class: " . get_class($kernel) . "<br>";
    
    echo "<h2>Step 4: Create Request</h2>";
    
    // Create request exactly like Laravel does
    $request = Illuminate\Http\Request::capture();
    echo "✅ Request captured<br>";
    echo "Method: " . $request->getMethod() . "<br>";
    echo "URI: " . $request->getRequestUri() . "<br>";
    echo "Host: " . $request->getHost() . "<br>";
    
    echo "<h2>Step 5: Handle Request (This is where it might fail)</h2>";
    
    // This is the critical step that might be failing
    echo "Attempting to handle request...<br>";
    $response = $kernel->handle($request);
    echo "✅ Request handled successfully!<br>";
    
    echo "Response status: " . $response->getStatusCode() . "<br>";
    echo "Response headers: " . count($response->headers->all()) . " headers<br>";
    
    $content = $response->getContent();
    echo "Response content length: " . strlen($content) . " bytes<br>";
    
    if (strlen($content) > 0) {
        echo "<h3>Response Preview (first 500 chars):</h3>";
        echo "<pre style='background: #f5f5f5; padding: 10px; max-height: 200px; overflow-y: auto;'>";
        echo htmlspecialchars(substr($content, 0, 500));
        echo "</pre>";
        
        // Check if it's an error page
        if (strpos($content, 'error') !== false || strpos($content, 'exception') !== false) {
            echo "⚠️ Response contains error content<br>";
        }
    }
    
    echo "<h2>Step 6: Terminate Request</h2>";
    $kernel->terminate($request, $response);
    echo "✅ Request terminated<br>";
    
    echo "<br><div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px;'>";
    echo "<h3>🎉 WEB REQUEST SUCCESSFUL!</h3>";
    echo "<p>The web request simulation worked perfectly. This means the issue might be:</p>";
    echo "<ul>";
    echo "<li>URL rewriting (.htaccess issues)</li>";
    echo "<li>Server configuration</li>";
    echo "<li>Specific route problems</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Throwable $e) {
    echo "<br><div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px;'>";
    echo "<h3>❌ ERROR FOUND!</h3>";
    echo "<p><strong>This is likely the cause of your 500 error:</strong></p>";
    echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>File:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    echo "<h4>Full Stack Trace:</h4>";
    echo "<pre style='background: #f8f8f8; padding: 10px; max-height: 400px; overflow-y: auto;'>";
    echo htmlspecialchars($e->getTraceAsString());
    echo "</pre>";
    echo "</div>";
}

echo "<h2>System Information:</h2>";
echo "<p>PHP Version: " . phpversion() . "</p>";
echo "<p>Memory Limit: " . ini_get('memory_limit') . "</p>";
echo "<p>Max Execution Time: " . ini_get('max_execution_time') . "</p>";
echo "<p>Server Software: " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "</p>";
echo "<p>Document Root: " . ($_SERVER['DOCUMENT_ROOT'] ?? 'Unknown') . "</p>";
?>

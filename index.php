<?php
// Main Index File - Simple Redirect Solution
// This ensures the homepage always works

session_start();

// Get language preference
$currentLang = $_SESSION['language'] ?? $_COOKIE['language'] ?? $_GET['lang'] ?? 'ar';

// Validate language
$supportedLanguages = ['ar', 'en'];
if (!in_array($currentLang, $supportedLanguages)) {
    $currentLang = 'ar';
}

// Save language preference
$_SESSION['language'] = $currentLang;
setcookie('language', $currentLang, time() + (30 * 24 * 60 * 60), '/');

// Simple redirect to working homepage
if ($currentLang === 'en') {
    header('Location: /en.php');
} else {
    header('Location: /homepage-fixed.php?lang=ar');
}
exit;
?>

<?php
// Main Index File - Redirects to Updated Homepage
// This ensures users always see the updated homepage with language support

session_start();

// Get language preference
$lang = $_SESSION['language'] ?? $_COOKIE['language'] ?? $_GET['lang'] ?? 'ar';

// Validate language
$supportedLanguages = ['ar', 'en'];
if (!in_array($lang, $supportedLanguages)) {
    $lang = 'ar';
}

// Save language preference
$_SESSION['language'] = $lang;
setcookie('language', $lang, time() + (30 * 24 * 60 * 60), '/');

// Redirect to the updated homepage
header('Location: /homepage-fixed.php?lang=' . $lang);
exit;
?>

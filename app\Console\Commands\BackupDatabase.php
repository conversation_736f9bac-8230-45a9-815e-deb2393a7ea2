<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class BackupDatabase extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'db:backup {--path=storage/backups}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create a backup of the MySQL database';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔄 Creating database backup...');

        $database = config('database.connections.mysql.database');
        $username = config('database.connections.mysql.username');
        $password = config('database.connections.mysql.password');
        $host = config('database.connections.mysql.host');
        $port = config('database.connections.mysql.port');

        $backupPath = $this->option('path');
        $filename = 'seasonexpodb_backup_' . date('Y-m-d_H-i-s') . '.sql';
        $fullPath = $backupPath . '/' . $filename;

        // Create backup directory if it doesn't exist
        if (!file_exists($backupPath)) {
            mkdir($backupPath, 0755, true);
        }

        // Build mysqldump command
        $command = sprintf(
            'mysqldump --user=%s --password=%s --host=%s --port=%s %s > %s',
            escapeshellarg($username),
            escapeshellarg($password),
            escapeshellarg($host),
            escapeshellarg($port),
            escapeshellarg($database),
            escapeshellarg($fullPath)
        );

        // Execute backup
        $output = null;
        $returnVar = null;
        exec($command, $output, $returnVar);

        if ($returnVar === 0) {
            $this->info("✅ Database backup created successfully!");
            $this->line("   File: {$fullPath}");
            $this->line("   Size: " . $this->formatBytes(filesize($fullPath)));
        } else {
            $this->error("❌ Backup failed!");
            $this->line("   Command: {$command}");
        }

        return $returnVar;
    }

    private function formatBytes($size, $precision = 2)
    {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');

        for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
            $size /= 1024;
        }

        return round($size, $precision) . ' ' . $units[$i];
    }
}

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>نسيت كلمة المرور - Season Expo</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .bg-gradient { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
    </style>
</head>
<body class="min-h-screen bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="/" class="text-2xl font-bold text-blue-600 hover:text-blue-700 transition-colors">
                        Season Expo
                    </a>
                </div>
                <div class="flex items-center space-x-reverse space-x-4">
                    <a href="/login-simple" class="text-gray-600 hover:text-gray-900">تسجيل الدخول</a>
                    <a href="/register" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">إنشاء حساب</a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8">
            <div>
                <div class="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-blue-100">
                    <span class="text-2xl">🔑</span>
                </div>
                <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                    نسيت كلمة المرور؟
                </h2>
                <p class="mt-2 text-center text-sm text-gray-600">
                    لا تقلق! أدخل بريدك الإلكتروني وسنرسل لك رابط إعادة تعيين كلمة المرور
                </p>
                <div class="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    <p class="text-sm text-blue-800 text-center">
                        💡 <strong>نصيحة:</strong> تحقق من مجلد الرسائل غير المرغوب فيها (Spam) إذا لم تجد الرسالة في صندوق الوارد
                    </p>
                </div>
            </div>

            <!-- Success Message -->
            <?php if(session('status')): ?>
                <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <span class="text-green-400 text-xl">✅</span>
                        </div>
                        <div class="mr-3">
                            <p class="text-sm font-medium text-green-800">
                                <?php echo e(session('status')); ?>

                            </p>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Error Messages -->
            <?php if($errors->any()): ?>
                <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <span class="text-red-400 text-xl">❌</span>
                        </div>
                        <div class="mr-3">
                            <h3 class="text-sm font-medium text-red-800">
                                يرجى تصحيح الأخطاء التالية:
                            </h3>
                            <div class="mt-2 text-sm text-red-700">
                                <ul class="list-disc list-inside space-y-1">
                                    <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <li><?php echo e($error); ?></li>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Forgot Password Form -->
            <form class="mt-8 space-y-6" action="/reset-password-form" method="POST">
                <?php echo csrf_field(); ?>
                <div class="rounded-md shadow-sm -space-y-px">
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                            البريد الإلكتروني
                        </label>
                        <input id="email" name="email" type="email" autocomplete="email" required
                               class="appearance-none rounded-lg relative block w-full px-3 py-3 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                               placeholder="أدخل بريدك الإلكتروني"
                               value="<?php echo e(old('email')); ?>">
                    </div>
                </div>

                <div>
                    <button type="submit"
                            class="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
                        <span class="absolute right-0 inset-y-0 flex items-center pr-3">
                            <span class="text-blue-500 group-hover:text-blue-400 text-lg">📧</span>
                        </span>
                        إرسال رابط إعادة التعيين
                    </button>
                </div>

                <div class="text-center">
                    <div class="text-sm">
                        <a href="/login-simple" class="font-medium text-blue-600 hover:text-blue-500">
                            ← العودة إلى تسجيل الدخول
                        </a>
                    </div>
                </div>
            </form>

            <!-- Additional Help -->
            <div class="mt-8 bg-gray-50 rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">هل تحتاج مساعدة؟</h3>
                <div class="space-y-3 text-sm text-gray-600">
                    <div class="flex items-center">
                        <span class="text-lg ml-2">📞</span>
                        <span>اتصل بنا: +965 1234 5678</span>
                    </div>
                    <div class="flex items-center">
                        <span class="text-lg ml-2">📧</span>
                        <span>البريد الإلكتروني: <EMAIL></span>
                    </div>
                    <div class="flex items-center">
                        <span class="text-lg ml-2">🕒</span>
                        <span>ساعات العمل: الأحد - الخميس، 9 صباحاً - 6 مساءً</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-8">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <p class="text-lg font-semibold mb-2">Season Expo</p>
            <p class="text-gray-400">منصة المعارض الرائدة في الكويت</p>
            <p class="text-gray-400 mt-4">© 2024 Season Expo. جميع الحقوق محفوظة.</p>
        </div>
    </footer>
</body>
</html>
<?php /**PATH E:\xampp\htdocs\season_expo_2\resources\views/auth/forgot-password.blade.php ENDPATH**/ ?>
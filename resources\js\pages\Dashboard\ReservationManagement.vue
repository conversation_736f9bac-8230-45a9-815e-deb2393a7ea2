<template>
  <Head :title="t('my_reservations', 'My Reservations') + ' - Dashboard'" />

  <div class="min-h-screen bg-gray-50" :dir="isRTL ? 'rtl' : 'ltr'">
    <!-- Navigation -->
    <nav class="bg-white shadow">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex items-center">
            <Link href="/" class="text-2xl font-bold text-blue-600">Season Expo</Link>
          </div>
          <div :class="isRTL ? 'flex items-center space-x-reverse space-x-4' : 'flex items-center space-x-4'">
            <Link href="/dashboard" class="text-gray-600 hover:text-gray-900">{{ t('dashboard', 'Dashboard') }}</Link>
            <Link href="/dashboard/booth-search" class="text-gray-600 hover:text-gray-900">{{ t('search_booths', 'Search Booths') }}</Link>
            <Link href="/logout" method="post" class="text-red-600 hover:text-red-800">{{ t('logout', 'Logout') }}</Link>
          </div>
        </div>
      </div>
    </nav>

    <div class="py-12">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
          <h1 class="text-3xl font-bold text-gray-900 mb-4">📋 {{ t('my_reservations', 'My Reservations') }}</h1>
          <p class="text-gray-600">{{ t('manage_reservations_subtitle', 'Manage your booth reservations and track their status') }}</p>
        </div>

        <!-- Summary Stats -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
          <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <span class="text-blue-600 text-sm font-bold">📊</span>
                </div>
              </div>
              <div class="ml-4">
                <div class="text-2xl font-bold text-gray-900">{{ summaryStats.total_reservations }}</div>
                <div class="text-sm text-gray-600">Total Reservations</div>
              </div>
            </div>
          </div>

          <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                  <span class="text-green-600 text-sm font-bold">✅</span>
                </div>
              </div>
              <div class="ml-4">
                <div class="text-2xl font-bold text-gray-900">{{ summaryStats.confirmed_reservations }}</div>
                <div class="text-sm text-gray-600">Confirmed</div>
              </div>
            </div>
          </div>

          <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                  <span class="text-yellow-600 text-sm font-bold">⏳</span>
                </div>
              </div>
              <div class="ml-4">
                <div class="text-2xl font-bold text-gray-900">{{ summaryStats.pending_reservations }}</div>
                <div class="text-sm text-gray-600">Pending</div>
              </div>
            </div>
          </div>

          <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                  <span class="text-red-600 text-sm font-bold">❌</span>
                </div>
              </div>
              <div class="ml-4">
                <div class="text-2xl font-bold text-gray-900">{{ summaryStats.cancelled_reservations }}</div>
                <div class="text-sm text-gray-600">Cancelled</div>
              </div>
            </div>
          </div>

          <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                  <span class="text-purple-600 text-sm font-bold">💰</span>
                </div>
              </div>
              <div class="ml-4">
                <div class="text-2xl font-bold text-gray-900">د.ك {{ formatKWD(summaryStats.total_spent) }}</div>
                <div class="text-sm text-gray-600">Total Spent</div>
              </div>
            </div>
          </div>
        </div>

        <!-- Filters -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
          <h2 class="text-lg font-semibold text-gray-900 mb-4">Filter Reservations</h2>

          <form @submit.prevent="search" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <!-- Status Filter -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
              <select v-model="filterForm.status" class="w-full rounded-md border-gray-300">
                <option value="">All Status</option>
                <option value="pending">Pending</option>
                <option value="confirmed">Confirmed</option>
                <option value="cancelled">Cancelled</option>
                <option value="completed">Completed</option>
              </select>
            </div>

            <!-- Exhibition Filter -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Exhibition</label>
              <select v-model="filterForm.exhibition_id" class="w-full rounded-md border-gray-300">
                <option value="">All Exhibitions</option>
                <option v-for="exhibition in userExhibitions" :key="exhibition.id" :value="exhibition.id">
                  {{ exhibition.title }}
                </option>
              </select>
            </div>

            <!-- Date Range -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">From Date</label>
              <input
                v-model="filterForm.from_date"
                type="date"
                class="w-full rounded-md border-gray-300"
              />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">To Date</label>
              <input
                v-model="filterForm.to_date"
                type="date"
                class="w-full rounded-md border-gray-300"
              />
            </div>

            <!-- Sort Options -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Sort By</label>
              <select v-model="filterForm.sort" class="w-full rounded-md border-gray-300">
                <option value="created_at">Booking Date</option>
                <option value="total_amount">Amount</option>
                <option value="status">Status</option>
              </select>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Order</label>
              <select v-model="filterForm.order" class="w-full rounded-md border-gray-300">
                <option value="desc">Newest First</option>
                <option value="asc">Oldest First</option>
              </select>
            </div>

            <!-- Action Buttons -->
            <div class="flex items-end">
              <button
                type="submit"
                class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
              >
                Apply Filters
              </button>
            </div>

            <div class="flex items-end">
              <button
                type="button"
                @click="clearFilters"
                class="w-full bg-gray-200 text-gray-800 py-2 px-4 rounded-md hover:bg-gray-300 transition-colors"
              >
                Clear Filters
              </button>
            </div>
          </form>
        </div>

        <!-- Results Summary -->
        <div class="bg-white rounded-lg shadow-md p-4 mb-6">
          <div class="text-sm text-gray-600">
            Showing {{ reservations.from || 0 }} to {{ reservations.to || 0 }} of {{ reservations.total || 0 }} reservations
          </div>
        </div>

        <!-- Reservations List -->
        <div v-if="reservations.data && reservations.data.length > 0" class="space-y-6">
          <div
            v-for="reservation in reservations.data"
            :key="reservation.id"
            class="bg-white rounded-lg shadow-md overflow-hidden"
          >
            <div class="p-6">
              <!-- Reservation Header -->
              <div class="flex justify-between items-start mb-4">
                <div>
                  <h3 class="text-lg font-semibold text-gray-900">
                    Booking #{{ reservation.id }}
                  </h3>
                  <p class="text-sm text-gray-600">{{ reservation.exhibition.title }}</p>
                  <p class="text-xs text-gray-500">
                    Booked on {{ formatDate(reservation.created_at) }}
                  </p>
                </div>
                <div class="text-right">
                  <div class="text-lg font-bold text-gray-900">د.ك {{ formatKWD(reservation.total_amount) }}</div>
                  <span
                    :class="getStatusClass(reservation.status)"
                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                  >
                    {{ getStatusText(reservation.status) }}
                  </span>
                </div>
              </div>

              <!-- Booth Details -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-4">
                <div>
                  <h4 class="font-medium text-gray-900 mb-2">Booth Information</h4>
                  <div class="space-y-1 text-sm text-gray-600">
                    <div>Booth #{{ reservation.booth.booth_number }}</div>
                    <div>Size: {{ reservation.booth.size }} sqm</div>
                    <div>Type: {{ reservation.booth.type || 'Standard' }}</div>
                    <div>Location: {{ reservation.booth.location || 'Main Hall' }}</div>
                  </div>
                </div>

                <div>
                  <h4 class="font-medium text-gray-900 mb-2">Exhibition Details</h4>
                  <div class="space-y-1 text-sm text-gray-600">
                    <div>{{ reservation.exhibition.venue_name }}</div>
                    <div>{{ reservation.exhibition.city }}, {{ reservation.exhibition.country }}</div>
                    <div>{{ formatDateRange(reservation.exhibition.start_date, reservation.exhibition.end_date) }}</div>
                    <div>Category: {{ reservation.exhibition.category?.name || 'General' }}</div>
                  </div>
                </div>
              </div>

              <!-- Payment Information -->
              <div v-if="reservation.payments && reservation.payments.length > 0" class="mb-4">
                <h4 class="font-medium text-gray-900 mb-2">Payment History</h4>
                <div class="space-y-2">
                  <div
                    v-for="payment in reservation.payments"
                    :key="payment.id"
                    class="flex justify-between items-center text-sm bg-gray-50 p-3 rounded"
                  >
                    <div>
                      <span class="font-medium">{{ payment.payment_method || 'Card' }}</span>
                      <span class="text-gray-500 ml-2">{{ formatDate(payment.created_at) }}</span>
                    </div>
                    <div class="flex items-center space-x-2">
                      <span class="font-medium">د.ك {{ formatKWD(payment.amount) }}</span>
                      <span
                        :class="payment.status === 'completed' ? 'text-green-600' : 'text-yellow-600'"
                        class="text-xs font-medium"
                      >
                        {{ payment.status }}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Action Buttons -->
              <div class="flex space-x-3">
                <Link
                  :href="`/exhibitions/${reservation.exhibition.slug}/booths/${reservation.booth.id}`"
                  class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors text-sm"
                >
                  View Booth
                </Link>

                <Link
                  :href="`/exhibitions/${reservation.exhibition.slug}`"
                  class="bg-gray-200 text-gray-800 px-4 py-2 rounded-md hover:bg-gray-300 transition-colors text-sm"
                >
                  View Exhibition
                </Link>

                <button
                  v-if="reservation.status === 'pending'"
                  @click="cancelReservation(reservation.id)"
                  class="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 transition-colors text-sm"
                >
                  Cancel Booking
                </button>

                <button
                  v-if="reservation.status === 'confirmed'"
                  @click="downloadInvoice(reservation.id)"
                  class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors text-sm"
                >
                  Download Invoice
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- No Results -->
        <div v-else class="text-center py-12">
          <div class="text-gray-400 text-6xl mb-4">📋</div>
          <h3 class="text-xl font-semibold text-gray-900 mb-2">No reservations found</h3>
          <p class="text-gray-600 mb-4">You haven't made any booth reservations yet</p>
          <Link
            href="/dashboard/booth-search"
            class="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 transition-colors"
          >
            Search Available Booths
          </Link>
        </div>

        <!-- Pagination -->
        <div v-if="reservations.links && reservations.links.length > 3" class="flex justify-center mt-8">
          <nav class="flex space-x-2">
            <Link
              v-for="link in reservations.links"
              :key="link.label"
              :href="link.url"
              :class="[
                'px-3 py-2 rounded-md text-sm font-medium',
                link.active
                  ? 'bg-blue-600 text-white'
                  : link.url
                    ? 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300'
                    : 'bg-gray-100 text-gray-400 cursor-not-allowed'
              ]"
            >
              {{ link.label }}
            </Link>
          </nav>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { Head, Link, router } from '@inertiajs/vue3'
import { reactive } from 'vue'

const props = defineProps({
  reservations: Object,
  userExhibitions: Array,
  summaryStats: Object,
  filters: Object,
  locale: {
    type: String,
    default: 'ar'
  }
})

// Translation helper
const t = (key, fallback) => {
  const translations = {
    // Navigation
    'dashboard': 'لوحة التحكم',
    'search_booths': 'البحث عن الأجنحة',
    'logout': 'تسجيل الخروج',

    // Page titles
    'my_reservations': 'حجوزاتي',
    'manage_reservations_subtitle': 'إدارة حجوزات الأجنحة وتتبع حالتها',

    // Summary stats
    'total_reservations': 'إجمالي الحجوزات',
    'confirmed': 'مؤكدة',
    'pending': 'قيد الانتظار',
    'cancelled': 'ملغية',
    'total_spent': 'إجمالي المبلغ المنفق',

    // Filters
    'filter_reservations': 'تصفية الحجوزات',
    'status': 'الحالة',
    'all_status': 'جميع الحالات',
    'exhibition': 'المعرض',
    'all_exhibitions': 'جميع المعارض',
    'from_date': 'من تاريخ',
    'to_date': 'إلى تاريخ',
    'sort_by': 'ترتيب حسب',
    'booking_date': 'تاريخ الحجز',
    'amount': 'المبلغ',
    'order': 'الترتيب',
    'newest_first': 'الأحدث أولاً',
    'oldest_first': 'الأقدم أولاً',
    'apply_filters': 'تطبيق المرشحات',
    'clear_filters': 'مسح المرشحات',

    // Results
    'showing': 'عرض',
    'to': 'إلى',
    'of': 'من',
    'reservations': 'حجز',

    // Reservation details
    'booking': 'الحجز',
    'booked_on': 'تم الحجز في',
    'booth_information': 'معلومات الجناح',
    'booth': 'الجناح',
    'size': 'المساحة',
    'type': 'النوع',
    'location': 'الموقع',
    'exhibition_details': 'تفاصيل المعرض',
    'category': 'الفئة',
    'payment_history': 'تاريخ الدفع',
    'completed': 'مكتمل',

    // Actions
    'view_booth': 'عرض الجناح',
    'view_exhibition': 'عرض المعرض',
    'cancel_booking': 'إلغاء الحجز',
    'download_invoice': 'تحميل الفاتورة',

    // Status
    'confirmed_status': '✅ مؤكد',
    'pending_status': '⏳ قيد الانتظار',
    'cancelled_status': '❌ ملغي',
    'completed_status': '🎉 مكتمل',

    // No results
    'no_reservations_found': 'لم يتم العثور على حجوزات',
    'no_reservations_yet': 'لم تقم بأي حجوزات للأجنحة بعد',
    'search_available_booths': 'البحث عن الأجنحة المتاحة',

    // Confirmation
    'cancel_confirmation': 'هل أنت متأكد من إلغاء هذا الحجز؟',
    'reservation_cancelled': 'تم إلغاء الحجز بنجاح',
    'cancel_error': 'خطأ في إلغاء الحجز: '
  };

  return props.locale === 'ar' ? (translations[key] || fallback) : fallback;
};

const isRTL = props.locale === 'ar';

// Filter form
const filterForm = reactive({
  status: props.filters.status || '',
  exhibition_id: props.filters.exhibition_id || '',
  from_date: props.filters.from_date || '',
  to_date: props.filters.to_date || '',
  sort: props.filters.sort || 'created_at',
  order: props.filters.order || 'desc'
})

// Methods
const search = () => {
  router.get('/dashboard/reservations', filterForm, {
    preserveState: true,
    preserveScroll: true
  })
}

const clearFilters = () => {
  Object.keys(filterForm).forEach(key => {
    if (key === 'sort') {
      filterForm[key] = 'created_at'
    } else if (key === 'order') {
      filterForm[key] = 'desc'
    } else {
      filterForm[key] = ''
    }
  })
  search()
}

const cancelReservation = (reservationId) => {
  if (confirm('Are you sure you want to cancel this reservation?')) {
    router.post(`/dashboard/reservations/${reservationId}/cancel`, {}, {
      onSuccess: () => {
        alert('Reservation cancelled successfully')
      },
      onError: (errors) => {
        alert('Error cancelling reservation: ' + Object.values(errors).join(', '))
      }
    })
  }
}

const downloadInvoice = (reservationId) => {
  window.open(`/dashboard/reservations/${reservationId}/invoice`, '_blank')
}

const formatKWD = (amount) => {
  return parseFloat(amount).toFixed(3)
}

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

const formatDateRange = (startDate, endDate) => {
  const start = formatDate(startDate)
  const end = formatDate(endDate)
  return start === end ? start : `${start} - ${end}`
}

const getStatusClass = (status) => {
  const classes = {
    'pending': 'bg-yellow-100 text-yellow-800',
    'confirmed': 'bg-green-100 text-green-800',
    'cancelled': 'bg-red-100 text-red-800',
    'completed': 'bg-blue-100 text-blue-800'
  }
  return classes[status] || 'bg-gray-100 text-gray-800'
}

const getStatusText = (status) => {
  const texts = {
    'pending': '⏳ Pending',
    'confirmed': '✅ Confirmed',
    'cancelled': '❌ Cancelled',
    'completed': '🎉 Completed'
  }
  return texts[status] || status
}
</script>
